import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MapPin, ArrowLeft } from "lucide-react";

export default function LocationNotFound() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md mx-auto text-center px-4">
        <div className="mb-8">
          <MapPin className="w-16 h-16 text-via-primary mx-auto mb-4" />
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Location Not Found
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            The location you&apos;re looking for doesn&apos;t exist. Please check the URL or browse our available locations.
          </p>
        </div>

        <div className="space-y-4">
          <Link href="/locations">
            <Button className="w-full bg-via-primary hover:bg-via-primary-dark text-white">
              <ArrowLeft className="w-4 h-4 mr-2" />
              View All Locations
            </Button>
          </Link>
          
          <Link href="/">
            <Button variant="outline" className="w-full border-via-primary text-via-primary hover:bg-via-primary hover:text-white">
              Back to Home
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
