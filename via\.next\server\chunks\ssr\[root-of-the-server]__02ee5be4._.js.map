{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/blog/page.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/Desktop/via/app/blog/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/app/blog/page.tsx <module evaluation>\",\n    \"default\",\n);\nexport const metadata = registerClientReference(\n    function() { throw new Error(\"Attempted to call metadata() from the server but metadata is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/app/blog/page.tsx <module evaluation>\",\n    \"metadata\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;uCACe,IAAA,yZAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAiS,GAC9T,+DACA;AAEG,MAAM,WAAW,IAAA,yZAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/blog/page.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/Desktop/via/app/blog/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/app/blog/page.tsx\",\n    \"default\",\n);\nexport const metadata = registerClientReference(\n    function() { throw new Error(\"Attempted to call metadata() from the server but metadata is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/app/blog/page.tsx\",\n    \"metadata\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;AACvE;;uCACe,IAAA,yZAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA6Q,GAC1S,2CACA;AAEG,MAAM,WAAW,IAAA,yZAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,2CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}