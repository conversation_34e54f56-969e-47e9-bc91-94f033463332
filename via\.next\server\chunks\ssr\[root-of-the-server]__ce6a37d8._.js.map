{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about/about-hero.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AboutHero = registerClientReference(\n    function() { throw new Error(\"Attempted to call AboutHero() from the server but AboutHero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/about/about-hero.tsx <module evaluation>\",\n    \"AboutHero\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,yZAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about/about-hero.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AboutHero = registerClientReference(\n    function() { throw new Error(\"Attempted to call AboutHero() from the server but AboutHero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/about/about-hero.tsx\",\n    \"AboutHero\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,yZAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about/company-story.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const CompanyStory = registerClientReference(\n    function() { throw new Error(\"Attempted to call CompanyStory() from the server but CompanyStory is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/about/company-story.tsx <module evaluation>\",\n    \"CompanyStory\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,eAAe,IAAA,yZAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,gFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about/company-story.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const CompanyStory = registerClientReference(\n    function() { throw new Error(\"Attempted to call CompanyStory() from the server but CompanyStory is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/about/company-story.tsx\",\n    \"CompanyStory\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,eAAe,IAAA,yZAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,4DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about/mission-vision.tsx"], "sourcesContent": ["import Image from \"next/image\";\n\nexport function MissionVision() {\n  return (\n    <section \n      className=\"py-16 text-white relative\"\n      style={{\n        background: \"#2d4f85 url('https://viatosuccess.com/wp-content/uploads/2021/01/about_us-section_2-bg_img.png')\",\n        backgroundPosition: \"center\",\n        backgroundRepeat: \"no-repeat\",\n        backgroundSize: \"cover\"\n      }}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            Our Mission and Vision\n          </h2>\n          <p className=\"text-xl text-blue-100 max-w-4xl mx-auto\">\n            VIA Executive Suites exists to deliver the office solutions businesses need to grow themselves and the economy of the RGV.\n          </p>\n        </div>\n\n        {/* Mission and Vision Cards */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Mission Card */}\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 hover:bg-white/20 transition-all duration-300\">\n            <div className=\"mb-6\">\n              <Image\n                src=\"https://viatosuccess.com/wp-content/uploads/2021/01/about_us-section_2-img_1.jpg\"\n                alt=\"Mission - Professional office environment\"\n                width={400}\n                height={250}\n                className=\"w-full h-48 object-cover rounded-lg\"\n              />\n            </div>\n            \n            <h3 className=\"text-2xl font-bold mb-4 text-center\">Mission</h3>\n            \n            <p className=\"text-blue-100 leading-relaxed text-center\">\n              Our mission is to enhance the enterprise, identity, and integrity of our clients in order to further our region&apos;s economy. We do so by providing office space and amenities that reduce overhead costs for small businesses and startups.\n            </p>\n          </div>\n\n          {/* Vision Card */}\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 hover:bg-white/20 transition-all duration-300\">\n            <div className=\"mb-6\">\n              <Image\n                src=\"https://viatosuccess.com/wp-content/uploads/2021/01/about_us-section_2-img_2.jpg\"\n                alt=\"Vision - Modern workspace solutions\"\n                width={400}\n                height={250}\n                className=\"w-full h-48 object-cover rounded-lg\"\n              />\n            </div>\n            \n            <h3 className=\"text-2xl font-bold mb-4 text-center\">Vision</h3>\n            \n            <p className=\"text-blue-100 leading-relaxed text-center\">\n              Our vision is to provide a thriving network of executive office suites that deliver state-of-the-art office spaces and amenities that make work easier, more affordable, and more achievable for businesses of all sizes.\n            </p>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEO,SAAS;IACd,qBACE,+XAAC;QACC,WAAU;QACV,OAAO;YACL,YAAY;YACZ,oBAAoB;YACpB,kBAAkB;YAClB,gBAAgB;QAClB;kBAEA,cAAA,+XAAC;YAAI,WAAU;;8BAEb,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,+XAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,+XAAC;oBAAI,WAAU;;sCAEb,+XAAC;4BAAI,WAAU;;8CACb,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC,yRAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAId,+XAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,+XAAC;oCAAE,WAAU;8CAA4C;;;;;;;;;;;;sCAM3D,+XAAC;4BAAI,WAAU;;8CACb,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC,yRAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAId,+XAAC;oCAAG,WAAU;8CAAsC;;;;;;8CAEpD,+XAAC;oCAAE,WAAU;8CAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrE", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about/all-inclusive-services.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport { Check } from \"lucide-react\";\n\nconst includedServices = [\n  \"Office space\",\n  \"Shared common areas (e.g. Break rooms, conference rooms)\",\n  \"Front desk services\",\n  \"Utilities\",\n  \"Security cameras\",\n  \"Parking lot\",\n  \"Cleaning\",\n  \"Starbucks coffee\",\n  \"And much more\"\n];\n\nexport function AllInclusiveServices() {\n  return (\n    <section className=\"py-16 bg-white border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\">\n          {/* Content */}\n          <div>\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n              All-Inclusive Executive Suites\n            </h2>\n            \n            <div className=\"prose prose-lg text-gray-600 mb-8\">\n              <p className=\"leading-relaxed mb-6\">\n                VIA Executive Suites enhances the affordability and accessibility of professional workspaces with all-inclusive solutions. For one price, you can enjoy beautiful, modern office spaces, along with an array of amenities and value-added features that create a business office where your company can grow.\n              </p>\n              \n              <p className=\"font-semibold text-gray-900 mb-4\">\n                Our all-inclusive prices include the following:\n              </p>\n            </div>\n\n            {/* Services List */}\n            <div className=\"space-y-3 mb-8\">\n              {includedServices.map((service, index) => (\n                <div key={index} className=\"flex items-start\">\n                  <div className=\"flex-shrink-0 mr-3 mt-1\">\n                    <div className=\"w-5 h-5 bg-primary rounded-full flex items-center justify-center\">\n                      <Check className=\"w-3 h-3 text-white\" />\n                    </div>\n                  </div>\n                  <span className=\"text-gray-700\">{service}</span>\n                </div>\n              ))}\n            </div>\n\n            {/* Additional Services */}\n            <div className=\"bg-gray-50 rounded-lg p-6\">\n              <h3 className=\"font-semibold text-gray-900 mb-3\">\n                Additional Services Available:\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                For businesses in need of additional services, we also provide mediation rooms, private offices, and flexible furniture rentals for additional fees.\n              </p>\n              <p className=\"text-gray-700 font-medium\">\n                Enjoy access to everything you need for an affordable price when you choose to rent office space at any of our 4 business centers throughout the RGV.\n              </p>\n            </div>\n          </div>\n          \n          {/* Image */}\n          <div className=\"relative\">\n            <div className=\"aspect-w-4 aspect-h-5 rounded-lg overflow-hidden shadow-xl\">\n              <Image\n                src=\"https://viatosuccess.com/wp-content/uploads/2021/01/about_us-section_3-img_1.jpg\"\n                alt=\"All-inclusive executive office suite\"\n                width={500}\n                height={600}\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n            \n            {/* Decorative elements */}\n            <div className=\"absolute -top-4 -left-4 w-24 h-24 bg-primary/10 rounded-full -z-10\"></div>\n            <div className=\"absolute -bottom-4 -right-4 w-32 h-32 bg-blue-100 rounded-full -z-10\"></div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEA,MAAM,mBAAmB;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS;IACd,qBACE,+XAAC;QAAQ,WAAU;kBACjB,cAAA,+XAAC;YAAI,WAAU;sBACb,cAAA,+XAAC;gBAAI,WAAU;;kCAEb,+XAAC;;0CACC,+XAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAIlE,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;wCAAE,WAAU;kDAAuB;;;;;;kDAIpC,+XAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAMlD,+XAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,+XAAC;wCAAgB,WAAU;;0DACzB,+XAAC;gDAAI,WAAU;0DACb,cAAA,+XAAC;oDAAI,WAAU;8DACb,cAAA,+XAAC,mTAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAGrB,+XAAC;gDAAK,WAAU;0DAAiB;;;;;;;uCANzB;;;;;;;;;;0CAYd,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;wCAAG,WAAU;kDAAmC;;;;;;kDAGjD,+XAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,+XAAC;wCAAE,WAAU;kDAA4B;;;;;;;;;;;;;;;;;;kCAO7C,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC,yRAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAKd,+XAAC;gCAAI,WAAU;;;;;;0CACf,+XAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about/about-testimonials.tsx"], "sourcesContent": ["import { Star, Quote } from \"lucide-react\";\n\nconst testimonials = [\n  {\n    quote: \"That moments when you realize everything will be just fine in our new location! I received a call from <PERSON><PERSON><PERSON>, the manager at ADBC, to tell me new customers are looking for us already in the new location!! He forwarded the customer to us! Thank you <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> for all your good customer service. Looking forward for great business for all.\",\n    author: \"Vital Camera Mary\",\n    company: \"Vital Camera\",\n    rating: 5\n  },\n  {\n    quote: \"<PERSON><PERSON><PERSON> and all the staff at ADBC have been extremely helpful since I started Pronto Credit in McAllen on 2011.\",\n    author: \"<PERSON>\",\n    company: \"Pronto Credit\",\n    rating: 5\n  }\n];\n\nexport function AboutTestimonials() {\n  return (\n    <section className=\"py-16 bg-gray-50 border-t border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Testimonials\n          </h2>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {testimonials.map((testimonial, index) => (\n            <div \n              key={index}\n              className=\"bg-white rounded-lg shadow-md p-8 hover:shadow-lg transition-all duration-300\"\n            >\n              {/* Quote Icon */}\n              <div className=\"flex justify-center mb-6\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-full\">\n                  <Quote className=\"w-6 h-6 text-primary\" />\n                </div>\n              </div>\n\n              {/* Star Rating */}\n              <div className=\"flex justify-center mb-4\">\n                {[...Array(testimonial.rating)].map((_, starIndex) => (\n                  <Star \n                    key={starIndex} \n                    className=\"w-5 h-5 text-yellow-400 fill-current\" \n                  />\n                ))}\n              </div>\n\n              {/* Quote Text */}\n              <blockquote className=\"text-center mb-6\">\n                <p className=\"text-gray-700 leading-relaxed italic\">\n                  &ldquo;{testimonial.quote}&rdquo;\n                </p>\n              </blockquote>\n\n              {/* Attribution */}\n              <div className=\"text-center\">\n                <div className=\"font-semibold text-gray-900 text-lg\">\n                  {testimonial.author}\n                </div>\n                <div className=\"text-primary text-sm\">\n                  {testimonial.company}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;;;AAEA,MAAM,eAAe;IACnB;QACE,OAAO;QACP,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,OAAO;QACP,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;CACD;AAEM,SAAS;IACd,qBACE,+XAAC;QAAQ,WAAU;kBACjB,cAAA,+XAAC;YAAI,WAAU;;8BACb,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC;wBAAG,WAAU;kCAAoD;;;;;;;;;;;8BAKpE,+XAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,+XAAC;4BAEC,WAAU;;8CAGV,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC;wCAAI,WAAU;kDACb,cAAA,+XAAC,mTAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAKrB,+XAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM,YAAY,MAAM;qCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,0BACtC,+XAAC,gTAAI;4CAEH,WAAU;2CADL;;;;;;;;;;8CAOX,+XAAC;oCAAW,WAAU;8CACpB,cAAA,+XAAC;wCAAE,WAAU;;4CAAuC;4CAC1C,YAAY,KAAK;4CAAC;;;;;;;;;;;;8CAK9B,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;sDACZ,YAAY,MAAM;;;;;;sDAErB,+XAAC;4CAAI,WAAU;sDACZ,YAAY,OAAO;;;;;;;;;;;;;2BAjCnB;;;;;;;;;;;;;;;;;;;;;AA0CnB", "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about/starbucks-feature.tsx"], "sourcesContent": ["\n\nexport function StarbucksFeature() {\n  return (\n    <section className=\"py-16 bg-white\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <div className=\"flex items-center justify-center mb-6\">\n            <div className=\"inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full\">\n              {/* Starbucks Logo Placeholder - In production, you'd use the actual Starbucks logo */}\n              <div className=\"w-12 h-12 bg-green-600 rounded-full flex items-center justify-center\">\n                <svg className=\"w-8 h-8 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                </svg>\n              </div>\n            </div>\n          </div>\n          \n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Complimentary Starbucks Coffee\n          </h2>\n          \n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Enjoy complimentary Starbucks coffee at any of our four locations.\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAEO,SAAS;IACd,qBACE,+XAAC;QAAQ,WAAU;kBACjB,cAAA,+XAAC;YAAI,WAAU;sBACb,cAAA,+XAAC;gBAAI,WAAU;;kCACb,+XAAC;wBAAI,WAAU;kCACb,cAAA,+XAAC;4BAAI,WAAU;sCAEb,cAAA,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAI,WAAU;oCAAqB,MAAK;oCAAe,SAAQ;8CAC9D,cAAA,+XAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMhB,+XAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAIlE,+XAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;;;;;;;;;;;AAOjE", "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about/about-contact-form.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AboutContactForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call AboutContactForm() from the server but AboutContactForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/about/about-contact-form.tsx <module evaluation>\",\n    \"AboutContactForm\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,mBAAmB,IAAA,yZAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about/about-contact-form.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AboutContactForm = registerClientReference(\n    function() { throw new Error(\"Attempted to call AboutContactForm() from the server but AboutContactForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/about/about-contact-form.tsx\",\n    \"AboutContactForm\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,mBAAmB,IAAA,yZAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 734, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 742, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/about/page.tsx"], "sourcesContent": ["import { Metada<PERSON> } from \"next\";\nimport { AboutHero } from \"@/components/about/about-hero\";\nimport { CompanyStory } from \"@/components/about/company-story\";\nimport { MissionVision } from \"@/components/about/mission-vision\";\nimport { AllInclusiveServices } from \"@/components/about/all-inclusive-services\";\nimport { AboutTestimonials } from \"@/components/about/about-testimonials\";\nimport { StarbucksFeature } from \"@/components/about/starbucks-feature\";\nimport { AboutContactForm } from \"@/components/about/about-contact-form\";\n\nexport const metadata: Metadata = {\n  title: \"About Us - Via Executive Suites | Professional Workspace Solutions\",\n  description: \"Learn about Via Executive Suites' mission to provide affordable and professional workspace solutions for startups, small businesses, and freelancers in the Rio Grande Valley.\",\n  keywords: \"about via executive suites, mission, vision, professional workspace, office space rental, rio grande valley, mcallen, edinburg\",\n  openGraph: {\n    title: \"About Us - Via Executive Suites\",\n    description: \"Discover how Via Executive Suites delivers affordable and professional workspace solutions through four all-inclusive business centers in the RGV.\",\n    type: \"website\",\n    locale: \"en_US\",\n  },\n};\n\nexport default function About() {\n  return (\n    <main className=\"min-h-screen\">\n      <AboutHero />\n      <CompanyStory />\n      <MissionVision />\n      <AllInclusiveServices />\n      <AboutTestimonials />\n      <StarbucksFeature />\n      <AboutContactForm />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;AACF;AAEe,SAAS;IACtB,qBACE,+XAAC;QAAK,WAAU;;0BACd,+XAAC,oKAAS;;;;;0BACV,+XAAC,0KAAY;;;;;0BACb,+XAAC,4KAAa;;;;;0BACd,+XAAC,8LAAoB;;;;;0BACrB,+XAAC,oLAAiB;;;;;0BAClB,+XAAC,kLAAgB;;;;;0BACjB,+XAAC,sLAAgB;;;;;;;;;;;AAGvB", "debugId": null}}]}