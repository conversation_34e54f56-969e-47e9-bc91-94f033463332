"use client";

import Image from "next/image";
import { motion } from "framer-motion";
import { fadeInLeft, fadeInRight, animationConfig } from "@/lib/animations";

export function CompanyStory() {
  return (
    <section className="py-16 bg-white border-b border-gray-200 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={animationConfig.viewport}
            variants={fadeInLeft}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Via Executive Suites
            </h2>

            <div className="prose prose-lg text-gray-600">
              <p className="leading-relaxed">
                VIA Executive Suites delivers affordable and professional workspace solutions for startups, small businesses, satellite offices, and freelancers through four all-inclusive business centers and an array of shared amenities. At VIA Executive Suites, businesses find everything they need to succeed, including networking opportunities, dedicated personnel, state-of-the-art office space, front desk services, break rooms, mediation rooms, flexible leases, and office furniture rental. With every convenience available in each of our 4 business centers, we make better work achievable throughout the Valley.
              </p>
            </div>
          </motion.div>
          
          {/* Image */}
          <motion.div
            className="relative"
            initial="hidden"
            whileInView="visible"
            viewport={animationConfig.viewport}
            variants={fadeInRight}
          >
            <motion.div
              className="aspect-w-4 aspect-h-3 rounded-lg overflow-hidden shadow-xl"
              whileHover={{
                scale: 1.02,
                transition: { duration: 0.3 }
              }}
            >
              <Image
                src="https://viatosuccess.com/wp-content/uploads/2021/01/about_us-section_1-img_1.jpg"
                alt="Modern office space at Via Executive Suites"
                width={600}
                height={400}
                className="w-full h-full object-cover"
              />
            </motion.div>

            {/* Decorative elements */}
            <motion.div
              className="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full -z-10"
              animate={{
                y: [0, -10, 0],
                transition: {
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }
              }}
            />
            <motion.div
              className="absolute -bottom-4 -left-4 w-32 h-32 bg-blue-100 rounded-full -z-10"
              animate={{
                y: [0, 10, 0],
                transition: {
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }
              }}
            />
          </motion.div>
        </div>
      </div>
    </section>
  );
}
