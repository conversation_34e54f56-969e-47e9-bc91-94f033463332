{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// This check can be removed, it is just for tutorial purposes\nexport const hasEnvVars =\n  process.env.NEXT_PUBLIC_SUPABASE_URL &&\n  process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY;\n"], "names": [], "mappings": ";;;;;;AASE;AATF;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yPAAO,EAAC,IAAA,mNAAI,EAAC;AACtB;AAGO,MAAM,aACX", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,IAAA,uQAAG,EACxB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,4TAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,kUAAI,GAAG;IAC9B,qBACE,8UAAC;QACC,WAAW,IAAA,uIAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { Menu, X, ChevronDown } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { cn } from \"@/lib/utils\";\n\nconst navigation = [\n  { name: \"Home\", href: \"/\" },\n  { name: \"About\", href: \"/about\" },\n  {\n    name: \"Locations\",\n    href: \"/locations\",\n    dropdown: [\n      { name: \"ADBC\", href: \"/locations/adbc\" },\n      { name: \"La Costa\", href: \"/locations/la-costa\" },\n      { name: \"23rd\", href: \"/locations/23rd\" },\n      { name: \"Edinburg\", href: \"/locations/edinburg\" },\n      { name: \"Lindberg\", href: \"/locations/lindberg\" },\n    ]\n  },\n  {\n    name: \"Services\",\n    href: \"/services\",\n    dropdown: [\n      { name: \"Executive Suites\", href: \"/services/executive-suites\" },\n      { name: \"Virtual Offices\", href: \"/services/virtual-offices\" },\n      { name: \"Beauty Suites\", href: \"/services/beauty-suites\" },\n    ]\n  },\n  { name: \"Blog\", href: \"/blog\" },\n  { name: \"Connect\", href: \"/connect\" },\n];\n\nexport function Navbar() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\" aria-label=\"Top\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-3\">\n              <div className=\"relative h-10 w-auto\">\n                <Image\n                  src=\"https://via2success.com/wp-content/themes/viatosuccess_v1/assets/images/logo.svg\"\n                  alt=\"Via Executive Suites\"\n                  width={120}\n                  height={40}\n                  className=\"h-10 w-auto\"\n                  priority\n                />\n              </div>\n              <div className=\"flex flex-col\">\n                <span className=\"text-xl font-bold text-via-primary leading-none\">\n                  via\n                </span>\n                <span className=\"text-xs font-medium text-gray-600 leading-none\">\n                  executive suites\n                </span>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex md:items-center md:space-x-8\">\n            {navigation.map((item) => (\n              <div key={item.name} className=\"relative\">\n                {item.dropdown ? (\n                  <div\n                    className=\"relative\"\n                    onMouseEnter={() => setActiveDropdown(item.name)}\n                    onMouseLeave={() => setActiveDropdown(null)}\n                  >\n                    <button className=\"flex items-center text-gray-700 hover:text-via-primary font-medium transition-colors duration-200\">\n                      {item.name}\n                      <ChevronDown className=\"ml-1 h-4 w-4\" />\n                    </button>\n                    {activeDropdown === item.name && (\n                      <div className=\"absolute top-full left-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50\">\n                        {item.dropdown.map((subItem) => (\n                          <Link\n                            key={subItem.name}\n                            href={subItem.href}\n                            className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-via-primary transition-colors duration-200\"\n                          >\n                            {subItem.name}\n                          </Link>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  <Link\n                    href={item.href}\n                    className=\"text-gray-700 hover:text-via-primary font-medium transition-colors duration-200\"\n                  >\n                    {item.name}\n                  </Link>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* Desktop Auth Buttons */}\n          <div className=\"hidden md:flex md:items-center md:space-x-4\">\n            <Link href=\"/connect\">\n              <Button variant=\"outline\" className=\"text-via-primary border-via-primary hover:bg-via-primary hover:text-white\">\n                Schedule a Tour\n              </Button>\n            </Link>\n            <Link href=\"/auth/login\">\n              <Button variant=\"ghost\" className=\"text-gray-700 hover:text-via-primary\">\n                Sign In\n              </Button>\n            </Link>\n            <Link href=\"/auth/sign-up\">\n              <Button className=\"bg-via-primary hover:bg-via-primary-dark text-white\">\n                Get Started\n              </Button>\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n              className=\"text-gray-700\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {mobileMenuOpen ? (\n                <X className=\"h-6 w-6\" aria-hidden=\"true\" />\n              ) : (\n                <Menu className=\"h-6 w-6\" aria-hidden=\"true\" />\n              )}\n            </Button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation Menu */}\n        <div\n          className={cn(\n            \"md:hidden transition-all duration-300 ease-in-out\",\n            mobileMenuOpen\n              ? \"max-h-[500px] opacity-100 visible\"\n              : \"max-h-0 opacity-0 invisible overflow-hidden\"\n          )}\n        >\n          <div className=\"space-y-1 pb-3 pt-2\">\n            {navigation.map((item) => (\n              <div key={item.name}>\n                {item.dropdown ? (\n                  <div>\n                    <button\n                      className=\"flex items-center justify-between w-full px-3 py-2 text-base font-medium text-gray-700 hover:text-via-primary hover:bg-gray-50 rounded-md transition-colors duration-200\"\n                      onClick={() => setActiveDropdown(activeDropdown === item.name ? null : item.name)}\n                    >\n                      {item.name}\n                      <ChevronDown className={cn(\"h-4 w-4 transition-transform\", activeDropdown === item.name && \"rotate-180\")} />\n                    </button>\n                    {activeDropdown === item.name && (\n                      <div className=\"pl-4 space-y-1\">\n                        {item.dropdown.map((subItem) => (\n                          <Link\n                            key={subItem.name}\n                            href={subItem.href}\n                            className=\"block px-3 py-2 text-sm text-gray-600 hover:text-via-primary hover:bg-gray-50 rounded-md transition-colors duration-200\"\n                            onClick={() => setMobileMenuOpen(false)}\n                          >\n                            {subItem.name}\n                          </Link>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  <Link\n                    href={item.href}\n                    className=\"block px-3 py-2 text-base font-medium text-gray-700 hover:text-via-primary hover:bg-gray-50 rounded-md transition-colors duration-200\"\n                    onClick={() => setMobileMenuOpen(false)}\n                  >\n                    {item.name}\n                  </Link>\n                )}\n              </div>\n            ))}\n            <div className=\"border-t border-gray-200 pt-4 pb-3\">\n              <div className=\"flex flex-col space-y-3 px-3\">\n                <Link href=\"/connect\" onClick={() => setMobileMenuOpen(false)}>\n                  <Button variant=\"outline\" className=\"w-full text-via-primary border-via-primary hover:bg-via-primary hover:text-white\">\n                    Schedule a Tour\n                  </Button>\n                </Link>\n                <Link href=\"/auth/login\" onClick={() => setMobileMenuOpen(false)}>\n                  <Button variant=\"ghost\" className=\"w-full justify-start text-gray-700 hover:text-via-primary\">\n                    Sign In\n                  </Button>\n                </Link>\n                <Link href=\"/auth/sign-up\" onClick={() => setMobileMenuOpen(false)}>\n                  <Button className=\"w-full bg-via-primary hover:bg-via-primary-dark text-white\">\n                    Get Started\n                  </Button>\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </nav>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAQ,MAAM;YAAkB;YACxC;gBAAE,MAAM;gBAAY,MAAM;YAAsB;YAChD;gBAAE,MAAM;gBAAQ,MAAM;YAAkB;YACxC;gBAAE,MAAM;gBAAY,MAAM;YAAsB;YAChD;gBAAE,MAAM;gBAAY,MAAM;YAAsB;SACjD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,UAAU;YACR;gBAAE,MAAM;gBAAoB,MAAM;YAA6B;YAC/D;gBAAE,MAAM;gBAAmB,MAAM;YAA4B;YAC7D;gBAAE,MAAM;gBAAiB,MAAM;YAA0B;SAC1D;IACH;IACA;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,SAAS;;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,0TAAQ,EAAC;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,0TAAQ,EAAgB;IAEpE,qBACE,8UAAC;QAAO,WAAU;kBAChB,cAAA,8UAAC;YAAI,WAAU;YAAyC,cAAW;;8BACjE,8UAAC;oBAAI,WAAU;;sCAEb,8UAAC;4BAAI,WAAU;sCACb,cAAA,8UAAC,2TAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8UAAC;wCAAI,WAAU;kDACb,cAAA,8UAAC,4RAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,QAAQ;;;;;;;;;;;kDAGZ,8UAAC;wCAAI,WAAU;;0DACb,8UAAC;gDAAK,WAAU;0DAAkD;;;;;;0DAGlE,8UAAC;gDAAK,WAAU;0DAAiD;;;;;;;;;;;;;;;;;;;;;;;sCAQvE,8UAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8UAAC;oCAAoB,WAAU;8CAC5B,KAAK,QAAQ,iBACZ,8UAAC;wCACC,WAAU;wCACV,cAAc,IAAM,kBAAkB,KAAK,IAAI;wCAC/C,cAAc,IAAM,kBAAkB;;0DAEtC,8UAAC;gDAAO,WAAU;;oDACf,KAAK,IAAI;kEACV,8UAAC,4UAAW;wDAAC,WAAU;;;;;;;;;;;;4CAExB,mBAAmB,KAAK,IAAI,kBAC3B,8UAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,8UAAC,2TAAI;wDAEH,MAAM,QAAQ,IAAI;wDAClB,WAAU;kEAET,QAAQ,IAAI;uDAJR,QAAQ,IAAI;;;;;;;;;;;;;;;6DAW3B,8UAAC,2TAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;;;;;;mCA9BN,KAAK,IAAI;;;;;;;;;;sCAsCvB,8UAAC;4BAAI,WAAU;;8CACb,8UAAC,2TAAI;oCAAC,MAAK;8CACT,cAAA,8UAAC,0JAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAA4E;;;;;;;;;;;8CAIlH,8UAAC,2TAAI;oCAAC,MAAK;8CACT,cAAA,8UAAC,0JAAM;wCAAC,SAAQ;wCAAQ,WAAU;kDAAuC;;;;;;;;;;;8CAI3E,8UAAC,2TAAI;oCAAC,MAAK;8CACT,cAAA,8UAAC,0JAAM;wCAAC,WAAU;kDAAsD;;;;;;;;;;;;;;;;;sCAO5E,8UAAC;4BAAI,WAAU;sCACb,cAAA,8UAAC,0JAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,kBAAkB,CAAC;gCAClC,WAAU;;kDAEV,8UAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,+BACC,8UAAC,0SAAC;wCAAC,WAAU;wCAAU,eAAY;;;;;6DAEnC,8UAAC,mTAAI;wCAAC,WAAU;wCAAU,eAAY;;;;;;;;;;;;;;;;;;;;;;;8BAO9C,8UAAC;oBACC,WAAW,IAAA,uIAAE,EACX,qDACA,iBACI,sCACA;8BAGN,cAAA,8UAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8UAAC;8CACE,KAAK,QAAQ,iBACZ,8UAAC;;0DACC,8UAAC;gDACC,WAAU;gDACV,SAAS,IAAM,kBAAkB,mBAAmB,KAAK,IAAI,GAAG,OAAO,KAAK,IAAI;;oDAE/E,KAAK,IAAI;kEACV,8UAAC,4UAAW;wDAAC,WAAW,IAAA,uIAAE,EAAC,gCAAgC,mBAAmB,KAAK,IAAI,IAAI;;;;;;;;;;;;4CAE5F,mBAAmB,KAAK,IAAI,kBAC3B,8UAAC;gDAAI,WAAU;0DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,8UAAC,2TAAI;wDAEH,MAAM,QAAQ,IAAI;wDAClB,WAAU;wDACV,SAAS,IAAM,kBAAkB;kEAEhC,QAAQ,IAAI;uDALR,QAAQ,IAAI;;;;;;;;;;;;;;;6DAY3B,8UAAC,2TAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEhC,KAAK,IAAI;;;;;;mCA/BN,KAAK,IAAI;;;;;0CAoCrB,8UAAC;gCAAI,WAAU;0CACb,cAAA,8UAAC;oCAAI,WAAU;;sDACb,8UAAC,2TAAI;4CAAC,MAAK;4CAAW,SAAS,IAAM,kBAAkB;sDACrD,cAAA,8UAAC,0JAAM;gDAAC,SAAQ;gDAAU,WAAU;0DAAmF;;;;;;;;;;;sDAIzH,8UAAC,2TAAI;4CAAC,MAAK;4CAAc,SAAS,IAAM,kBAAkB;sDACxD,cAAA,8UAAC,0JAAM;gDAAC,SAAQ;gDAAQ,WAAU;0DAA4D;;;;;;;;;;;sDAIhG,8UAAC,2TAAI;4CAAC,MAAK;4CAAgB,SAAS,IAAM,kBAAkB;sDAC1D,cAAA,8UAAC,0JAAM;gDAAC,WAAU;0DAA6D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWjG;GApLgB;KAAA", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/lib/animations.ts"], "sourcesContent": ["import { Variants } from \"framer-motion\";\n\n// Animation variants for consistent animations across the site\nexport const fadeInUp: Variants = {\n  hidden: {\n    opacity: 0,\n    y: 30, // Reduced from 60 to prevent overflow\n  },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6,\n      ease: [0.6, -0.05, 0.01, 0.99],\n    },\n  },\n};\n\nexport const fadeInDown: Variants = {\n  hidden: {\n    opacity: 0,\n    y: -30, // Reduced from -60 to prevent overflow\n  },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6,\n      ease: [0.6, -0.05, 0.01, 0.99],\n    },\n  },\n};\n\nexport const fadeInLeft: Variants = {\n  hidden: {\n    opacity: 0,\n    x: -30, // Reduced from -60 to prevent overflow\n  },\n  visible: {\n    opacity: 1,\n    x: 0,\n    transition: {\n      duration: 0.6,\n      ease: [0.6, -0.05, 0.01, 0.99],\n    },\n  },\n};\n\nexport const fadeInRight: Variants = {\n  hidden: {\n    opacity: 0,\n    x: 30, // Reduced from 60 to prevent overflow\n  },\n  visible: {\n    opacity: 1,\n    x: 0,\n    transition: {\n      duration: 0.6,\n      ease: [0.6, -0.05, 0.01, 0.99],\n    },\n  },\n};\n\nexport const scaleIn: Variants = {\n  hidden: {\n    opacity: 0,\n    scale: 0.9, // Reduced from 0.8 to prevent overflow\n  },\n  visible: {\n    opacity: 1,\n    scale: 1,\n    transition: {\n      duration: 0.5,\n      ease: [0.6, -0.05, 0.01, 0.99],\n    },\n  },\n};\n\nexport const staggerContainer: Variants = {\n  hidden: {},\n  visible: {\n    transition: {\n      staggerChildren: 0.1,\n      delayChildren: 0.1,\n    },\n  },\n};\n\nexport const staggerItem: Variants = {\n  hidden: {\n    opacity: 0,\n    y: 20,\n  },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.5,\n      ease: [0.6, -0.05, 0.01, 0.99],\n    },\n  },\n};\n\n// Page transition variants\nexport const pageTransition: Variants = {\n  hidden: {\n    opacity: 0,\n    y: 20,\n  },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.4,\n      ease: [0.6, -0.05, 0.01, 0.99],\n    },\n  },\n  exit: {\n    opacity: 0,\n    y: -20,\n    transition: {\n      duration: 0.3,\n      ease: [0.6, -0.05, 0.01, 0.99],\n    },\n  },\n};\n\n// Hover effects\nexport const hoverScale = {\n  scale: 1.05,\n  transition: {\n    duration: 0.2,\n    ease: \"easeInOut\",\n  },\n};\n\nexport const hoverLift = {\n  y: -8,\n  transition: {\n    duration: 0.2,\n    ease: \"easeInOut\",\n  },\n};\n\n// Parallax effect\nexport const parallaxVariants: Variants = {\n  hidden: {\n    y: 0,\n  },\n  visible: {\n    y: -25, // Reduced from -50 to prevent overflow\n    transition: {\n      duration: 0.8,\n      ease: \"easeOut\",\n    },\n  },\n};\n\n// Animation configuration\nexport const animationConfig = {\n  // Viewport settings for scroll-triggered animations\n  viewport: {\n    once: true,\n    margin: \"-100px\",\n    amount: 0.3,\n  },\n  \n  // Reduced motion settings\n  reducedMotion: {\n    duration: 0.01,\n    ease: \"linear\" as const,\n  },\n};\n\n// Utility function to check for reduced motion preference\nexport const shouldReduceMotion = () => {\n  if (typeof window !== \"undefined\") {\n    return window.matchMedia(\"(prefers-reduced-motion: reduce)\").matches;\n  }\n  return false;\n};\n\n// Utility function to get animation variants with reduced motion support\nexport const getAnimationVariants = (variants: Variants): Variants => {\n  if (shouldReduceMotion()) {\n    const reducedVariants: Variants = {};\n    Object.keys(variants).forEach((key) => {\n      reducedVariants[key] = {\n        ...variants[key],\n        transition: animationConfig.reducedMotion,\n      };\n    });\n    return reducedVariants;\n  }\n  return variants;\n};\n\n// Custom easing curves\nexport const easings = {\n  easeInOutCubic: [0.4, 0, 0.2, 1],\n  easeOutCubic: [0.33, 1, 0.68, 1],\n  easeInCubic: [0.32, 0, 0.67, 0],\n  easeOutQuart: [0.25, 1, 0.5, 1],\n  easeInOutQuart: [0.76, 0, 0.24, 1],\n  spring: {\n    type: \"spring\",\n    damping: 25,\n    stiffness: 120,\n  },\n};\n\n// Hero section specific animations\nexport const heroAnimations: Variants = {\n  hidden: {},\n  visible: {\n    transition: {\n      staggerChildren: 0.2,\n      delayChildren: 0.1,\n    },\n  },\n};\n\nexport const heroTitle: Variants = {\n  hidden: {\n    opacity: 0,\n    y: 50, // Reduced from 100 to prevent overflow\n  },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.8,\n      ease: [0.6, -0.05, 0.01, 0.99],\n    },\n  },\n};\n\nexport const heroSubtitle: Variants = {\n  hidden: {\n    opacity: 0,\n    y: 25, // Reduced from 50 to prevent overflow\n  },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6,\n      ease: [0.6, -0.05, 0.01, 0.99],\n      delay: 0.2,\n    },\n  },\n};\n\nexport const heroButtons: Variants = {\n  hidden: {\n    opacity: 0,\n    y: 20, // Reduced from 30 to prevent overflow\n  },\n  visible: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.5,\n      ease: [0.6, -0.05, 0.01, 0.99],\n      delay: 0.4,\n    },\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,MAAM,WAAqB;IAChC,QAAQ;QACN,SAAS;QACT,GAAG;IACL;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK,CAAC;gBAAM;gBAAM;aAAK;QAChC;IACF;AACF;AAEO,MAAM,aAAuB;IAClC,QAAQ;QACN,SAAS;QACT,GAAG,CAAC;IACN;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK,CAAC;gBAAM;gBAAM;aAAK;QAChC;IACF;AACF;AAEO,MAAM,aAAuB;IAClC,QAAQ;QACN,SAAS;QACT,GAAG,CAAC;IACN;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK,CAAC;gBAAM;gBAAM;aAAK;QAChC;IACF;AACF;AAEO,MAAM,cAAwB;IACnC,QAAQ;QACN,SAAS;QACT,GAAG;IACL;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK,CAAC;gBAAM;gBAAM;aAAK;QAChC;IACF;AACF;AAEO,MAAM,UAAoB;IAC/B,QAAQ;QACN,SAAS;QACT,OAAO;IACT;IACA,SAAS;QACP,SAAS;QACT,OAAO;QACP,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK,CAAC;gBAAM;gBAAM;aAAK;QAChC;IACF;AACF;AAEO,MAAM,mBAA6B;IACxC,QAAQ,CAAC;IACT,SAAS;QACP,YAAY;YACV,iBAAiB;YACjB,eAAe;QACjB;IACF;AACF;AAEO,MAAM,cAAwB;IACnC,QAAQ;QACN,SAAS;QACT,GAAG;IACL;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK,CAAC;gBAAM;gBAAM;aAAK;QAChC;IACF;AACF;AAGO,MAAM,iBAA2B;IACtC,QAAQ;QACN,SAAS;QACT,GAAG;IACL;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK,CAAC;gBAAM;gBAAM;aAAK;QAChC;IACF;IACA,MAAM;QACJ,SAAS;QACT,GAAG,CAAC;QACJ,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK,CAAC;gBAAM;gBAAM;aAAK;QAChC;IACF;AACF;AAGO,MAAM,aAAa;IACxB,OAAO;IACP,YAAY;QACV,UAAU;QACV,MAAM;IACR;AACF;AAEO,MAAM,YAAY;IACvB,GAAG,CAAC;IACJ,YAAY;QACV,UAAU;QACV,MAAM;IACR;AACF;AAGO,MAAM,mBAA6B;IACxC,QAAQ;QACN,GAAG;IACL;IACA,SAAS;QACP,GAAG,CAAC;QACJ,YAAY;YACV,UAAU;YACV,MAAM;QACR;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,oDAAoD;IACpD,UAAU;QACR,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;IAEA,0BAA0B;IAC1B,eAAe;QACb,UAAU;QACV,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB;IAChC,wCAAmC;QACjC,OAAO,OAAO,UAAU,CAAC,oCAAoC,OAAO;IACtE;;;AAEF;AAGO,MAAM,uBAAuB,CAAC;IACnC,IAAI,sBAAsB;QACxB,MAAM,kBAA4B,CAAC;QACnC,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,CAAC;YAC7B,eAAe,CAAC,IAAI,GAAG;gBACrB,GAAG,QAAQ,CAAC,IAAI;gBAChB,YAAY,gBAAgB,aAAa;YAC3C;QACF;QACA,OAAO;IACT;IACA,OAAO;AACT;AAGO,MAAM,UAAU;IACrB,gBAAgB;QAAC;QAAK;QAAG;QAAK;KAAE;IAChC,cAAc;QAAC;QAAM;QAAG;QAAM;KAAE;IAChC,aAAa;QAAC;QAAM;QAAG;QAAM;KAAE;IAC/B,cAAc;QAAC;QAAM;QAAG;QAAK;KAAE;IAC/B,gBAAgB;QAAC;QAAM;QAAG;QAAM;KAAE;IAClC,QAAQ;QACN,MAAM;QACN,SAAS;QACT,WAAW;IACb;AACF;AAGO,MAAM,iBAA2B;IACtC,QAAQ,CAAC;IACT,SAAS;QACP,YAAY;YACV,iBAAiB;YACjB,eAAe;QACjB;IACF;AACF;AAEO,MAAM,YAAsB;IACjC,QAAQ;QACN,SAAS;QACT,GAAG;IACL;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK,CAAC;gBAAM;gBAAM;aAAK;QAChC;IACF;AACF;AAEO,MAAM,eAAyB;IACpC,QAAQ;QACN,SAAS;QACT,GAAG;IACL;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK,CAAC;gBAAM;gBAAM;aAAK;YAC9B,OAAO;QACT;IACF;AACF;AAEO,MAAM,cAAwB;IACnC,QAAQ;QACN,SAAS;QACT,GAAG;IACL;IACA,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAK,CAAC;gBAAM;gBAAM;aAAK;YAC9B,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/page-transition.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { usePathname } from \"next/navigation\";\nimport { pageTransition } from \"@/lib/animations\";\n\ninterface PageTransitionProps {\n  children: React.ReactNode;\n}\n\nexport function PageTransition({ children }: PageTransitionProps) {\n  const pathname = usePathname();\n\n  return (\n    <AnimatePresence mode=\"wait\" initial={false}>\n      <motion.div\n        key={pathname}\n        initial=\"hidden\"\n        animate=\"visible\"\n        exit=\"exit\"\n        variants={pageTransition}\n        className=\"min-h-screen overflow-hidden\"\n      >\n        {children}\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AACA;;;AAJA;;;;AAUO,SAAS,eAAe,KAAiC;QAAjC,EAAE,QAAQ,EAAuB,GAAjC;;IAC7B,MAAM,WAAW,IAAA,qSAAW;IAE5B,qBACE,8UAAC,uUAAe;QAAC,MAAK;QAAO,SAAS;kBACpC,cAAA,8UAAC,+TAAM,CAAC,GAAG;YAET,SAAQ;YACR,SAAQ;YACR,MAAK;YACL,UAAU,wJAAc;YACxB,WAAU;sBAET;WAPI;;;;;;;;;;AAWb;GAjBgB;;QACG,qSAAW;;;KADd", "debugId": null}}]}