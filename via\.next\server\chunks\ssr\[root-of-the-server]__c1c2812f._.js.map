{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_6ee70f7.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_6ee70f7-module__6GiuUG__className\",\n  \"variable\": \"geist_6ee70f7-module__6GiuUG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_6ee70f7.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22display%22:%22swap%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,+JAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,+JAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,+JAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/navbar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Navbar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/navbar.tsx <module evaluation>\",\n    \"Navbar\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,SAAS,IAAA,yZAAuB,EACzC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,mEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/navbar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Navbar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/navbar.tsx\",\n    \"Navbar\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,SAAS,IAAA,yZAAuB,EACzC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport Image from \"next/image\";\nimport { Facebook, Instagram, Linkedin, Youtube, Twitter } from \"lucide-react\";\n\nconst locations = [\n  { name: \"Via Edinburg\", href: \"/locations/edinburg\" },\n  { name: \"La Costa\", href: \"/locations/la-costa\" },\n  { name: \"Via 23rd\", href: \"/locations/23rd\" },\n  { name: \"ADBC\", href: \"/locations/adbc\" }\n];\n\nconst services = [\n  { name: \"Virtual Offices\", href: \"/services/virtual-offices\" },\n  { name: \"Beauty Suites\", href: \"/services/beauty-suites\" },\n  { name: \"Executive Suites\", href: \"/services/executive-suites\" }\n];\n\nconst socialLinks = [\n  { name: \"Facebook\", href: \"https://www.facebook.com/viatosuccess/\", icon: Facebook },\n  { name: \"Instagram\", href: \"https://www.instagram.com/viatosuccess/\", icon: Instagram },\n  { name: \"LinkedIn\", href: \"https://www.linkedin.com/company/via-executive-suites/\", icon: Linkedin },\n  { name: \"YouTube\", href: \"https://www.youtube.com/channel/UCzAaMIx4GQqXgLv7k_7meuA\", icon: Youtube },\n  { name: \"Twitter\", href: \"https://twitter.com/viatosuccess\", icon: Twitter }\n];\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"mb-6\">\n              <Image\n                src=\"https://via2success.com/wp-content/themes/viatosuccess_v1/assets/images/logo-footer.svg\"\n                alt=\"Via Executive Suites\"\n                width={150}\n                height={40}\n                className=\"h-10 w-auto filter brightness-0 invert\"\n              />\n            </div>\n            \n            <p className=\"text-gray-300 text-sm leading-relaxed mb-6\">\n              Via Executive Suites has four all inclusive business centers strategically located in\n              the Rio Grande Valley. So we take pride in helping entrepreneurs and startups. \n              And we also make it our mission to enhance the enterprise, identity, and integrity\n              of our clients in order to further our region&apos;s economy.\n            </p>\n            \n            <div className=\"text-sm text-gray-300\">\n              <div className=\"font-semibold text-white mb-1\">Main Phone:</div>\n              <div>(*************</div>\n            </div>\n          </div>\n\n          {/* Locations */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Locations</h3>\n            <ul className=\"space-y-2\">\n              {locations.map((location) => (\n                <li key={location.name}>\n                  <Link \n                    href={location.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {location.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Services</h3>\n            <ul className=\"space-y-2\">\n              {services.map((service) => (\n                <li key={service.name}>\n                  <Link \n                    href={service.href}\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\"\n                  >\n                    {service.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Follow Us */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Follow Us</h3>\n            <div className=\"flex space-x-4\">\n              {socialLinks.map((social) => {\n                const IconComponent = social.icon;\n                return (\n                  <a\n                    key={social.name}\n                    href={social.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-gray-300 hover:text-white transition-colors duration-200\"\n                    aria-label={social.name}\n                  >\n                    <IconComponent className=\"w-5 h-5\" />\n                  </a>\n                );\n              })}\n            </div>\n            \n            <div className=\"mt-6\">\n              <h4 className=\"text-sm font-semibold mb-2\">Quick Links</h4>\n              <ul className=\"space-y-1\">\n                <li>\n                  <Link href=\"/about\" className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\">\n                    About\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/blog\" className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\">\n                    Blog\n                  </Link>\n                </li>\n                <li>\n                  <Link href=\"/connect\" className=\"text-gray-300 hover:text-white transition-colors duration-200 text-sm\">\n                    Connect\n                  </Link>\n                </li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <div className=\"text-sm text-gray-400\">\n            &copy; 2025 Via Executive Suites. All rights reserved.\n          </div>\n          \n          <div className=\"mt-4 md:mt-0\">\n            <Link \n              href=\"/privacy-policy\" \n              className=\"text-sm text-gray-400 hover:text-white transition-colors duration-200\"\n            >\n              Privacy Policy\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;AAEA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAgB,MAAM;IAAsB;IACpD;QAAE,MAAM;QAAY,MAAM;IAAsB;IAChD;QAAE,MAAM;QAAY,MAAM;IAAkB;IAC5C;QAAE,MAAM;QAAQ,MAAM;IAAkB;CACzC;AAED,MAAM,WAAW;IACf;QAAE,MAAM;QAAmB,MAAM;IAA4B;IAC7D;QAAE,MAAM;QAAiB,MAAM;IAA0B;IACzD;QAAE,MAAM;QAAoB,MAAM;IAA6B;CAChE;AAED,MAAM,cAAc;IAClB;QAAE,MAAM;QAAY,MAAM;QAA0C,MAAM,4TAAQ;IAAC;IACnF;QAAE,MAAM;QAAa,MAAM;QAA2C,MAAM,+TAAS;IAAC;IACtF;QAAE,MAAM;QAAY,MAAM;QAA0D,MAAM,4TAAQ;IAAC;IACnG;QAAE,MAAM;QAAW,MAAM;QAA4D,MAAM,yTAAO;IAAC;IACnG;QAAE,MAAM;QAAW,MAAM;QAAoC,MAAM,yTAAO;IAAC;CAC5E;AAEM,SAAS;IACd,qBACE,+XAAC;QAAO,WAAU;kBAChB,cAAA,+XAAC;YAAI,WAAU;;8BACb,+XAAC;oBAAI,WAAU;;sCAEb,+XAAC;4BAAI,WAAU;;8CACb,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC,yRAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAId,+XAAC;oCAAE,WAAU;8CAA6C;;;;;;8CAO1D,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;sDAAgC;;;;;;sDAC/C,+XAAC;sDAAI;;;;;;;;;;;;;;;;;;sCAKT,+XAAC;;8CACC,+XAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,+XAAC;oCAAG,WAAU;8CACX,UAAU,GAAG,CAAC,CAAC,yBACd,+XAAC;sDACC,cAAA,+XAAC,wTAAI;gDACH,MAAM,SAAS,IAAI;gDACnB,WAAU;0DAET,SAAS,IAAI;;;;;;2CALT,SAAS,IAAI;;;;;;;;;;;;;;;;sCAa5B,+XAAC;;8CACC,+XAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,+XAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,wBACb,+XAAC;sDACC,cAAA,+XAAC,wTAAI;gDACH,MAAM,QAAQ,IAAI;gDAClB,WAAU;0DAET,QAAQ,IAAI;;;;;;2CALR,QAAQ,IAAI;;;;;;;;;;;;;;;;sCAa3B,+XAAC;;8CACC,+XAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,+XAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,gBAAgB,OAAO,IAAI;wCACjC,qBACE,+XAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAY,OAAO,IAAI;sDAEvB,cAAA,+XAAC;gDAAc,WAAU;;;;;;2CAPpB,OAAO,IAAI;;;;;oCAUtB;;;;;;8CAGF,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,+XAAC;4CAAG,WAAU;;8DACZ,+XAAC;8DACC,cAAA,+XAAC,wTAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAwE;;;;;;;;;;;8DAIxG,+XAAC;8DACC,cAAA,+XAAC,wTAAI;wDAAC,MAAK;wDAAQ,WAAU;kEAAwE;;;;;;;;;;;8DAIvG,+XAAC;8DACC,cAAA,+XAAC,wTAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAwE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUlH,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAI,WAAU;sCAAwB;;;;;;sCAIvC,+XAAC;4BAAI,WAAU;sCACb,cAAA,+XAAC,wTAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/page-transition.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const PageTransition = registerClientReference(\n    function() { throw new Error(\"Attempted to call PageTransition() from the server but PageTransition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/page-transition.tsx <module evaluation>\",\n    \"PageTransition\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,iBAAiB,IAAA,yZAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,4EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/page-transition.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const PageTransition = registerClientReference(\n    function() { throw new Error(\"Attempted to call PageTransition() from the server but PageTransition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/page-transition.tsx\",\n    \"PageTransition\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,iBAAiB,IAAA,yZAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,wDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 485, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON> } from \"next/font/google\";\nimport { ThemeProvider } from \"next-themes\";\nimport { Navbar } from \"@/components/navbar\";\nimport { Footer } from \"@/components/footer\";\nimport { PageTransition } from \"@/components/page-transition\";\nimport \"./globals.css\";\n\nconst defaultUrl = process.env.VERCEL_URL\n  ? `https://${process.env.VERCEL_URL}`\n  : \"http://localhost:3000\";\n\nexport const metadata: Metadata = {\n  metadataBase: new URL(defaultUrl),\n  title: \"Via Executive Suites - Premium Workspace Solutions\",\n  description: \"Transform your business with Via Executive Suites. Premium office spaces, meeting rooms, coworking environments, and virtual office solutions in the Rio Grande Valley and beyond.\",\n  keywords: [\"office space\", \"executive suites\", \"coworking\", \"virtual offices\", \"Rio Grande Valley\", \"business solutions\"],\n  authors: [{ name: \"Via Executive Suites\" }],\n  openGraph: {\n    title: \"Via Executive Suites - Premium Workspace Solutions\",\n    description: \"Transform your business with Via Executive Suites. Premium office spaces, meeting rooms, coworking environments, and virtual office solutions.\",\n    type: \"website\",\n    locale: \"en_US\",\n  },\n};\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  display: \"swap\",\n  subsets: [\"latin\"],\n});\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <body className={`${geistSans.className} antialiased min-h-screen flex flex-col`} suppressHydrationWarning>\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"system\"\n          enableSystem\n          disableTransitionOnChange\n        >\n          <Navbar />\n          <main className=\"flex-1 overflow-x-hidden\">\n            <PageTransition>\n              {children}\n            </PageTransition>\n          </main>\n          <Footer />\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AACA;;;;;;;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,GACrC,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE,GACnC;AAEG,MAAM,WAAqB;IAChC,cAAc,IAAI,IAAI;IACtB,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAgB;QAAoB;QAAa;QAAmB;QAAqB;KAAqB;IACzH,SAAS;QAAC;YAAE,MAAM;QAAuB;KAAE;IAC3C,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;AACF;AAQe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,+XAAC;QAAK,MAAK;QAAK,wBAAwB;kBACtC,cAAA,+XAAC;YAAK,WAAW,GAAG,mJAAS,CAAC,SAAS,CAAC,uCAAuC,CAAC;YAAE,wBAAwB;sBACxG,cAAA,+XAAC,4RAAa;gBACZ,WAAU;gBACV,cAAa;gBACb,YAAY;gBACZ,yBAAyB;;kCAEzB,+XAAC,iJAAM;;;;;kCACP,+XAAC;wBAAK,WAAU;kCACd,cAAA,+XAAC,qKAAc;sCACZ;;;;;;;;;;;kCAGL,+XAAC,iJAAM;;;;;;;;;;;;;;;;;;;;;AAKjB", "debugId": null}}]}