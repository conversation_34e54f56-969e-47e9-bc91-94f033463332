{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/hero-section.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HeroSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call HeroSection() from the server but HeroSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/hero-section.tsx <module evaluation>\",\n    \"HeroSection\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,cAAc,IAAA,yZAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/hero-section.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HeroSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call HeroSec<PERSON>() from the server but HeroSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/hero-section.tsx\",\n    \"HeroSection\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,cAAc,IAAA,yZAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,qDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/features-grid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const FeaturesGrid = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeaturesGrid() from the server but FeaturesGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/features-grid.tsx <module evaluation>\",\n    \"FeaturesGrid\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,eAAe,IAAA,yZAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/features-grid.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const FeaturesGrid = registerClientReference(\n    function() { throw new Error(\"Attempted to call FeaturesGrid() from the server but FeaturesGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/features-grid.tsx\",\n    \"FeaturesGrid\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,eAAe,IAAA,yZAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about-section.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AboutSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call AboutSection() from the server but AboutSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/about-section.tsx <module evaluation>\",\n    \"AboutSection\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,eAAe,IAAA,yZAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about-section.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AboutSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call AboutSection() from the server but AboutSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/about-section.tsx\",\n    \"AboutSection\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,eAAe,IAAA,yZAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/gallery-section.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GallerySection = registerClientReference(\n    function() { throw new Error(\"Attempted to call GallerySection() from the server but GallerySection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/gallery-section.tsx <module evaluation>\",\n    \"GallerySection\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,iBAAiB,IAAA,yZAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,4EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/gallery-section.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GallerySection = registerClientReference(\n    function() { throw new Error(\"Attempted to call GallerySection() from the server but GallerySection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/gallery-section.tsx\",\n    \"GallerySection\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,iBAAiB,IAAA,yZAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,wDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// This check can be removed, it is just for tutorial purposes\nexport const hasEnvVars =\n  process.env.NEXT_PUBLIC_SUPABASE_URL &&\n  process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sPAAO,EAAC,IAAA,gNAAI,EAAC;AACtB;AAGO,MAAM,aACX", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,IAAA,oQAAG,EACxB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,oWAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,+TAAI,GAAG;IAC9B,qBACE,+XAAC;QACC,WAAW,IAAA,oIAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/workspace-types.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport Image from \"next/image\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Building2, Globe, Scissors } from \"lucide-react\";\n\nconst workspaceTypes = [\n  {\n    icon: Building2,\n    title: \"Executive Suites\",\n    description: \"Our conveniently located Executive Suites offer office space plus shared amenities such as lobbies, reception areas, front desk services, break rooms, conference rooms.\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/home-section_5-img_1.jpg\",\n    href: \"/services/executive-suites\"\n  },\n  {\n    icon: Globe,\n    title: \"Virtual Offices\",\n    description: \"Our Virtual Office is an excellent option to create a professional image, business address, or if you're just looking to operate with a tight budget, we can get you set up in a matter of minutes.\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/home-section_5-img_2.jpg\",\n    href: \"/services/virtual-offices\"\n  },\n  {\n    icon: Scissors,\n    title: \"Health & Beauty Suites\",\n    description: \"Our Health and Beauty Suites provide state-of-the-art facilities for your health and beauty business. Comfortable waiting areas, beautiful work areas, front-desk staff, weekend hours, and a single, affordable, all-inclusive price help your business succeed.\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/home-section_5-img_3.jpg\",\n    href: \"/services/beauty-suites\"\n  }\n];\n\nexport function WorkspaceTypes() {\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Workspace Types\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Our four strategic locations offer a variety of suites designed to meet your specific business needs.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {workspaceTypes.map((workspace, index) => {\n            const IconComponent = workspace.icon;\n            return (\n              <div \n                key={index}\n                className=\"bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group\"\n              >\n                {/* Image */}\n                <div className=\"relative h-48 overflow-hidden\">\n                  <Image\n                    src={workspace.image}\n                    alt={workspace.title}\n                    width={400}\n                    height={300}\n                    className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300\"></div>\n                </div>\n\n                {/* Content */}\n                <div className=\"p-6\">\n                  <div className=\"flex items-center mb-4\">\n                    <div className=\"inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-full mr-4\">\n                      <IconComponent className=\"w-6 h-6 text-primary\" />\n                    </div>\n                    <h3 className=\"text-xl font-semibold text-gray-900\">\n                      {workspace.title}\n                    </h3>\n                  </div>\n                  \n                  <p className=\"text-gray-600 mb-6 leading-relaxed\">\n                    {workspace.description}\n                  </p>\n                  \n                  <Link href={workspace.href}>\n                    <Button \n                      variant=\"outline\"\n                      className=\"w-full border-primary text-primary hover:bg-primary hover:text-white transition-all duration-300\"\n                    >\n                      Learn More\n                    </Button>\n                  </Link>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n\n        {/* Bottom CTA */}\n        <div className=\"text-center mt-12\">\n          <Link href=\"/services\">\n            <Button \n              size=\"lg\"\n              className=\"bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg font-semibold rounded-md transition-all duration-300 transform hover:scale-105\"\n            >\n              View All Workspaces\n            </Button>\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;;;;;;AAEA,MAAM,iBAAiB;IACrB;QACE,MAAM,mUAAS;QACf,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,MAAM,mTAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;IACR;IACA;QACE,MAAM,4TAAQ;QACd,OAAO;QACP,aAAa;QACb,OAAO;QACP,MAAM;IACR;CACD;AAEM,SAAS;IACd,qBACE,+XAAC;QAAQ,WAAU;kBACjB,cAAA,+XAAC;YAAI,WAAU;;8BACb,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,+XAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,+XAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,WAAW;wBAC9B,MAAM,gBAAgB,UAAU,IAAI;wBACpC,qBACE,+XAAC;4BAEC,WAAU;;8CAGV,+XAAC;oCAAI,WAAU;;sDACb,+XAAC,yRAAK;4CACJ,KAAK,UAAU,KAAK;4CACpB,KAAK,UAAU,KAAK;4CACpB,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,+XAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIjB,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAI,WAAU;8DACb,cAAA,+XAAC;wDAAc,WAAU;;;;;;;;;;;8DAE3B,+XAAC;oDAAG,WAAU;8DACX,UAAU,KAAK;;;;;;;;;;;;sDAIpB,+XAAC;4CAAE,WAAU;sDACV,UAAU,WAAW;;;;;;sDAGxB,+XAAC,wTAAI;4CAAC,MAAM,UAAU,IAAI;sDACxB,cAAA,+XAAC,uJAAM;gDACL,SAAQ;gDACR,WAAU;0DACX;;;;;;;;;;;;;;;;;;2BAlCA;;;;;oBAyCX;;;;;;8BAIF,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC,wTAAI;wBAAC,MAAK;kCACT,cAAA,+XAAC,uJAAM;4BACL,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/testimonials-section.tsx"], "sourcesContent": ["import { Quote } from \"lucide-react\";\n\nconst testimonials = [\n  {\n    quote: \"That moment when you realize everything will be just fine in our new location! I received a call from <PERSON><PERSON><PERSON>, the manager at ADBC, to tell me new customers are looking for us already in the new location!! He forwarded the customer to us! Thank you <PERSON><PERSON><PERSON>, <PERSON> and <PERSON> for all your good customer service. Looking forward for great business for all.\",\n    author: \"Vital Camera Mary\",\n    company: \"Vital Camera\",\n    location: \"ADBC Location\"\n  },\n  {\n    quote: \"<PERSON><PERSON><PERSON> and all the staff at ADBC have been extremely helpful since I started Pronto Credit in McAllen on 2011.\",\n    author: \"<PERSON>\",\n    company: \"Pronto Credit\",\n    location: \"ADBC Location\"\n  }\n];\n\nexport function TestimonialsSection() {\n  return (\n    <section className=\"py-16 bg-primary text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\n            Testimonials\n          </h2>\n          <p className=\"text-lg text-blue-100 max-w-3xl mx-auto\">\n            Hear what our clients have to say about their experience with Via Executive Suites\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {testimonials.map((testimonial, index) => (\n            <div \n              key={index}\n              className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 hover:bg-white/20 transition-all duration-300\"\n            >\n              {/* Quote Icon */}\n              <div className=\"flex justify-center mb-6\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-white/20 rounded-full\">\n                  <Quote className=\"w-6 h-6 text-white\" />\n                </div>\n              </div>\n\n              {/* Quote Text */}\n              <blockquote className=\"text-center mb-6\">\n                <p className=\"text-lg leading-relaxed text-white/90 italic\">\n                  &ldquo;{testimonial.quote}&rdquo;\n                </p>\n              </blockquote>\n\n              {/* Attribution */}\n              <div className=\"text-center\">\n                <div className=\"font-semibold text-white text-lg\">\n                  {testimonial.author}\n                </div>\n                <div className=\"text-blue-200 text-sm\">\n                  {testimonial.company}\n                </div>\n                <div className=\"text-blue-300 text-xs mt-1\">\n                  {testimonial.location}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Additional Testimonial Feature */}\n        <div className=\"mt-12 text-center\">\n          <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 max-w-4xl mx-auto\">\n            <div className=\"flex items-center justify-center mb-6\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-yellow-400 rounded-full\">\n                <svg className=\"w-8 h-8 text-yellow-800\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                </svg>\n              </div>\n            </div>\n            \n            <h3 className=\"text-2xl font-bold mb-4\">\n              Complimentary Starbucks Coffee\n            </h3>\n            \n            <p className=\"text-lg text-white/90\">\n              Enjoy complimentary Starbucks coffee at any of our four locations.\n            </p>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEA,MAAM,eAAe;IACnB;QACE,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;CACD;AAEM,SAAS;IACd,qBACE,+XAAC;QAAQ,WAAU;kBACjB,cAAA,+XAAC;YAAI,WAAU;;8BACb,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,+XAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAKzD,+XAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,+XAAC;4BAEC,WAAU;;8CAGV,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC;wCAAI,WAAU;kDACb,cAAA,+XAAC,mTAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAKrB,+XAAC;oCAAW,WAAU;8CACpB,cAAA,+XAAC;wCAAE,WAAU;;4CAA+C;4CAClD,YAAY,KAAK;4CAAC;;;;;;;;;;;;8CAK9B,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;4CAAI,WAAU;sDACZ,YAAY,MAAM;;;;;;sDAErB,+XAAC;4CAAI,WAAU;sDACZ,YAAY,OAAO;;;;;;sDAEtB,+XAAC;4CAAI,WAAU;sDACZ,YAAY,QAAQ;;;;;;;;;;;;;2BA1BpB;;;;;;;;;;8BAkCX,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC;wCAAI,WAAU;wCAA0B,MAAK;wCAAe,SAAQ;kDACnE,cAAA,+XAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;;;;;;0CAKd,+XAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAIxC,+XAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}, {"offset": {"line": 684, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/contact-form-section.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ContactFormSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call ContactFormSection() from the server but ContactFormSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/contact-form-section.tsx <module evaluation>\",\n    \"ContactFormSection\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,qBAAqB,IAAA,yZAAuB,EACrD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,iFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/contact-form-section.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ContactFormSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call ContactFormSection() from the server but ContactFormSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/contact-form-section.tsx\",\n    \"ContactFormSection\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,qBAAqB,IAAA,yZAAuB,EACrD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,6DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 712, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/locations-section.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const LocationsSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call LocationsSection() from the server but LocationsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/locations-section.tsx <module evaluation>\",\n    \"LocationsSection\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,mBAAmB,IAAA,yZAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 734, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/locations-section.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const LocationsSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call LocationsSection() from the server but LocationsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/components/locations-section.tsx\",\n    \"LocationsSection\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,mBAAmB,IAAA,yZAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/page.tsx"], "sourcesContent": ["import { HeroSection } from \"@/components/hero-section\";\nimport { FeaturesGrid } from \"@/components/features-grid\";\nimport { AboutSection } from \"@/components/about-section\";\nimport { GallerySection } from \"@/components/gallery-section\";\nimport { WorkspaceTypes } from \"@/components/workspace-types\";\nimport { TestimonialsSection } from \"@/components/testimonials-section\";\nimport { ContactFormSection } from \"@/components/contact-form-section\";\nimport { LocationsSection } from \"@/components/locations-section\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <HeroSection />\n      <FeaturesGrid />\n      <AboutSection />\n      <GallerySection />\n      <WorkspaceTypes />\n      <LocationsSection />\n      <TestimonialsSection />\n      <ContactFormSection />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEe,SAAS;IACtB,qBACE,+XAAC;QAAI,WAAU;;0BACb,+XAAC,+JAAW;;;;;0BACZ,+XAAC,iKAAY;;;;;0BACb,+XAAC,iKAAY;;;;;0BACb,+XAAC,qKAAc;;;;;0BACf,+XAAC,qKAAc;;;;;0BACf,+XAAC,yKAAgB;;;;;0BACjB,+XAAC,+KAAmB;;;;;0BACpB,+XAAC,iLAAkB;;;;;;;;;;;AAGzB", "debugId": null}}]}