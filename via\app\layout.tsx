import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { ThemeProvider } from "next-themes";
import { Navbar } from "@/components/navbar";
import { Footer } from "@/components/footer";
import { PageTransition } from "@/components/page-transition";
import "./globals.css";

const defaultUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : "http://localhost:3000";

export const metadata: Metadata = {
  metadataBase: new URL(defaultUrl),
  title: "Via Executive Suites - Premium Workspace Solutions",
  description: "Transform your business with Via Executive Suites. Premium office spaces, meeting rooms, coworking environments, and virtual office solutions in the Rio Grande Valley and beyond.",
  keywords: ["office space", "executive suites", "coworking", "virtual offices", "Rio Grande Valley", "business solutions"],
  authors: [{ name: "Via Executive Suites" }],
  openGraph: {
    title: "Via Executive Suites - Premium Workspace Solutions",
    description: "Transform your business with Via Executive Suites. Premium office spaces, meeting rooms, coworking environments, and virtual office solutions.",
    type: "website",
    locale: "en_US",
  },
};

const geistSans = Geist({
  variable: "--font-geist-sans",
  display: "swap",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${geistSans.className} antialiased min-h-screen flex flex-col`} suppressHydrationWarning>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <Navbar />
          <main className="flex-1 overflow-x-hidden">
            <PageTransition>
              {children}
            </PageTransition>
          </main>
          <Footer />
        </ThemeProvider>
      </body>
    </html>
  );
}
