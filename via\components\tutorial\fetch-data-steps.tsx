"use client";

import { useState, useEffect } from "react";
import { createClient } from "@/lib/supabase/client";
import { TutorialStep } from "./tutorial-step";

interface Note {
  id: number;
  title: string;
  content: string;
  created_at: string;
}

export function FetchDataSteps() {
  const [notes, setNotes] = useState<Note[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchNotes() {
      try {
        const supabase = createClient();
        const { data, error } = await supabase
          .from("notes")
          .select("*")
          .order("created_at", { ascending: false });

        if (error) {
          throw error;
        }

        setNotes(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      } finally {
        setLoading(false);
      }
    }

    fetchNotes();
  }, []);

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        <p className="mt-2 text-gray-600">Loading notes...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong className="font-bold">Error:</strong>
          <span className="block sm:inline"> {error}</span>
        </div>
        <p className="text-gray-600">Failed to fetch notes from the database.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <TutorialStep title="Fetch Data from Supabase">
        Retrieve data from your Supabase database using the client.
      </TutorialStep>

      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Notes from Database</h3>
        {notes && notes.length > 0 ? (
          <div className="space-y-4">
            {notes.map((note) => (
              <div key={note.id} className="bg-white p-4 rounded-lg border">
                <h4 className="font-semibold text-gray-900">{note.title}</h4>
                <p className="text-gray-600 mt-2">{note.content}</p>
                <p className="text-sm text-gray-500 mt-2">
                  Created: {new Date(note.created_at).toLocaleDateString()}
                </p>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-center py-8">
            No notes found. Create some notes first using the previous step.
          </p>
        )}
      </div>

      <TutorialStep title="Display Data in UI">
        Render the fetched data in your React components with proper loading and error states.
      </TutorialStep>

      <div className="bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">Key Concepts</h3>
        <ul className="space-y-2 text-sm text-gray-700">
          <li>• Use <code className="bg-gray-200 px-1 rounded">useEffect</code> for data fetching</li>
          <li>• Implement loading states for better UX</li>
          <li>• Handle errors gracefully with user-friendly messages</li>
          <li>• Use TypeScript interfaces for type safety</li>
          <li>• Order data by creation date for logical display</li>
        </ul>
      </div>
    </div>
  );
}
