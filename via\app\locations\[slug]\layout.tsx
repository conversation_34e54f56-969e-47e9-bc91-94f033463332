import { Metadata } from "next";
import { LocationSlugs } from "@/lib/types/location";

// Location data - in a real app, this would come from a database
const locationData: Record<LocationSlugs, {
  name: string;
  fullName: string;
  address: string;
}> = {
  "adbc": {
    name: "ADBC",
    fullName: "Via Executive Suites ADBC",
    address: "813 N. Main St., McAllen, Texas 78501",
  },
  "la-costa": {
    name: "La Costa",
    fullName: "Via Executive Suites La Costa",
    address: "214 N 16th St, McAllen, Texas 78501",
  },
  "23rd": {
    name: "23rd",
    fullName: "Via Executive Suites 23rd",
    address: "1821 N 23rd Street, McAllen, Texas 78501",
  },
  "edinburg": {
    name: "Edinburg",
    fullName: "Via Executive Suites Edinburg",
    address: "1409 S 9th Ave, Edinburg, Texas 78539",
  },
  "lindberg": {
    name: "Lindberg",
    fullName: "Via Executive Suites Lindberg",
    address: "1234 Lindberg Ave, McAllen, Texas 78501",
  }
};

interface LocationLayoutProps {
  children: React.ReactNode;
  params: Promise<{
    slug: string;
  }>;
}

// Generate static paths for all location slugs
export async function generateStaticParams() {
  return Object.keys(locationData).map((slug) => ({
    slug,
  }));
}

export async function generateMetadata({ params }: LocationLayoutProps): Promise<Metadata> {
  const resolvedParams = await params;
  const location = locationData[resolvedParams.slug as LocationSlugs];
  
  if (!location) {
    return {
      title: "Location Not Found - Via Executive Suites",
    };
  }

  return {
    title: `${location.fullName} - Via Executive Suites`,
    description: `Visit ${location.fullName} at ${location.address}. Professional office spaces with premium amenities and business support services.`,
    keywords: `${location.name}, office space, McAllen, Edinburg, Rio Grande Valley, executive suites`,
    openGraph: {
      title: `${location.fullName} - Via Executive Suites`,
      description: `Visit ${location.fullName} at ${location.address}. Professional office spaces with premium amenities.`,
      type: "website",
      locale: "en_US",
    },
  };
}

export default function LocationLayout({ children }: LocationLayoutProps) {
  return children;
}
