// Via Executive Suites - Button Styling Utilities
// This file provides consistent button styling to prevent visibility issues

export const buttonStyles = {
  // Primary buttons - for main actions
  primary: {
    base: "bg-via-primary hover:bg-via-primary-dark text-white",
    large: "bg-via-primary hover:bg-via-primary-dark text-white px-8 py-3",
    full: "w-full bg-via-primary hover:bg-via-primary-dark text-white"
  },

  // Secondary buttons - for secondary actions
  secondary: {
    base: "bg-gray-100 hover:bg-gray-200 text-gray-900",
    large: "bg-gray-100 hover:bg-gray-200 text-gray-900 px-8 py-3",
    full: "w-full bg-gray-100 hover:bg-gray-200 text-gray-900"
  },

  // Outline buttons - for less prominent actions
  outline: {
    primary: "border-via-primary text-via-primary hover:bg-via-primary hover:text-white",
    white: "border-white text-white hover:bg-white hover:text-via-primary",
    large: {
      primary: "border-via-primary text-via-primary hover:bg-via-primary hover:text-white px-8 py-3",
      white: "border-white text-white hover:bg-white hover:text-via-primary px-8 py-3"
    }
  },

  // Accent buttons - for beauty suites and special services
  accent: {
    base: "bg-via-accent hover:bg-via-accent-dark text-white",
    large: "bg-via-accent hover:bg-via-accent-dark text-white px-8 py-3",
    full: "w-full bg-via-accent hover:bg-via-accent-dark text-white"
  },

  // White buttons - for dark backgrounds
  white: {
    base: "bg-white text-via-primary hover:bg-gray-100",
    large: "bg-white text-via-primary hover:bg-gray-100 px-8 py-3"
  }
};

// Helper function to get button styles
export function getButtonStyle(type: keyof typeof buttonStyles, variant?: string): string {
  if (variant && buttonStyles[type][variant]) {
    return buttonStyles[type][variant];
  }
  return buttonStyles[type].base;
}

// Common button combinations
export const commonButtons = {
  hero: {
    primary: buttonStyles.white.large,
    secondary: buttonStyles.outline.large.white
  },
  cta: {
    primary: buttonStyles.white.large,
    secondary: buttonStyles.outline.large.white
  },
  pricing: {
    primary: buttonStyles.primary.full,
    secondary: buttonStyles.primary.full
  },
  service: {
    primary: buttonStyles.primary.full
  }
};
