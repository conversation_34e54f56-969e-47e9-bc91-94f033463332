"use client";

import { motion } from "framer-motion";
import { 
  fadeInUp, 
  fadeInDown, 
  staggerContainer, 
  staggerItem, 
  animationConfig 
} from "@/lib/animations";

interface Service {
  title: string;
  description: string;
  features: string[];
  icon?: string;
}

interface ServicesSectionProps {
  title?: string;
  subtitle?: string;
  services?: Service[];
}

const defaultServices: Service[] = [
  {
    title: "Executive Suites",
    description: "Premium furnished office spaces designed for professionals who demand excellence.",
    features: [
      "Fully furnished private offices",
      "Professional business address",
      "24/7 secure access",
      "High-speed internet included",
      "Conference room access",
      "Reception services"
    ],
    icon: "🏢"
  },
  {
    title: "Virtual Offices",
    description: "Professional business presence without the overhead of a physical office space.",
    features: [
      "Prestigious business address",
      "Mail handling services",
      "Phone answering service",
      "Meeting room access",
      "Professional receptionist",
      "Flexible terms"
    ],
    icon: "💼"
  },
  {
    title: "Beauty Suites",
    description: "Specialized suites designed for beauty professionals and wellness practitioners.",
    features: [
      "Fully equipped beauty stations",
      "Professional lighting",
      "Comfortable client seating",
      "Storage solutions",
      "Flexible scheduling",
      "Professional atmosphere"
    ],
    icon: "✨"
  }
];

export default function ServicesSection({
  title = "Our Services",
  subtitle = "Discover our comprehensive range of professional workspace solutions designed to help you succeed in today's competitive business landscape.",
  services = defaultServices
}: ServicesSectionProps) {
  return (
    <section className="py-16 bg-gradient-to-b from-gray-50 to-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={animationConfig.viewport}
            variants={staggerContainer}
            className="text-center mb-16"
          >
            <motion.h2
              variants={fadeInDown}
              className="text-4xl md:text-5xl font-bold text-primary mb-6"
            >
              {title}
            </motion.h2>
            <motion.p
              variants={fadeInUp}
              className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed"
            >
              {subtitle}
            </motion.p>
          </motion.div>

          {/* Services Grid */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={animationConfig.viewport}
            variants={staggerContainer}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {services.map((service, index) => (
              <motion.div
                key={index}
                variants={staggerItem}
                whileHover={{ 
                  y: -8, 
                  scale: 1.02,
                  transition: { duration: 0.2, ease: "easeInOut" }
                }}
                className="bg-white rounded-xl shadow-lg p-8 border border-gray-100 hover:shadow-xl transition-shadow duration-300"
              >
                {/* Service Icon */}
                {service.icon && (
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    viewport={animationConfig.viewport}
                    transition={{ delay: index * 0.1 + 0.3, duration: 0.3 }}
                    className="text-4xl mb-6 text-center"
                  >
                    {service.icon}
                  </motion.div>
                )}

                {/* Service Title */}
                <motion.h3
                  variants={fadeInUp}
                  className="text-2xl font-semibold text-primary mb-4 text-center"
                >
                  {service.title}
                </motion.h3>

                {/* Service Description */}
                <motion.p
                  variants={fadeInUp}
                  className="text-gray-600 mb-6 text-center leading-relaxed"
                >
                  {service.description}
                </motion.p>

                {/* Service Features */}
                <motion.ul
                  variants={staggerContainer}
                  className="space-y-3"
                >
                  {service.features.map((feature, featureIndex) => (
                    <motion.li
                      key={featureIndex}
                      variants={staggerItem}
                      className="flex items-center text-sm text-gray-600"
                    >
                      <motion.span
                        initial={{ scale: 0 }}
                        whileInView={{ scale: 1 }}
                        viewport={animationConfig.viewport}
                        transition={{ 
                          delay: index * 0.1 + featureIndex * 0.05 + 0.5,
                          duration: 0.2 
                        }}
                        className="w-2 h-2 bg-primary rounded-full mr-3 flex-shrink-0"
                      />
                      <span>{feature}</span>
                    </motion.li>
                  ))}
                </motion.ul>

                {/* Call to Action */}
                <motion.div
                  variants={fadeInUp}
                  className="mt-8 text-center"
                >
                  <motion.button
                    whileHover={{ 
                      scale: 1.05,
                      backgroundColor: "#1e3a5f",
                      transition: { duration: 0.2 }
                    }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors duration-200"
                  >
                    Learn More
                  </motion.button>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>

          {/* Bottom CTA Section */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={animationConfig.viewport}
            variants={staggerContainer}
            className="text-center mt-16"
          >
            <motion.h3
              variants={fadeInUp}
              className="text-2xl font-semibold text-primary mb-4"
            >
              Ready to Get Started?
            </motion.h3>
            <motion.p
              variants={fadeInUp}
              className="text-gray-600 mb-8 max-w-2xl mx-auto"
            >
              Contact us today to schedule a tour and find the perfect workspace solution for your business needs.
            </motion.p>
            <motion.div
              variants={fadeInUp}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <motion.button
                whileHover={{ 
                  scale: 1.05,
                  backgroundColor: "#1e3a5f",
                  transition: { duration: 0.2 }
                }}
                whileTap={{ scale: 0.95 }}
                className="bg-primary text-white px-8 py-4 rounded-lg font-medium hover:bg-primary/90 transition-colors duration-200"
              >
                Schedule a Tour
              </motion.button>
              <motion.button
                whileHover={{ 
                  scale: 1.05,
                  borderColor: "#1e3a5f",
                  color: "#1e3a5f",
                  transition: { duration: 0.2 }
                }}
                whileTap={{ scale: 0.95 }}
                className="border-2 border-primary text-primary px-8 py-4 rounded-lg font-medium hover:bg-primary hover:text-white transition-all duration-200"
              >
                Get Free Consultation
              </motion.button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
