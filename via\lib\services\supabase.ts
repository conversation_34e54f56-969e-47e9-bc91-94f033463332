import { createClient } from '@/lib/supabase/client';
import { Service, ServiceCategory } from '@/lib/types/services';

// Supabase client for services
const supabase = createClient();

// Future database queries - these will replace the static data
export async function getServicesFromDatabase(): Promise<Service[]> {
  try {
    const { data, error } = await supabase
      .from('via_services')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching services:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching services:', error);
    return [];
  }
}

export async function getServiceBySlugFromDatabase(slug: string): Promise<Service | null> {
  try {
    const { data, error } = await supabase
      .from('via_services')
      .select('*')
      .eq('slug', slug)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching service:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error fetching service:', error);
    return null;
  }
}

export async function getServiceCategoriesFromDatabase(): Promise<ServiceCategory[]> {
  try {
    const { data, error } = await supabase
      .from('via_service_categories')
      .select(`
        *,
        via_services (*)
      `)
      .eq('is_active', true)
      .order('name', { ascending: true });

    if (error) {
      console.error('Error fetching service categories:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching service categories:', error);
    return [];
  }
}

export async function getServicesByCategoryFromDatabase(category: string): Promise<Service[]> {
  try {
    const { data, error } = await supabase
      .from('via_services')
      .select('*')
      .eq('category', category)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching services by category:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching services by category:', error);
    return [];
  }
}

// Search services by keyword
export async function searchServicesFromDatabase(query: string): Promise<Service[]> {
  try {
    const { data, error } = await supabase
      .from('via_services')
      .select('*')
      .eq('is_active', true)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%,features.ilike.%${query}%`)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error searching services:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error searching services:', error);
    return [];
  }
}

// Get services by location
export async function getServicesByLocationFromDatabase(locationSlug: string): Promise<Service[]> {
  try {
    const { data, error } = await supabase
      .from('via_services')
      .select('*')
      .eq('is_active', true)
      .contains('locations', [locationSlug])
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching services by location:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching services by location:', error);
    return [];
  }
}

// Get popular services (based on views or bookings)
export async function getPopularServicesFromDatabase(limit: number = 6): Promise<Service[]> {
  try {
    const { data, error } = await supabase
      .from('via_services')
      .select('*')
      .eq('is_active', true)
      .order('views', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching popular services:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching popular services:', error);
    return [];
  }
}

// Future: Create service booking
export async function createServiceBooking(serviceId: string, userId: string, bookingData: any) {
  try {
    const { data, error } = await supabase
      .from('via_service_bookings')
      .insert({
        service_id: serviceId,
        user_id: userId,
        ...bookingData,
        status: 'pending'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating service booking:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error creating service booking:', error);
    return null;
  }
}

// Future: Get user's service bookings
export async function getUserServiceBookings(userId: string) {
  try {
    const { data, error } = await supabase
      .from('via_service_bookings')
      .select(`
        *,
        via_services (*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user service bookings:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching user service bookings:', error);
    return [];
  }
}
