export interface ServiceFeature {
  icon: string;
  title: string;
  description: string;
}

export interface PricingPlan {
  id: string;
  name: string;
  price: number;
  period: 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly';
  description: string;
  features: string[];
  popular: boolean;
  sqft?: number;
  maxOccupancy?: number;
}

export interface Service {
  id: string;
  slug: string;
  name: string;
  shortDescription: string;
  longDescription: string;
  heroImage: string;
  overviewImage: string;
  features: ServiceFeature[];
  pricingPlans: PricingPlan[];
  amenities: string[];
  locations: string[];
  category: 'executive-suites' | 'virtual-offices' | 'beauty-suites';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ServiceCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  color: string;
  services: Service[];
}

export interface ServicePageProps {
  params: Promise<{
    slug: string;
  }>;
}
