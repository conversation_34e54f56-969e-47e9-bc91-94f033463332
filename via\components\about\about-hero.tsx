"use client";

import { motion } from "framer-motion";
import { heroTitle } from "@/lib/animations";

export function AboutHero() {
  return (
    <section 
      className="relative bg-primary text-white py-24 lg:py-32 overflow-hidden"
      style={{
        backgroundImage: "url('https://viatosuccess.com/wp-content/uploads/2021/01/about_us-header-bg_img.jpg')",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover"
      }}
    >
      {/* Blue Overlay */}
      <motion.div
        className="absolute inset-0 bg-primary bg-opacity-80"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      />

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <motion.h1
            className="text-4xl md:text-5xl lg:text-6xl font-bold"
            initial="hidden"
            animate="visible"
            variants={heroTitle}
          >
            About us
          </motion.h1>
        </div>
      </div>
    </section>
  );
}
