{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/blog/page.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\r\nimport { BlogPageClient } from \"./blog-client\";\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Latest News & Insights - Via Executive Suites\",\r\n  description: \"Stay updated with the latest news, insights, and tips for business success from Via Executive Suites. Expert advice on office spaces, entrepreneurship, and business growth.\",\r\n  keywords: \"business blog, office space tips, entrepreneurship, business insights, Rio Grande Valley, executive suites, virtual offices, workspace trends\",\r\n  openGraph: {\r\n    title: \"Latest News & Insights - Via Executive Suites\",\r\n    description: \"Stay updated with the latest news, insights, and tips for business success from Via Executive Suites.\",\r\n    type: \"website\",\r\n    locale: \"en_US\",\r\n  },\r\n};\r\n\r\nexport default function BlogPage() {\r\n  return (\r\n    <motion.div\r\n      className=\"min-h-screen overflow-hidden\"\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      exit=\"exit\"\r\n      variants={pageTransition}\r\n    >\r\n      {/* Hero Section */}\r\n      <section className=\"relative bg-gradient-to-r from-via-primary via-via-primary-light to-via-primary text-white py-24 lg:py-32 overflow-hidden\">\r\n        {/* Background Pattern */}\r\n        <motion.div\r\n          className=\"absolute inset-0 opacity-10\"\r\n          style={{\r\n            backgroundImage: \"url('data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')\"\r\n          }}\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 0.1 }}\r\n          transition={{ duration: 1, delay: 0.5 }}\r\n        />\r\n\r\n        {/* Gradient Overlay */}\r\n        <div className=\"absolute inset-0 bg-black/20\"></div>\r\n\r\n        {/* Content */}\r\n        <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\r\n          <motion.h1\r\n            className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight\"\r\n            initial=\"hidden\"\r\n            animate=\"visible\"\r\n            variants={heroTitle}\r\n          >\r\n            Latest News &\r\n            <br />\r\n            <span className=\"text-blue-200\">Insights</span>\r\n          </motion.h1>\r\n\r\n          <motion.p\r\n            className=\"text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto\"\r\n            initial=\"hidden\"\r\n            animate=\"visible\"\r\n            variants={heroSubtitle}\r\n          >\r\n            Expert insights, business tips, and industry trends to help your business thrive in today's competitive landscape\r\n          </motion.p>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Blog Section */}\r\n      <BlogSection />\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;AACF;AAEe,SAAS;IACtB,qBACE,+XAAC,OAAO,GAAG;QACT,WAAU;QACV,SAAQ;QACR,SAAQ;QACR,MAAK;QACL,UAAU;;0BAGV,+XAAC;gBAAQ,WAAU;;kCAEjB,+XAAC,OAAO,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,iBAAiB;wBACnB;wBACA,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAI;wBACxB,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAI;;;;;;kCAIxC,+XAAC;wBAA<PERSON>,WAAU;;;;;;kCAGf,+XAAC;wBAAI,WAAU;;0CACb,+XAAC,OAAO,EAAE;gCACR,WAAU;gCACV,SAAQ;gCACR,SAAQ;gCACR,UAAU;;oCACX;kDAEC,+XAAC;;;;;kDACD,+XAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAGlC,+XAAC,OAAO,CAAC;gCACP,WAAU;gCACV,SAAQ;gCACR,SAAQ;gCACR,UAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOL,+XAAC;;;;;;;;;;;AAGP", "debugId": null}}]}