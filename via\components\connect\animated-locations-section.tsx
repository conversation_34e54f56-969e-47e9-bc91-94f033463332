"use client";

import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { MapPin, Phone, Calendar } from "lucide-react";
import { LocationInfo, LocationSectionProps } from "@/types/connect";
import { staggerContainer, staggerItem } from "@/lib/animations";
import { cn } from "@/lib/utils";

interface AnimatedLocationCardProps {
  location: LocationInfo;
  index: number;
}

function AnimatedLocationCard({ location, index }: AnimatedLocationCardProps) {
  return (
    <motion.div
      className="bg-gray-50 rounded-lg p-4 sm:p-6 border border-gray-200 group hover:shadow-lg transition-all duration-300"
      variants={staggerItem}
      whileHover={{
        y: -8,
        scale: 1.02,
        boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
        transition: { duration: 0.3, ease: "easeInOut" }
      }}
      whileTap={{ scale: 0.98 }}
    >
      <motion.h3 
        className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-via-primary transition-colors duration-200"
        whileHover={{
          scale: 1.05,
          transition: { duration: 0.2 }
        }}
      >
        {location.name}
      </motion.h3>
      
      <div className="space-y-2 mb-4">
        <motion.div 
          className="flex items-start"
          whileHover={{ x: 4, transition: { duration: 0.2 } }}
        >
          <MapPin className="h-5 w-5 text-via-primary mr-3 mt-0.5 flex-shrink-0" />
          <p className="text-gray-600">{location.address}</p>
        </motion.div>
        
        <motion.div 
          className="flex items-center"
          whileHover={{ x: 4, transition: { duration: 0.2 } }}
        >
          <Phone className="h-5 w-5 text-via-primary mr-3 flex-shrink-0" />
          <a 
            href={`tel:${location.phone}`}
            className="text-gray-600 hover:text-via-primary transition-colors duration-200"
          >
            {location.phone}
          </a>
        </motion.div>
        
        {location.hours && (
          <motion.div 
            className="flex items-center"
            whileHover={{ x: 4, transition: { duration: 0.2 } }}
          >
            <Calendar className="h-5 w-5 text-via-primary mr-3 flex-shrink-0" />
            <p className="text-gray-600 text-sm">{location.hours}</p>
          </motion.div>
        )}
      </div>
      
      <motion.div
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Button 
          variant="outline" 
          className="border-via-primary text-via-primary hover:bg-via-primary hover:text-white transition-all duration-200 w-full"
        >
          <Calendar className="mr-2 h-4 w-4" />
          Schedule Tour
        </Button>
      </motion.div>
    </motion.div>
  );
}

export function AnimatedLocationsSection({
  title,
  subtitle,
  locations,
  className
}: LocationSectionProps) {
  return (
    <motion.section
      className={cn("py-12 sm:py-16 lg:py-20 bg-white overflow-hidden", className)}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      variants={staggerContainer}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12 sm:mb-16"
          variants={staggerItem}
        >
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            {title}
          </h2>
          {subtitle && (
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
              {subtitle}
            </p>
          )}
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8"
          variants={staggerContainer}
        >
          {locations.map((location, index) => (
            <AnimatedLocationCard
              key={location.name}
              location={location}
              index={index}
            />
          ))}
        </motion.div>
      </div>
    </motion.section>
  );
}
