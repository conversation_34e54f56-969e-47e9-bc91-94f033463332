(()=>{var a={};a.id=379,a.ids=[379],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},941:(a,b,c)=>{"use strict";c.d(b,{LogoutButton:()=>d});let d=(0,c(90850).registerClientReference)(function(){throw Error("Attempted to call LogoutButton() from the server but LogoutButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\via\\components\\logout-button.tsx","LogoutButton")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9829:(a,b,c)=>{"use strict";c.d(b,{U:()=>f});var d=c(35135),e=c(7602);async function f(){let a=await (0,e.UL)();return(0,d.createServerClient)("https://hmlxgkfvjzpldqhpsoya.supabase.co","sb_publishable_sKUrOhdbUU32vkHDprfP3A_0FrYxKrO",{cookies:{getAll:()=>a.getAll(),setAll(b){try{b.forEach(({name:b,value:c,options:d})=>a.set(b,c,d))}catch{}}}})}},9949:(a,b,c)=>{"use strict";c.d(b,{ThemeSwitcher:()=>d});let d=(0,c(90850).registerClientReference)(function(){throw Error("Attempted to call ThemeSwitcher() from the server but ThemeSwitcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\via\\components\\theme-switcher.tsx","ThemeSwitcher")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},13129:(a,b,c)=>{"use strict";c.d(b,{$:()=>m});var d=c(31850),e=c(62003);function f(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}var g=function(a){let b=function(a){let b=e.forwardRef((a,b)=>{let{children:c,...d}=a;if(e.isValidElement(c)){var g;let a,h,i=(g=c,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(d,c.props);return c.type!==e.Fragment&&(j.ref=b?function(...a){return b=>{let c=!1,d=a.map(a=>{let d=f(a,b);return c||"function"!=typeof d||(c=!0),d});if(c)return()=>{for(let b=0;b<d.length;b++){let c=d[b];"function"==typeof c?c():f(a[b],null)}}}}(b,i):i),e.cloneElement(c,j)}return e.Children.count(c)>1?e.Children.only(null):null});return b.displayName=`${a}.SlotClone`,b}(a),c=e.forwardRef((a,c)=>{let{children:f,...g}=a,h=e.Children.toArray(f),j=h.find(i);if(j){let a=j.props.children,f=h.map(b=>b!==j?b:e.Children.count(a)>1?e.Children.only(null):e.isValidElement(a)?a.props.children:null);return(0,d.jsx)(b,{...g,ref:c,children:e.isValidElement(a)?e.cloneElement(a,void 0,f):null})}return(0,d.jsx)(b,{...g,ref:c,children:f})});return c.displayName=`${a}.Slot`,c}("Slot"),h=Symbol("radix.slottable");function i(a){return e.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===h}var j=c(76231),k=c(15292);let l=(0,j.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),m=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...f},h)=>(0,d.jsx)(e?g:"button",{className:(0,k.cn)(l({variant:b,size:c,className:a})),ref:h,...f}));m.displayName="Button"},15292:(a,b,c)=>{"use strict";c.d(b,{A:()=>g,cn:()=>f});var d=c(61601),e=c(20017);function f(...a){return(0,e.QP)((0,d.$)(a))}let g="sb_publishable_sKUrOhdbUU32vkHDprfP3A_0FrYxKrO"},15598:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>D.a,__next_app__:()=>J,handler:()=>L,pages:()=>I,routeModule:()=>K,tree:()=>H});var d=c(55546),e=c(24285),f=c(79587),g=c(51876),h=c(55326),i=c(65744),j=c(5743),k=c(60812),l=c(60082),m=c(67320),n=c(47410),o=c(94781),p=c(46669),q=c(81078),r=c(261),s=c(89205),t=c(74714),u=c(26713),v=c(89628),w=c(16765),x=c(3713),y=c(23702),z=c(51722),A=c(78094),B=c(86439),C=c(41970),D=c.n(C),E=c(95498),F=c(52003),G={};for(let a in E)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(G[a]=()=>E[a]);c.d(b,G);let H={children:["",{children:["protected",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,44532)),"C:\\Users\\<USER>\\Desktop\\via\\app\\protected\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,93789)),"C:\\Users\\<USER>\\Desktop\\via\\app\\protected\\layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,38729))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,77707))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,21557))).default(a)],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,78385)),"C:\\Users\\<USER>\\Desktop\\via\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,41970,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,68724,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,72157,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,93568,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,38729))).default(a)],apple:[],openGraph:[async a=>(await Promise.resolve().then(c.bind(c,77707))).default(a)],twitter:[async a=>(await Promise.resolve().then(c.bind(c,21557))).default(a)],manifest:void 0}}]}.children,I=["C:\\Users\\<USER>\\Desktop\\via\\app\\protected\\page.tsx"],J={require:c,loadChunk:()=>Promise.resolve()},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/protected/page",pathname:"/protected",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:H},distDir:".next",relativeProjectDir:""});async function L(a,b,d){var C;let G="/protected/page";"/index"===G&&(G="/");let M=(0,h.getRequestMeta)(a,"postponed"),N=(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:G,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==d.waitUntil||d.waitUntil.call(d,Promise.resolve()),null;let{buildId:P,query:Q,params:R,parsedUrl:S,pageIsDynamic:T,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,serverActionsManifest:X,clientReferenceManifest:Y,subresourceIntegrityManifest:Z,prerenderManifest:$,isDraftMode:_,resolvedPathname:aa,revalidateOnlyGenerated:ab,routerServerContext:ac,nextConfig:ad,interceptionRoutePatterns:ae}=O,af=S.pathname||"/",ag=(0,r.normalizeAppPath)(G),{isOnDemandRevalidate:ah}=O,ai=K.match(af,$),aj=!!$.routes[aa],ak=!!(ai||aj||$.routes[ag]),al=a.headers["user-agent"]||"",am=(0,u.getBotType)(al),an=(0,p.isHtmlBotRequest)(a),ao=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],ap=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],aq=(0,s.getIsPossibleServerAction)(a),ar=(0,m.checkIsAppPPREnabled)(ad.experimental.ppr)&&(null==(C=$.routes[ag]??$.dynamicRoutes[ag])?void 0:C.renderingMode)==="PARTIALLY_STATIC",as=!1,at=!1,au=ar?M:void 0,av=ar&&ap&&!ao,aw=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),ax=!al||(0,p.shouldServeStreamingMetadata)(al,ad.htmlLimitedBots);an&&ar&&(ak=!1,ax=!1);let ay=!0===K.isDev||!ak||"string"==typeof M||av,az=an&&ar,aA=null;_||!ak||ay||aq||au||av||(aA=aa);let aB=aA;!aB&&K.isDev&&(aB=aa),K.isDev||_||!ak||!ap||av||(0,k.d)(a.headers);let aC={...E,tree:H,pages:I,GlobalError:D(),handler:L,routeModule:K,__next_app__:J};X&&Y&&(0,o.setReferenceManifestsSingleton)({page:G,clientReferenceManifest:Y,serverActionsManifest:X,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:X})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan();try{let f=K.getVaryHeader(aa,ae);b.setHeader("Vary",f);let k=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aE.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aD} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${a.url}`)})},m=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:Q,params:R,page:ag,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.T)(aC),params:R,routeModule:K,page:G,postponed:f,shouldWaitOnAllReady:az,serveStreamingMetadata:ax,supportsDynamicResponse:"string"==typeof f||ay,buildManifest:U,nextFontManifest:V,reactLoadableManifest:W,subresourceIntegrityManifest:Z,serverActionsManifest:X,clientReferenceManifest:Y,setIsrStatus:null==ac?void 0:ac.setIsrStatus,dir:c(33873).join(process.cwd(),K.relativeProjectDir),isDraftMode:_,isRevalidate:ak&&!f&&!av,botType:am,isOnDemandRevalidate:ah,isPossibleServerAction:aq,assetPrefix:ad.assetPrefix,nextConfigOutput:ad.output,crossOrigin:ad.crossOrigin,trailingSlash:ad.trailingSlash,previewProps:$.preview,deploymentId:ad.deploymentId,enableTainting:ad.experimental.taint,htmlLimitedBots:ad.htmlLimitedBots,devtoolSegmentExplorer:ad.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ad.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ad.experimental.cacheLife,basePath:ad.basePath,serverActions:ad.experimental.serverActions,...as?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:as}:{},experimental:{isRoutePPREnabled:ar,expireTime:ad.expireTime,staleTimes:ad.experimental.staleTimes,cacheComponents:!!ad.experimental.cacheComponents,clientSegmentCache:!!ad.experimental.clientSegmentCache,clientParamParsing:!!ad.experimental.clientParamParsing,dynamicOnHover:!!ad.experimental.dynamicOnHover,inlineCss:!!ad.experimental.inlineCss,authInterrupts:!!ad.experimental.authInterrupts,clientTraceMetadata:ad.experimental.clientTraceMetadata||[]},waitUntil:d.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ac),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}},l=await k(e,i),{metadata:m}=l,{cacheControl:n,headers:o={},fetchTags:p}=m;if(p&&(o[y.NEXT_CACHE_TAGS_HEADER]=p),a.fetchMetrics=m.fetchMetrics,ak&&(null==n?void 0:n.revalidate)===0&&!K.isDev&&!ar){let a=m.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${aa}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:l,headers:o,rscData:m.flightData,postponed:m.postponed,status:m.statusCode,segmentData:m.segmentData},cacheControl:n}},o=async({hasResolved:c,previousCacheEntry:f,isRevalidating:g,span:i})=>{let j,k=!1===K.isDev,l=c||b.writableEnded;if(ah&&ab&&!f&&!N)return(null==ac?void 0:ac.render404)?await ac.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ai&&(j=(0,w.parseFallbackField)(ai.fallback)),j===w.FallbackMode.PRERENDER&&(0,u.isBot)(al)&&(!ar||an)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ah=!0),ah&&(j!==w.FallbackMode.NOT_FOUND||f)&&(j=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&j!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!l&&!_&&T&&(k||!aj)){let b;if((k||ai)&&j===w.FallbackMode.NOT_FOUND)throw new B.NoFallbackError;if(ar&&!ap){let c="string"==typeof(null==ai?void 0:ai.fallback)?ai.fallback:k?ag:null;if(b=await K.handleResponse({cacheKey:c,req:a,nextConfig:ad,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:$,isRoutePPREnabled:ar,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:k||at?(0,n.u)(ag):null}),waitUntil:d.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=ah||g||!au?void 0:au;if(as&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let p=T&&ar&&((0,h.getRequestMeta)(a,"renderFallbackShell")||at)?(0,n.u)(af):null;return m({span:i,postponed:o,fallbackRouteParams:p})},p=async c=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:c,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ah,isRoutePPREnabled:ar,req:a,nextConfig:ad,prerenderManifest:$,waitUntil:d.waitUntil});if(_&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;ak&&!av&&(!p||ao)&&(N||b.setHeader("x-nextjs-cache",ah?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(au)l={revalidate:0,expire:void 0};else if(N&&ap&&!ao&&ar)l={revalidate:0,expire:void 0};else if(!K.isDev)if(_)l={revalidate:0,expire:void 0};else if(ak){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ad.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof aw&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&ak&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(aw);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...n,value:{...n.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&au)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(N&&ak||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&ak&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||ap&&ar||(b.statusCode=q.status),!N&&q.status&&F.RedirectStatusCode[q.status]&&ap&&(b.statusCode=200),p&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),ap&&!_){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:q.html,cacheControl:av?{revalidate:0,expire:void 0}:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||ap)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:n.cacheControl});if(as)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:c,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ad.generateEtags,poweredByHeader:ad.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw aF||b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:G,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ak,isOnDemandRevalidate:ah})},ac),b}}},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25540:(a,b,c)=>{"use strict";c.d(b,{FetchDataSteps:()=>F});var d=c(92536),e=c(84041);c(50651);var f=c(11656),g=c(48067),h=c(76358),i=c(57713),j=c(40607),k=c(66244),l=c(90142),m="Checkbox",[n,o]=(0,g.A)(m),[p,q]=n(m);function r(a){let{__scopeCheckbox:b,checked:c,children:f,defaultChecked:g,disabled:h,form:j,name:k,onCheckedChange:l,required:n,value:o="on",internal_do_not_use_render:q}=a,[r,s]=(0,i.i)({prop:c,defaultProp:g??!1,onChange:l,caller:m}),[t,u]=e.useState(null),[v,w]=e.useState(null),x=e.useRef(!1),y=!t||!!j||!!t.closest("form"),A={checked:r,disabled:h,setChecked:s,control:t,setControl:u,name:k,form:j,value:o,hasConsumerStoppedPropagationRef:x,required:n,defaultChecked:!z(g)&&g,isFormControl:y,bubbleInput:v,setBubbleInput:w};return(0,d.jsx)(p,{scope:b,...A,children:"function"==typeof q?q(A):f})}var s="CheckboxTrigger",t=e.forwardRef(({__scopeCheckbox:a,onKeyDown:b,onClick:c,...g},i)=>{let{control:j,value:k,disabled:m,checked:n,required:o,setControl:p,setChecked:r,hasConsumerStoppedPropagationRef:t,isFormControl:u,bubbleInput:v}=q(s,a),w=(0,f.s)(i,p),x=e.useRef(n);return e.useEffect(()=>{let a=j?.form;if(a){let b=()=>r(x.current);return a.addEventListener("reset",b),()=>a.removeEventListener("reset",b)}},[j,r]),(0,d.jsx)(l.sG.button,{type:"button",role:"checkbox","aria-checked":z(n)?"mixed":n,"aria-required":o,"data-state":A(n),"data-disabled":m?"":void 0,disabled:m,value:k,...g,ref:w,onKeyDown:(0,h.mK)(b,a=>{"Enter"===a.key&&a.preventDefault()}),onClick:(0,h.mK)(c,a=>{r(a=>!!z(a)||!a),v&&u&&(t.current=a.isPropagationStopped(),t.current||a.stopPropagation())})})});t.displayName=s;var u=e.forwardRef((a,b)=>{let{__scopeCheckbox:c,name:e,checked:f,defaultChecked:g,required:h,disabled:i,value:j,onCheckedChange:k,form:l,...m}=a;return(0,d.jsx)(r,{__scopeCheckbox:c,checked:f,defaultChecked:g,disabled:i,required:h,onCheckedChange:k,name:e,form:l,value:j,internal_do_not_use_render:({isFormControl:a})=>(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(t,{...m,ref:b,__scopeCheckbox:c}),a&&(0,d.jsx)(y,{__scopeCheckbox:c})]})})});u.displayName=m;var v="CheckboxIndicator",w=e.forwardRef((a,b)=>{let{__scopeCheckbox:c,forceMount:e,...f}=a,g=q(v,c);return(0,d.jsx)(k.C,{present:e||z(g.checked)||!0===g.checked,children:(0,d.jsx)(l.sG.span,{"data-state":A(g.checked),"data-disabled":g.disabled?"":void 0,...f,ref:b,style:{pointerEvents:"none",...a.style}})})});w.displayName=v;var x="CheckboxBubbleInput",y=e.forwardRef(({__scopeCheckbox:a,...b},c)=>{let{control:g,hasConsumerStoppedPropagationRef:h,checked:i,defaultChecked:k,required:m,disabled:n,name:o,value:p,form:r,bubbleInput:s,setBubbleInput:t}=q(x,a),u=(0,f.s)(c,t),v=function(a){let b=e.useRef({value:a,previous:a});return e.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}(i),w=(0,j.X)(g);e.useEffect(()=>{if(!s)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,b=!h.current;if(v!==i&&a){let c=new Event("click",{bubbles:b});s.indeterminate=z(i),a.call(s,!z(i)&&i),s.dispatchEvent(c)}},[s,v,i,h]);let y=e.useRef(!z(i)&&i);return(0,d.jsx)(l.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:k??y.current,required:m,disabled:n,name:o,value:p,form:r,...b,tabIndex:-1,ref:u,style:{...b.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function z(a){return"indeterminate"===a}function A(a){return z(a)?"indeterminate":a?"checked":"unchecked"}y.displayName=x;var B=c(44634),C=c(21026);let D=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(u,{ref:c,className:(0,C.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...b,children:(0,d.jsx)(w,{className:(0,C.cn)("flex items-center justify-center text-current"),children:(0,d.jsx)(B.A,{className:"h-4 w-4"})})}));function E({title:a,children:b}){return(0,d.jsxs)("li",{className:"relative",children:[(0,d.jsx)(D,{id:a,name:a,className:"absolute top-[3px] mr-2 peer"}),(0,d.jsxs)("label",{htmlFor:a,className:"relative text-base text-foreground peer-checked:line-through font-medium",children:[(0,d.jsx)("span",{className:"ml-8",children:a}),(0,d.jsx)("div",{className:"ml-8 text-sm peer-checked:line-through font-normal text-muted-foreground",children:b})]})]})}function F(){let[a,b]=(0,e.useState)(null),[c,f]=(0,e.useState)(!0),[g,h]=(0,e.useState)(null);return c?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"}),(0,d.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading notes..."})]}):g?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsxs)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:[(0,d.jsx)("strong",{className:"font-bold",children:"Error:"}),(0,d.jsxs)("span",{className:"block sm:inline",children:[" ",g]})]}),(0,d.jsx)("p",{className:"text-gray-600",children:"Failed to fetch notes from the database."})]}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(E,{step:1,title:"Fetch Data from Supabase",description:"Retrieve data from your Supabase database using the client."}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Notes from Database"}),a&&a.length>0?(0,d.jsx)("div",{className:"space-y-4",children:a.map(a=>(0,d.jsxs)("div",{className:"bg-white p-4 rounded-lg border",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900",children:a.title}),(0,d.jsx)("p",{className:"text-gray-600 mt-2",children:a.content}),(0,d.jsxs)("p",{className:"text-sm text-gray-500 mt-2",children:["Created: ",new Date(a.created_at).toLocaleDateString()]})]},a.id))}):(0,d.jsx)("p",{className:"text-gray-500 text-center py-8",children:"No notes found. Create some notes first using the previous step."})]}),(0,d.jsx)(E,{step:2,title:"Display Data in UI",description:"Render the fetched data in your React components with proper loading and error states."}),(0,d.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Key Concepts"}),(0,d.jsxs)("ul",{className:"space-y-2 text-sm text-gray-700",children:[(0,d.jsxs)("li",{children:["• Use ",(0,d.jsx)("code",{className:"bg-gray-200 px-1 rounded",children:"useEffect"})," for data fetching"]}),(0,d.jsx)("li",{children:"• Implement loading states for better UX"}),(0,d.jsx)("li",{children:"• Handle errors gracefully with user-friendly messages"}),(0,d.jsx)("li",{children:"• Use TypeScript interfaces for type safety"}),(0,d.jsx)("li",{children:"• Order data by creation date for logical display"})]})]})]})}D.displayName=u.displayName},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33792:(a,b,c)=>{Promise.resolve().then(c.bind(c,25540))},33873:a=>{"use strict";a.exports=require("path")},39407:(a,b,c)=>{Promise.resolve().then(c.bind(c,941)),Promise.resolve().then(c.bind(c,9949)),Promise.resolve().then(c.t.bind(c,81169,23))},40357:(a,b,c)=>{"use strict";c.d(b,{ThemeSwitcher:()=>d_});var d,e,f,g=c(92536),h=c(15353),i=c(84041),j=c.t(i,2),k=c(76358),l=c(11656),m=c(48067),n=c(57713),o=c(90142),p=c(93071);function q(a){let b=a+"CollectionProvider",[c,d]=(0,m.A)(b),[e,f]=c(b,{collectionRef:{current:null},itemMap:new Map}),h=a=>{let{scope:b,children:c}=a,d=i.useRef(null),f=i.useRef(new Map).current;return(0,g.jsx)(e,{scope:b,itemMap:f,collectionRef:d,children:c})};h.displayName=b;let j=a+"CollectionSlot",k=(0,p.TL)(j),n=i.forwardRef((a,b)=>{let{scope:c,children:d}=a,e=f(j,c),h=(0,l.s)(b,e.collectionRef);return(0,g.jsx)(k,{ref:h,children:d})});n.displayName=j;let o=a+"CollectionItemSlot",q="data-radix-collection-item",r=(0,p.TL)(o),s=i.forwardRef((a,b)=>{let{scope:c,children:d,...e}=a,h=i.useRef(null),j=(0,l.s)(b,h),k=f(o,c);return i.useEffect(()=>(k.itemMap.set(h,{ref:h,...e}),()=>void k.itemMap.delete(h))),(0,g.jsx)(r,{...{[q]:""},ref:j,children:d})});return s.displayName=o,[{Provider:h,Slot:n,ItemSlot:s},function(b){let c=f(a+"CollectionConsumer",b);return i.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${q}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},d]}var r=new WeakMap;function s(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=t(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function t(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],r.set(this,!0)}set(a,b){return r.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=t(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],l=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{l||k[a-1]!==b||(l=!0);let c=k[l?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=s(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=s(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return s(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}});var u=i.createContext(void 0);function v(a){let b=i.useContext(u);return a||b||"ltr"}function w(a){let b=i.useRef(a);return i.useEffect(()=>{b.current=a}),i.useMemo(()=>(...a)=>b.current?.(...a),[])}var x="dismissableLayer.update",y=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),z=i.forwardRef((a,b)=>{let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:h,onInteractOutside:j,onDismiss:m,...n}=a,p=i.useContext(y),[q,r]=i.useState(null),s=q?.ownerDocument??globalThis?.document,[,t]=i.useState({}),u=(0,l.s)(b,a=>r(a)),v=Array.from(p.layers),[z]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),C=v.indexOf(z),D=q?v.indexOf(q):-1,E=p.layersWithOutsidePointerEventsDisabled.size>0,F=D>=C,G=function(a,b=globalThis?.document){let c=w(a),d=i.useRef(!1),e=i.useRef(()=>{});return i.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){B("dismissableLayer.pointerDownOutside",c,f,{discrete:!0})},f={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",e.current),e.current=d,b.addEventListener("click",e.current,{once:!0})):d()}else b.removeEventListener("click",e.current);d.current=!1},f=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(f),b.removeEventListener("pointerdown",a),b.removeEventListener("click",e.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...p.branches].some(a=>a.contains(b));F&&!c&&(f?.(a),j?.(a),a.defaultPrevented||m?.())},s),H=function(a,b=globalThis?.document){let c=w(a),d=i.useRef(!1);return i.useEffect(()=>{let a=a=>{a.target&&!d.current&&B("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...p.branches].some(a=>a.contains(b))&&(h?.(a),j?.(a),a.defaultPrevented||m?.())},s);return!function(a,b=globalThis?.document){let c=w(a);i.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{D===p.layers.size-1&&(d?.(a),!a.defaultPrevented&&m&&(a.preventDefault(),m()))},s),i.useEffect(()=>{if(q)return c&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(e=s.body.style.pointerEvents,s.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(q)),p.layers.add(q),A(),()=>{c&&1===p.layersWithOutsidePointerEventsDisabled.size&&(s.body.style.pointerEvents=e)}},[q,s,c,p]),i.useEffect(()=>()=>{q&&(p.layers.delete(q),p.layersWithOutsidePointerEventsDisabled.delete(q),A())},[q,p]),i.useEffect(()=>{let a=()=>t({});return document.addEventListener(x,a),()=>document.removeEventListener(x,a)},[]),(0,g.jsx)(o.sG.div,{...n,ref:u,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:(0,k.mK)(a.onFocusCapture,H.onFocusCapture),onBlurCapture:(0,k.mK)(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:(0,k.mK)(a.onPointerDownCapture,G.onPointerDownCapture)})});function A(){let a=new CustomEvent(x);document.dispatchEvent(a)}function B(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,o.hO)(e,f):e.dispatchEvent(f)}z.displayName="DismissableLayer",i.forwardRef((a,b)=>{let c=i.useContext(y),d=i.useRef(null),e=(0,l.s)(b,d);return i.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,g.jsx)(o.sG.div,{...a,ref:e})}).displayName="DismissableLayerBranch";var C=0;function D(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var E="focusScope.autoFocusOnMount",F="focusScope.autoFocusOnUnmount",G={bubbles:!1,cancelable:!0},H=i.forwardRef((a,b)=>{let{loop:c=!1,trapped:d=!1,onMountAutoFocus:e,onUnmountAutoFocus:f,...h}=a,[j,k]=i.useState(null),m=w(e),n=w(f),p=i.useRef(null),q=(0,l.s)(b,a=>k(a)),r=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(d){let a=function(a){if(r.paused||!j)return;let b=a.target;j.contains(b)?p.current=b:K(p.current,{select:!0})},b=function(a){if(r.paused||!j)return;let b=a.relatedTarget;null!==b&&(j.contains(b)||K(p.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&K(j)});return j&&c.observe(j,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[d,j,r.paused]),i.useEffect(()=>{if(j){L.add(r);let a=document.activeElement;if(!j.contains(a)){let b=new CustomEvent(E,G);j.addEventListener(E,m),j.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(K(d,{select:b}),document.activeElement!==c)return}(I(j).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&K(j))}return()=>{j.removeEventListener(E,m),setTimeout(()=>{let b=new CustomEvent(F,G);j.addEventListener(F,n),j.dispatchEvent(b),b.defaultPrevented||K(a??document.body,{select:!0}),j.removeEventListener(F,n),L.remove(r)},0)}}},[j,m,n,r]);let s=i.useCallback(a=>{if(!c&&!d||r.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,e=document.activeElement;if(b&&e){let b=a.currentTarget,[d,f]=function(a){let b=I(a);return[J(b,a),J(b.reverse(),a)]}(b);d&&f?a.shiftKey||e!==f?a.shiftKey&&e===d&&(a.preventDefault(),c&&K(f,{select:!0})):(a.preventDefault(),c&&K(d,{select:!0})):e===b&&a.preventDefault()}},[c,d,r.paused]);return(0,g.jsx)(o.sG.div,{tabIndex:-1,...h,ref:q,onKeyDown:s})});function I(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function J(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function K(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}H.displayName="FocusScope";var L=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=M(a,b)).unshift(b)},remove(b){a=M(a,b),a[0]?.resume()}}}();function M(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}var N=c(94844),O=j[" useId ".trim().toString()]||(()=>void 0),P=0;function Q(a){let[b,c]=i.useState(O());return(0,N.N)(()=>{a||c(a=>a??String(P++))},[a]),a||(b?`radix-${b}`:"")}let R=["top","right","bottom","left"],S=Math.min,T=Math.max,U=Math.round,V=Math.floor,W=a=>({x:a,y:a}),X={left:"right",right:"left",bottom:"top",top:"bottom"},Y={start:"end",end:"start"};function Z(a,b){return"function"==typeof a?a(b):a}function $(a){return a.split("-")[0]}function _(a){return a.split("-")[1]}function aa(a){return"x"===a?"y":"x"}function ab(a){return"y"===a?"height":"width"}let ac=new Set(["top","bottom"]);function ad(a){return ac.has($(a))?"y":"x"}function ae(a){return a.replace(/start|end/g,a=>Y[a])}let af=["left","right"],ag=["right","left"],ah=["top","bottom"],ai=["bottom","top"];function aj(a){return a.replace(/left|right|bottom|top/g,a=>X[a])}function ak(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function al(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function am(a,b,c){let d,{reference:e,floating:f}=a,g=ad(b),h=aa(ad(b)),i=ab(h),j=$(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,n=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(_(b)){case"start":d[h]-=n*(c&&k?-1:1);break;case"end":d[h]+=n*(c&&k?-1:1)}return d}let an=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=am(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=am(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function ao(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:m=!1,padding:n=0}=Z(b,a),o=ak(n),p=h[m?"floating"===l?"reference":"floating":l],q=al(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(p)))||c?p:p.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),r="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,s=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),t=await (null==f.isElement?void 0:f.isElement(s))&&await (null==f.getScale?void 0:f.getScale(s))||{x:1,y:1},u=al(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:r,offsetParent:s,strategy:i}):r);return{top:(q.top-u.top+o.top)/t.y,bottom:(u.bottom-q.bottom+o.bottom)/t.y,left:(q.left-u.left+o.left)/t.x,right:(u.right-q.right+o.right)/t.x}}function ap(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function aq(a){return R.some(b=>a[b]>=0)}let ar=new Set(["left","top"]);async function as(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=$(c),h=_(c),i="y"===ad(c),j=ar.has(g)?-1:1,k=f&&i?-1:1,l=Z(b,a),{mainAxis:m,crossAxis:n,alignmentAxis:o}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof o&&(n="end"===h?-1*o:o),i?{x:n*k,y:m*j}:{x:m*j,y:n*k}}function at(){return"undefined"!=typeof window}function au(a){return ax(a)?(a.nodeName||"").toLowerCase():"#document"}function av(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function aw(a){var b;return null==(b=(ax(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function ax(a){return!!at()&&(a instanceof Node||a instanceof av(a).Node)}function ay(a){return!!at()&&(a instanceof Element||a instanceof av(a).Element)}function az(a){return!!at()&&(a instanceof HTMLElement||a instanceof av(a).HTMLElement)}function aA(a){return!!at()&&"undefined"!=typeof ShadowRoot&&(a instanceof ShadowRoot||a instanceof av(a).ShadowRoot)}let aB=new Set(["inline","contents"]);function aC(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=aN(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!aB.has(e)}let aD=new Set(["table","td","th"]),aE=[":popover-open",":modal"];function aF(a){return aE.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let aG=["transform","translate","scale","rotate","perspective"],aH=["transform","translate","scale","rotate","perspective","filter"],aI=["paint","layout","strict","content"];function aJ(a){let b=aK(),c=ay(a)?aN(a):a;return aG.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||aH.some(a=>(c.willChange||"").includes(a))||aI.some(a=>(c.contain||"").includes(a))}function aK(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let aL=new Set(["html","body","#document"]);function aM(a){return aL.has(au(a))}function aN(a){return av(a).getComputedStyle(a)}function aO(a){return ay(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function aP(a){if("html"===au(a))return a;let b=a.assignedSlot||a.parentNode||aA(a)&&a.host||aw(a);return aA(b)?b.host:b}function aQ(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=aP(b);return aM(c)?b.ownerDocument?b.ownerDocument.body:b.body:az(c)&&aC(c)?c:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=av(e);if(f){let a=aR(g);return b.concat(g,g.visualViewport||[],aC(e)?e:[],a&&c?aQ(a):[])}return b.concat(e,aQ(e,[],c))}function aR(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function aS(a){let b=aN(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=az(a),f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,h=U(c)!==f||U(d)!==g;return h&&(c=f,d=g),{width:c,height:d,$:h}}function aT(a){return ay(a)?a:a.contextElement}function aU(a){let b=aT(a);if(!az(b))return W(1);let c=b.getBoundingClientRect(),{width:d,height:e,$:f}=aS(b),g=(f?U(c.width):c.width)/d,h=(f?U(c.height):c.height)/e;return g&&Number.isFinite(g)||(g=1),h&&Number.isFinite(h)||(h=1),{x:g,y:h}}let aV=W(0);function aW(a){let b=av(a);return aK()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:aV}function aX(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=aT(a),h=W(1);b&&(d?ay(d)&&(h=aU(d)):h=aU(a));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===av(g))&&e)?aW(g):W(0),j=(f.left+i.x)/h.x,k=(f.top+i.y)/h.y,l=f.width/h.x,m=f.height/h.y;if(g){let a=av(g),b=d&&ay(d)?av(d):d,c=a,e=aR(c);for(;e&&d&&b!==c;){let a=aU(e),b=e.getBoundingClientRect(),d=aN(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;j*=a.x,k*=a.y,l*=a.x,m*=a.y,j+=f,k+=g,e=aR(c=av(e))}}return al({width:l,height:m,x:j,y:k})}function aY(a,b){let c=aO(a).scrollLeft;return b?b.left+c:aX(aw(a)).left+c}function aZ(a,b){let c=a.getBoundingClientRect();return{x:c.left+b.scrollLeft-aY(a,c),y:c.top+b.scrollTop}}let a$=new Set(["absolute","fixed"]);function a_(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=av(a),d=aw(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=aK();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}let j=aY(d);if(j<=0){let a=d.ownerDocument,b=a.body,c=getComputedStyle(b),e="CSS1Compat"===a.compatMode&&parseFloat(c.marginLeft)+parseFloat(c.marginRight)||0,g=Math.abs(d.clientWidth-b.clientWidth-e);g<=25&&(f-=g)}else j<=25&&(f+=j);return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=aw(a),c=aO(a),d=a.ownerDocument.body,e=T(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=T(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),g=-c.scrollLeft+aY(a),h=-c.scrollTop;return"rtl"===aN(d).direction&&(g+=T(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:g,y:h}}(aw(a));else if(ay(b))d=function(a,b){let c=aX(a,!0,"fixed"===b),d=c.top+a.clientTop,e=c.left+a.clientLeft,f=az(a)?aU(a):W(1),g=a.clientWidth*f.x,h=a.clientHeight*f.y;return{width:g,height:h,x:e*f.x,y:d*f.y}}(b,c);else{let c=aW(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}return al(d)}function a0(a){return"static"===aN(a).position}function a1(a,b){if(!az(a)||"fixed"===aN(a).position)return null;if(b)return b(a);let c=a.offsetParent;return aw(a)===c&&(c=c.ownerDocument.body),c}function a2(a,b){var c;let d=av(a);if(aF(a))return d;if(!az(a)){let b=aP(a);for(;b&&!aM(b);){if(ay(b)&&!a0(b))return b;b=aP(b)}return d}let e=a1(a,b);for(;e&&(c=e,aD.has(au(c)))&&a0(e);)e=a1(e,b);return e&&aM(e)&&a0(e)&&!aJ(e)?d:e||function(a){let b=aP(a);for(;az(b)&&!aM(b);){if(aJ(b))return b;if(aF(b))break;b=aP(b)}return null}(a)||d}let a3=async function(a){let b=this.getOffsetParent||a2,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){let d=az(b),e=aw(b),f="fixed"===c,g=aX(a,!0,f,b),h={scrollLeft:0,scrollTop:0},i=W(0);if(d||!d&&!f)if(("body"!==au(b)||aC(e))&&(h=aO(b)),d){let a=aX(b,!0,f,b);i.x=a.x+b.clientLeft,i.y=a.y+b.clientTop}else e&&(i.x=aY(e));f&&!d&&e&&(i.x=aY(e));let j=!e||d||f?W(0):aZ(e,h);return{x:g.left+h.scrollLeft-i.x-j.x,y:g.top+h.scrollTop-i.y-j.y,width:g.width,height:g.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},a4={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let{elements:b,rect:c,offsetParent:d,strategy:e}=a,f="fixed"===e,g=aw(d),h=!!b&&aF(b.floating);if(d===g||h&&f)return c;let i={scrollLeft:0,scrollTop:0},j=W(1),k=W(0),l=az(d);if((l||!l&&!f)&&(("body"!==au(d)||aC(g))&&(i=aO(d)),az(d))){let a=aX(d);j=aU(d),k.x=a.x+d.clientLeft,k.y=a.y+d.clientTop}let m=!g||l||f?W(0):aZ(g,i);return{width:c.width*j.x,height:c.height*j.y,x:c.x*j.x-i.scrollLeft*j.x+k.x+m.x,y:c.y*j.y-i.scrollTop*j.y+k.y+m.y}},getDocumentElement:aw,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,f=[..."clippingAncestors"===c?aF(b)?[]:function(a,b){let c=b.get(a);if(c)return c;let d=aQ(a,[],!1).filter(a=>ay(a)&&"body"!==au(a)),e=null,f="fixed"===aN(a).position,g=f?aP(a):a;for(;ay(g)&&!aM(g);){let b=aN(g),c=aJ(g);c||"fixed"!==b.position||(e=null),(f?!c&&!e:!c&&"static"===b.position&&!!e&&a$.has(e.position)||aC(g)&&!c&&function a(b,c){let d=aP(b);return!(d===c||!ay(d)||aM(d))&&("fixed"===aN(d).position||a(d,c))}(a,g))?d=d.filter(a=>a!==g):e=b,g=aP(g)}return b.set(a,d),d}(b,this._c):[].concat(c),d],g=f[0],h=f.reduce((a,c)=>{let d=a_(b,c,e);return a.top=T(d.top,a.top),a.right=S(d.right,a.right),a.bottom=S(d.bottom,a.bottom),a.left=T(d.left,a.left),a},a_(b,g,e));return{width:h.right-h.left,height:h.bottom-h.top,x:h.left,y:h.top}},getOffsetParent:a2,getElementRects:a3,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=aS(a);return{width:b,height:c}},getScale:aU,isElement:ay,isRTL:function(a){return"rtl"===aN(a).direction}};function a5(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let a6=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:f,platform:g,elements:h,middlewareData:i}=b,{element:j,padding:k=0}=Z(a,b)||{};if(null==j)return{};let l=ak(k),m={x:c,y:d},n=aa(ad(e)),o=ab(n),p=await g.getDimensions(j),q="y"===n,r=q?"clientHeight":"clientWidth",s=f.reference[o]+f.reference[n]-m[n]-f.floating[o],t=m[n]-f.reference[n],u=await (null==g.getOffsetParent?void 0:g.getOffsetParent(j)),v=u?u[r]:0;v&&await (null==g.isElement?void 0:g.isElement(u))||(v=h.floating[r]||f.floating[o]);let w=v/2-p[o]/2-1,x=S(l[q?"top":"left"],w),y=S(l[q?"bottom":"right"],w),z=v-p[o]-y,A=v/2-p[o]/2+(s/2-t/2),B=T(x,S(A,z)),C=!i.arrow&&null!=_(e)&&A!==B&&f.reference[o]/2-(A<x?x:y)-p[o]/2<0,D=C?A<x?A-x:A-z:0;return{[n]:m[n]+D,data:{[n]:B,centerOffset:A-B-D,...C&&{alignmentOffset:D}},reset:C}}});var a7=c(15436),a8="undefined"!=typeof document?i.useLayoutEffect:function(){};function a9(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!a9(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!a9(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function ba(a){return"undefined"==typeof window?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function bb(a,b){let c=ba(a);return Math.round(b*c)/c}function bc(a){let b=i.useRef(a);return a8(()=>{b.current=a}),b}var bd=i.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,g.jsx)(o.sG.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,g.jsx)("polygon",{points:"0,0 30,0 15,10"})})});bd.displayName="Arrow";var be=c(40607),bf="Popper",[bg,bh]=(0,m.A)(bf),[bi,bj]=bg(bf),bk=a=>{let{__scopePopper:b,children:c}=a,[d,e]=i.useState(null);return(0,g.jsx)(bi,{scope:b,anchor:d,onAnchorChange:e,children:c})};bk.displayName=bf;var bl="PopperAnchor",bm=i.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:d,...e}=a,f=bj(bl,c),h=i.useRef(null),j=(0,l.s)(b,h),k=i.useRef(null);return i.useEffect(()=>{let a=k.current;k.current=d?.current||h.current,a!==k.current&&f.onAnchorChange(k.current)}),d?null:(0,g.jsx)(o.sG.div,{...e,ref:j})});bm.displayName=bl;var bn="PopperContent",[bo,bp]=bg(bn),bq=i.forwardRef((a,b)=>{let{__scopePopper:c,side:d="bottom",sideOffset:e=0,align:f="center",alignOffset:h=0,arrowPadding:j=0,avoidCollisions:k=!0,collisionBoundary:m=[],collisionPadding:n=0,sticky:p="partial",hideWhenDetached:q=!1,updatePositionStrategy:r="optimized",onPlaced:s,...t}=a,u=bj(bn,c),[v,x]=i.useState(null),y=(0,l.s)(b,a=>x(a)),[z,A]=i.useState(null),B=(0,be.X)(z),C=B?.width??0,D=B?.height??0,E="number"==typeof n?n:{top:0,right:0,bottom:0,left:0,...n},F=Array.isArray(m)?m:[m],G=F.length>0,H={padding:E,boundary:F.filter(bu),altBoundary:G},{refs:I,floatingStyles:J,placement:K,isPositioned:L,middlewareData:M}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:d=[],platform:e,elements:{reference:f,floating:g}={},transform:h=!0,whileElementsMounted:j,open:k}=a,[l,m]=i.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[n,o]=i.useState(d);a9(n,d)||o(d);let[p,q]=i.useState(null),[r,s]=i.useState(null),t=i.useCallback(a=>{a!==x.current&&(x.current=a,q(a))},[]),u=i.useCallback(a=>{a!==y.current&&(y.current=a,s(a))},[]),v=f||p,w=g||r,x=i.useRef(null),y=i.useRef(null),z=i.useRef(l),A=null!=j,B=bc(j),C=bc(e),D=bc(k),E=i.useCallback(()=>{if(!x.current||!y.current)return;let a={placement:b,strategy:c,middleware:n};C.current&&(a.platform=C.current),((a,b,c)=>{let d=new Map,e={platform:a4,...c},f={...e.platform,_c:d};return an(a,b,{...e,platform:f})})(x.current,y.current,a).then(a=>{let b={...a,isPositioned:!1!==D.current};F.current&&!a9(z.current,b)&&(z.current=b,a7.flushSync(()=>{m(b)}))})},[n,b,c,C,D]);a8(()=>{!1===k&&z.current.isPositioned&&(z.current.isPositioned=!1,m(a=>({...a,isPositioned:!1})))},[k]);let F=i.useRef(!1);a8(()=>(F.current=!0,()=>{F.current=!1}),[]),a8(()=>{if(v&&(x.current=v),w&&(y.current=w),v&&w){if(B.current)return B.current(v,w,E);E()}},[v,w,E,B,A]);let G=i.useMemo(()=>({reference:x,floating:y,setReference:t,setFloating:u}),[t,u]),H=i.useMemo(()=>({reference:v,floating:w}),[v,w]),I=i.useMemo(()=>{let a={position:c,left:0,top:0};if(!H.floating)return a;let b=bb(H.floating,l.x),d=bb(H.floating,l.y);return h?{...a,transform:"translate("+b+"px, "+d+"px)",...ba(H.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:b,top:d}},[c,h,H.floating,l.x,l.y]);return i.useMemo(()=>({...l,update:E,refs:G,elements:H,floatingStyles:I}),[l,E,G,H,I])}({strategy:"fixed",placement:d+("center"!==f?"-"+f:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:j=!1}=d,k=aT(a),l=f||g?[...k?aQ(k):[],...aQ(b)]:[];l.forEach(a=>{f&&a.addEventListener("scroll",c,{passive:!0}),g&&a.addEventListener("resize",c)});let m=k&&i?function(a,b){let c,d=null,e=aw(a);function f(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function g(h,i){void 0===h&&(h=!1),void 0===i&&(i=1),f();let j=a.getBoundingClientRect(),{left:k,top:l,width:m,height:n}=j;if(h||b(),!m||!n)return;let o=V(l),p=V(e.clientWidth-(k+m)),q={rootMargin:-o+"px "+-p+"px "+-V(e.clientHeight-(l+n))+"px "+-V(k)+"px",threshold:T(0,S(1,i))||1},r=!0;function s(b){let d=b[0].intersectionRatio;if(d!==i){if(!r)return g();d?g(!1,d):c=setTimeout(()=>{g(!1,1e-7)},1e3)}1!==d||a5(j,a.getBoundingClientRect())||g(),r=!1}try{d=new IntersectionObserver(s,{...q,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(s,q)}d.observe(a)}(!0),f}(k,c):null,n=-1,o=null;h&&(o=new ResizeObserver(a=>{let[d]=a;d&&d.target===k&&o&&(o.unobserve(b),cancelAnimationFrame(n),n=requestAnimationFrame(()=>{var a;null==(a=o)||a.observe(b)})),c()}),k&&!j&&o.observe(k),o.observe(b));let p=j?aX(a):null;return j&&function b(){let d=aX(a);p&&!a5(p,d)&&c(),p=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;l.forEach(a=>{f&&a.removeEventListener("scroll",c),g&&a.removeEventListener("resize",c)}),null==m||m(),null==(a=o)||a.disconnect(),o=null,j&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===r}),elements:{reference:u.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await as(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:e+D,alignmentAxis:h}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...i}=Z(a,b),j={x:c,y:d},k=await ao(b,i),l=ad($(e)),m=aa(l),n=j[m],o=j[l];if(f){let a="y"===m?"top":"left",b="y"===m?"bottom":"right",c=n+k[a],d=n-k[b];n=T(c,S(n,d))}if(g){let a="y"===l?"top":"left",b="y"===l?"bottom":"right",c=o+k[a],d=o-k[b];o=T(c,S(o,d))}let p=h.fn({...b,[m]:n,[l]:o});return{...p,data:{x:p.x-c,y:p.y-d,enabled:{[m]:f,[l]:g}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===p?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=Z(a,b),k={x:c,y:d},l=ad(e),m=aa(l),n=k[m],o=k[l],p=Z(h,b),q="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(i){let a="y"===m?"height":"width",b=f.reference[m]-f.floating[a]+q.mainAxis,c=f.reference[m]+f.reference[a]-q.mainAxis;n<b?n=b:n>c&&(n=c)}if(j){var r,s;let a="y"===m?"width":"height",b=ar.has($(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(r=g.offset)?void 0:r[l])||0)+(b?0:q.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(s=g.offset)?void 0:s[l])||0)-(b?q.crossAxis:0);o<c?o=c:o>d&&(o=d)}return{[m]:n,[l]:o}}}}(a),options:[a,b]}))():void 0,...H}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:m}=b,{mainAxis:n=!0,crossAxis:o=!0,fallbackPlacements:p,fallbackStrategy:q="bestFit",fallbackAxisSideDirection:r="none",flipAlignment:s=!0,...t}=Z(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let u=$(h),v=ad(k),w=$(k)===k,x=await (null==l.isRTL?void 0:l.isRTL(m.floating)),y=p||(w||!s?[aj(k)]:function(a){let b=aj(a);return[ae(a),b,ae(b)]}(k)),z="none"!==r;!p&&z&&y.push(...function(a,b,c,d){let e=_(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?ag:af;return b?af:ag;case"left":case"right":return b?ah:ai;default:return[]}}($(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(ae)))),f}(k,s,r,x));let A=[k,...y],B=await ao(b,t),C=[],D=(null==(d=i.flip)?void 0:d.overflows)||[];if(n&&C.push(B[u]),o){let a=function(a,b,c){void 0===c&&(c=!1);let d=_(a),e=aa(ad(a)),f=ab(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=aj(g)),[g,aj(g)]}(h,j,x);C.push(B[a[0]],B[a[1]])}if(D=[...D,{placement:h,overflows:C}],!C.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=A[a];if(b&&("alignment"!==o||v===ad(b)||D.every(a=>ad(a.placement)!==v||a.overflows[0]>0)))return{data:{index:a,overflows:D},reset:{placement:b}};let c=null==(f=D.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(q){case"bestFit":{let a=null==(g=D.filter(a=>{if(z){let b=ad(a.placement);return b===v||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({...H}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,f,{placement:g,rects:h,platform:i,elements:j}=b,{apply:k=()=>{},...l}=Z(a,b),m=await ao(b,l),n=$(g),o=_(g),p="y"===ad(g),{width:q,height:r}=h.floating;"top"===n||"bottom"===n?(e=n,f=o===(await (null==i.isRTL?void 0:i.isRTL(j.floating))?"start":"end")?"left":"right"):(f=n,e="end"===o?"top":"bottom");let s=r-m.top-m.bottom,t=q-m.left-m.right,u=S(r-m[e],s),v=S(q-m[f],t),w=!b.middlewareData.shift,x=u,y=v;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(y=t),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(x=s),w&&!o){let a=T(m.left,0),b=T(m.right,0),c=T(m.top,0),d=T(m.bottom,0);p?y=q-2*(0!==a||0!==b?a+b:T(m.left,m.right)):x=r-2*(0!==c||0!==d?c+d:T(m.top,m.bottom))}await k({...b,availableWidth:y,availableHeight:x});let z=await i.getDimensions(j.floating);return q!==z.width||r!==z.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({...H,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),z&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?a6({element:c.current,padding:d}).fn(b):{}:c?a6({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:z,padding:j}),bv({arrowWidth:C,arrowHeight:D}),q&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=Z(a,b);switch(d){case"referenceHidden":{let a=ap(await ao(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:aq(a)}}}case"escaped":{let a=ap(await ao(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:aq(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",...H})]}),[O,P]=bw(K),Q=w(s);(0,N.N)(()=>{L&&Q?.()},[L,Q]);let R=M.arrow?.x,U=M.arrow?.y,W=M.arrow?.centerOffset!==0,[X,Y]=i.useState();return(0,N.N)(()=>{v&&Y(window.getComputedStyle(v).zIndex)},[v]),(0,g.jsx)("div",{ref:I.setFloating,"data-radix-popper-content-wrapper":"",style:{...J,transform:L?J.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[M.transformOrigin?.x,M.transformOrigin?.y].join(" "),...M.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,g.jsx)(bo,{scope:c,placedSide:O,onArrowChange:A,arrowX:R,arrowY:U,shouldHideArrow:W,children:(0,g.jsx)(o.sG.div,{"data-side":O,"data-align":P,...t,ref:y,style:{...t.style,animation:L?void 0:"none"}})})})});bq.displayName=bn;var br="PopperArrow",bs={top:"bottom",right:"left",bottom:"top",left:"right"},bt=i.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=bp(br,c),f=bs[e.placedSide];return(0,g.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,g.jsx)(bd,{...d,ref:b,style:{...d.style,display:"block"}})})});function bu(a){return null!==a}bt.displayName=br;var bv=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=bw(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function bw(a){let[b,c="center"]=a.split("-");return[b,c]}var bx=i.forwardRef((a,b)=>{let{container:c,...d}=a,[e,f]=i.useState(!1);(0,N.N)(()=>f(!0),[]);let h=c||e&&globalThis?.document?.body;return h?a7.createPortal((0,g.jsx)(o.sG.div,{...d,ref:b}),h):null});bx.displayName="Portal";var by=c(66244),bz="rovingFocusGroup.onEntryFocus",bA={bubbles:!1,cancelable:!0},bB="RovingFocusGroup",[bC,bD,bE]=q(bB),[bF,bG]=(0,m.A)(bB,[bE]),[bH,bI]=bF(bB),bJ=i.forwardRef((a,b)=>(0,g.jsx)(bC.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,g.jsx)(bC.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,g.jsx)(bK,{...a,ref:b})})}));bJ.displayName=bB;var bK=i.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:d,loop:e=!1,dir:f,currentTabStopId:h,defaultCurrentTabStopId:j,onCurrentTabStopIdChange:m,onEntryFocus:p,preventScrollOnEntryFocus:q=!1,...r}=a,s=i.useRef(null),t=(0,l.s)(b,s),u=v(f),[x,y]=(0,n.i)({prop:h,defaultProp:j??null,onChange:m,caller:bB}),[z,A]=i.useState(!1),B=w(p),C=bD(c),D=i.useRef(!1),[E,F]=i.useState(0);return i.useEffect(()=>{let a=s.current;if(a)return a.addEventListener(bz,B),()=>a.removeEventListener(bz,B)},[B]),(0,g.jsx)(bH,{scope:c,orientation:d,dir:u,loop:e,currentTabStopId:x,onItemFocus:i.useCallback(a=>y(a),[y]),onItemShiftTab:i.useCallback(()=>A(!0),[]),onFocusableItemAdd:i.useCallback(()=>F(a=>a+1),[]),onFocusableItemRemove:i.useCallback(()=>F(a=>a-1),[]),children:(0,g.jsx)(o.sG.div,{tabIndex:z||0===E?-1:0,"data-orientation":d,...r,ref:t,style:{outline:"none",...a.style},onMouseDown:(0,k.mK)(a.onMouseDown,()=>{D.current=!0}),onFocus:(0,k.mK)(a.onFocus,a=>{let b=!D.current;if(a.target===a.currentTarget&&b&&!z){let b=new CustomEvent(bz,bA);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=C().filter(a=>a.focusable);bO([a.find(a=>a.active),a.find(a=>a.id===x),...a].filter(Boolean).map(a=>a.ref.current),q)}}D.current=!1}),onBlur:(0,k.mK)(a.onBlur,()=>A(!1))})})}),bL="RovingFocusGroupItem",bM=i.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:d=!0,active:e=!1,tabStopId:f,children:h,...j}=a,l=Q(),m=f||l,n=bI(bL,c),p=n.currentTabStopId===m,q=bD(c),{onFocusableItemAdd:r,onFocusableItemRemove:s,currentTabStopId:t}=n;return i.useEffect(()=>{if(d)return r(),()=>s()},[d,r,s]),(0,g.jsx)(bC.ItemSlot,{scope:c,id:m,focusable:d,active:e,children:(0,g.jsx)(o.sG.span,{tabIndex:p?0:-1,"data-orientation":n.orientation,...j,ref:b,onMouseDown:(0,k.mK)(a.onMouseDown,a=>{d?n.onItemFocus(m):a.preventDefault()}),onFocus:(0,k.mK)(a.onFocus,()=>n.onItemFocus(m)),onKeyDown:(0,k.mK)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void n.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return bN[e]}(a,n.orientation,n.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=q().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=n.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>bO(c))}}),children:"function"==typeof h?h({isCurrentTabStop:p,hasTabStop:null!=t}):h})})});bM.displayName=bL;var bN={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function bO(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var bP=new WeakMap,bQ=new WeakMap,bR={},bS=0,bT=function(a){return a&&(a.host||bT(a.parentNode))},bU=function(a,b,c,d){var e=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=bT(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});bR[c]||(bR[c]=new WeakMap);var f=bR[c],g=[],h=new Set,i=new Set(e),j=function(a){!a||h.has(a)||(h.add(a),j(a.parentNode))};e.forEach(j);var k=function(a){!a||i.has(a)||Array.prototype.forEach.call(a.children,function(a){if(h.has(a))k(a);else try{var b=a.getAttribute(d),e=null!==b&&"false"!==b,i=(bP.get(a)||0)+1,j=(f.get(a)||0)+1;bP.set(a,i),f.set(a,j),g.push(a),1===i&&e&&bQ.set(a,!0),1===j&&a.setAttribute(c,"true"),e||a.setAttribute(d,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return k(b),h.clear(),bS++,function(){g.forEach(function(a){var b=bP.get(a)-1,e=f.get(a)-1;bP.set(a,b),f.set(a,e),b||(bQ.has(a)||a.removeAttribute(d),bQ.delete(a)),e||a.removeAttribute(c)}),--bS||(bP=new WeakMap,bP=new WeakMap,bQ=new WeakMap,bR={})}},bV=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),bU(d,e,c,"aria-hidden")):function(){return null}},bW=function(){return(bW=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function bX(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var bY=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),bZ="width-before-scroll-bar";function b$(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var b_="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,b0=new WeakMap;function b1(a){return a}var b2=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=b1),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=bW({async:!0,ssr:!1},a),e}(),b3=function(){},b4=i.forwardRef(function(a,b){var c,d,e,f,g=i.useRef(null),h=i.useState({onScrollCapture:b3,onWheelCapture:b3,onTouchMoveCapture:b3}),j=h[0],k=h[1],l=a.forwardProps,m=a.children,n=a.className,o=a.removeScrollBar,p=a.enabled,q=a.shards,r=a.sideCar,s=a.noRelative,t=a.noIsolation,u=a.inert,v=a.allowPinchZoom,w=a.as,x=a.gapMode,y=bX(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=(c=[g,b],d=function(a){return c.forEach(function(b){return b$(b,a)})},(e=(0,i.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,f=e.facade,b_(function(){var a=b0.get(f);if(a){var b=new Set(a),d=new Set(c),e=f.current;b.forEach(function(a){d.has(a)||b$(a,null)}),d.forEach(function(a){b.has(a)||b$(a,e)})}b0.set(f,c)},[c]),f),A=bW(bW({},y),j);return i.createElement(i.Fragment,null,p&&i.createElement(r,{sideCar:b2,removeScrollBar:o,shards:q,noRelative:s,noIsolation:t,inert:u,setCallbacks:k,allowPinchZoom:!!v,lockRef:g,gapMode:x}),l?i.cloneElement(i.Children.only(m),bW(bW({},A),{ref:z})):i.createElement(void 0===w?"div":w,bW({},A,{className:n,ref:z}),m))});b4.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},b4.classNames={fullWidth:bZ,zeroRight:bY};var b5=function(a){var b=a.sideCar,c=bX(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return i.createElement(d,bW({},c))};b5.isSideCarExport=!0;var b6=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=f||c.nc;return b&&a.setAttribute("nonce",b),a}())){var e,g;(e=b).styleSheet?e.styleSheet.cssText=d:e.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},b7=function(){var a=b6();return function(b,c){i.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},b8=function(){var a=b7();return function(b){return a(b.styles,b.dynamic),null}},b9={left:0,top:0,right:0,gap:0},ca=function(a){return parseInt(a||"",10)||0},cb=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[ca(c),ca(d),ca(e)]},cc=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return b9;var b=cb(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},cd=b8(),ce="data-scroll-locked",cf=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(ce,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(bY," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(bZ," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(bY," .").concat(bY," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(bZ," .").concat(bZ," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(ce,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},cg=function(){var a=parseInt(document.body.getAttribute(ce)||"0",10);return isFinite(a)?a:0},ch=function(){i.useEffect(function(){return document.body.setAttribute(ce,(cg()+1).toString()),function(){var a=cg()-1;a<=0?document.body.removeAttribute(ce):document.body.setAttribute(ce,a.toString())}},[])},ci=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;ch();var f=i.useMemo(function(){return cc(e)},[e]);return i.createElement(cd,{styles:cf(f,!b,e,c?"":"!important")})},cj=!1;if("undefined"!=typeof window)try{var ck=Object.defineProperty({},"passive",{get:function(){return cj=!0,!0}});window.addEventListener("test",ck,ck),window.removeEventListener("test",ck,ck)}catch(a){cj=!1}var cl=!!cj&&{passive:!1},cm=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},cn=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),co(a,d)){var e=cp(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},co=function(a,b){return"v"===a?cm(b,"overflowY"):cm(b,"overflowX")},cp=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},cq=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=cp(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&co(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},cr=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},cs=function(a){return[a.deltaX,a.deltaY]},ct=function(a){return a&&"current"in a?a.current:a},cu=0,cv=[];let cw=(d=function(a){var b=i.useRef([]),c=i.useRef([0,0]),d=i.useRef(),e=i.useState(cu++)[0],f=i.useState(b8)[0],g=i.useRef(a);i.useEffect(function(){g.current=a},[a]),i.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(ct),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var h=i.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!g.current.allowPinchZoom;var e,f=cr(a),h=c.current,i="deltaX"in a?a.deltaX:h[0]-f[0],j="deltaY"in a?a.deltaY:h[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=cn(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=cn(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return cq(n,b,a,"h"===n?i:j,!0)},[]),j=i.useCallback(function(a){if(cv.length&&cv[cv.length-1]===f){var c="deltaY"in a?cs(a):cr(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(g.current.shards||[]).map(ct).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?h(a,e[0]):!g.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=i.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=i.useCallback(function(a){c.current=cr(a),d.current=void 0},[]),m=i.useCallback(function(b){k(b.type,cs(b),b.target,h(b,a.lockRef.current))},[]),n=i.useCallback(function(b){k(b.type,cr(b),b.target,h(b,a.lockRef.current))},[]);i.useEffect(function(){return cv.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,cl),document.addEventListener("touchmove",j,cl),document.addEventListener("touchstart",l,cl),function(){cv=cv.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,cl),document.removeEventListener("touchmove",j,cl),document.removeEventListener("touchstart",l,cl)}},[]);var o=a.removeScrollBar,p=a.inert;return i.createElement(i.Fragment,null,p?i.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?i.createElement(ci,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},b2.useMedium(d),b5);var cx=i.forwardRef(function(a,b){return i.createElement(b4,bW({},a,{ref:b,sideCar:cw}))});cx.classNames=b4.classNames;var cy=["Enter"," "],cz=["ArrowUp","PageDown","End"],cA=["ArrowDown","PageUp","Home",...cz],cB={ltr:[...cy,"ArrowRight"],rtl:[...cy,"ArrowLeft"]},cC={ltr:["ArrowLeft"],rtl:["ArrowRight"]},cD="Menu",[cE,cF,cG]=q(cD),[cH,cI]=(0,m.A)(cD,[cG,bh,bG]),cJ=bh(),cK=bG(),[cL,cM]=cH(cD),[cN,cO]=cH(cD),cP=a=>{let{__scopeMenu:b,open:c=!1,children:d,dir:e,onOpenChange:f,modal:h=!0}=a,j=cJ(b),[k,l]=i.useState(null),m=i.useRef(!1),n=w(f),o=v(e);return i.useEffect(()=>{let a=()=>{m.current=!0,document.addEventListener("pointerdown",b,{capture:!0,once:!0}),document.addEventListener("pointermove",b,{capture:!0,once:!0})},b=()=>m.current=!1;return document.addEventListener("keydown",a,{capture:!0}),()=>{document.removeEventListener("keydown",a,{capture:!0}),document.removeEventListener("pointerdown",b,{capture:!0}),document.removeEventListener("pointermove",b,{capture:!0})}},[]),(0,g.jsx)(bk,{...j,children:(0,g.jsx)(cL,{scope:b,open:c,onOpenChange:n,content:k,onContentChange:l,children:(0,g.jsx)(cN,{scope:b,onClose:i.useCallback(()=>n(!1),[n]),isUsingKeyboardRef:m,dir:o,modal:h,children:d})})})};cP.displayName=cD;var cQ=i.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=cJ(c);return(0,g.jsx)(bm,{...e,...d,ref:b})});cQ.displayName="MenuAnchor";var cR="MenuPortal",[cS,cT]=cH(cR,{forceMount:void 0}),cU=a=>{let{__scopeMenu:b,forceMount:c,children:d,container:e}=a,f=cM(cR,b);return(0,g.jsx)(cS,{scope:b,forceMount:c,children:(0,g.jsx)(by.C,{present:c||f.open,children:(0,g.jsx)(bx,{asChild:!0,container:e,children:d})})})};cU.displayName=cR;var cV="MenuContent",[cW,cX]=cH(cV),cY=i.forwardRef((a,b)=>{let c=cT(cV,a.__scopeMenu),{forceMount:d=c.forceMount,...e}=a,f=cM(cV,a.__scopeMenu),h=cO(cV,a.__scopeMenu);return(0,g.jsx)(cE.Provider,{scope:a.__scopeMenu,children:(0,g.jsx)(by.C,{present:d||f.open,children:(0,g.jsx)(cE.Slot,{scope:a.__scopeMenu,children:h.modal?(0,g.jsx)(cZ,{...e,ref:b}):(0,g.jsx)(c$,{...e,ref:b})})})})}),cZ=i.forwardRef((a,b)=>{let c=cM(cV,a.__scopeMenu),d=i.useRef(null),e=(0,l.s)(b,d);return i.useEffect(()=>{let a=d.current;if(a)return bV(a)},[]),(0,g.jsx)(c0,{...a,ref:e,trapFocus:c.open,disableOutsidePointerEvents:c.open,disableOutsideScroll:!0,onFocusOutside:(0,k.mK)(a.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>c.onOpenChange(!1)})}),c$=i.forwardRef((a,b)=>{let c=cM(cV,a.__scopeMenu);return(0,g.jsx)(c0,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>c.onOpenChange(!1)})}),c_=(0,p.TL)("MenuContent.ScrollLock"),c0=i.forwardRef((a,b)=>{let{__scopeMenu:c,loop:d=!1,trapFocus:e,onOpenAutoFocus:f,onCloseAutoFocus:h,disableOutsidePointerEvents:j,onEntryFocus:m,onEscapeKeyDown:n,onPointerDownOutside:o,onFocusOutside:p,onInteractOutside:q,onDismiss:r,disableOutsideScroll:s,...t}=a,u=cM(cV,c),v=cO(cV,c),w=cJ(c),x=cK(c),y=cF(c),[A,B]=i.useState(null),E=i.useRef(null),F=(0,l.s)(b,E,u.onContentChange),G=i.useRef(0),I=i.useRef(""),J=i.useRef(0),K=i.useRef(null),L=i.useRef("right"),M=i.useRef(0),N=s?cx:i.Fragment;i.useEffect(()=>()=>window.clearTimeout(G.current),[]),i.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??D()),document.body.insertAdjacentElement("beforeend",a[1]??D()),C++,()=>{1===C&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),C--}},[]);let O=i.useCallback(a=>L.current===K.current?.side&&function(a,b){return!!b&&function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}({x:a.clientX,y:a.clientY},b)}(a,K.current?.area),[]);return(0,g.jsx)(cW,{scope:c,searchRef:I,onItemEnter:i.useCallback(a=>{O(a)&&a.preventDefault()},[O]),onItemLeave:i.useCallback(a=>{O(a)||(E.current?.focus(),B(null))},[O]),onTriggerLeave:i.useCallback(a=>{O(a)&&a.preventDefault()},[O]),pointerGraceTimerRef:J,onPointerGraceIntentChange:i.useCallback(a=>{K.current=a},[]),children:(0,g.jsx)(N,{...s?{as:c_,allowPinchZoom:!0}:void 0,children:(0,g.jsx)(H,{asChild:!0,trapped:e,onMountAutoFocus:(0,k.mK)(f,a=>{a.preventDefault(),E.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:h,children:(0,g.jsx)(z,{asChild:!0,disableOutsidePointerEvents:j,onEscapeKeyDown:n,onPointerDownOutside:o,onFocusOutside:p,onInteractOutside:q,onDismiss:r,children:(0,g.jsx)(bJ,{asChild:!0,...x,dir:v.dir,orientation:"vertical",loop:d,currentTabStopId:A,onCurrentTabStopIdChange:B,onEntryFocus:(0,k.mK)(m,a=>{v.isUsingKeyboardRef.current||a.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,g.jsx)(bq,{role:"menu","aria-orientation":"vertical","data-state":dr(u.open),"data-radix-menu-content":"",dir:v.dir,...w,...t,ref:F,style:{outline:"none",...t.style},onKeyDown:(0,k.mK)(t.onKeyDown,a=>{let b=a.target.closest("[data-radix-menu-content]")===a.currentTarget,c=a.ctrlKey||a.altKey||a.metaKey,d=1===a.key.length;b&&("Tab"===a.key&&a.preventDefault(),!c&&d&&(a=>{let b=I.current+a,c=y().filter(a=>!a.disabled),d=document.activeElement,e=c.find(a=>a.ref.current===d)?.textValue,f=function(a,b,c){var d;let e=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,f=c?a.indexOf(c):-1,g=(d=Math.max(f,0),a.map((b,c)=>a[(d+c)%a.length]));1===e.length&&(g=g.filter(a=>a!==c));let h=g.find(a=>a.toLowerCase().startsWith(e.toLowerCase()));return h!==c?h:void 0}(c.map(a=>a.textValue),b,e),g=c.find(a=>a.textValue===f)?.ref.current;!function a(b){I.current=b,window.clearTimeout(G.current),""!==b&&(G.current=window.setTimeout(()=>a(""),1e3))}(b),g&&setTimeout(()=>g.focus())})(a.key));let e=E.current;if(a.target!==e||!cA.includes(a.key))return;a.preventDefault();let f=y().filter(a=>!a.disabled).map(a=>a.ref.current);cz.includes(a.key)&&f.reverse(),function(a){let b=document.activeElement;for(let c of a)if(c===b||(c.focus(),document.activeElement!==b))return}(f)}),onBlur:(0,k.mK)(a.onBlur,a=>{a.currentTarget.contains(a.target)||(window.clearTimeout(G.current),I.current="")}),onPointerMove:(0,k.mK)(a.onPointerMove,du(a=>{let b=a.target,c=M.current!==a.clientX;a.currentTarget.contains(b)&&c&&(L.current=a.clientX>M.current?"right":"left",M.current=a.clientX)}))})})})})})})});cY.displayName=cV;var c1=i.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,g.jsx)(o.sG.div,{role:"group",...d,ref:b})});c1.displayName="MenuGroup";var c2=i.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,g.jsx)(o.sG.div,{...d,ref:b})});c2.displayName="MenuLabel";var c3="MenuItem",c4="menu.itemSelect",c5=i.forwardRef((a,b)=>{let{disabled:c=!1,onSelect:d,...e}=a,f=i.useRef(null),h=cO(c3,a.__scopeMenu),j=cX(c3,a.__scopeMenu),m=(0,l.s)(b,f),n=i.useRef(!1);return(0,g.jsx)(c6,{...e,ref:m,disabled:c,onClick:(0,k.mK)(a.onClick,()=>{let a=f.current;if(!c&&a){let b=new CustomEvent(c4,{bubbles:!0,cancelable:!0});a.addEventListener(c4,a=>d?.(a),{once:!0}),(0,o.hO)(a,b),b.defaultPrevented?n.current=!1:h.onClose()}}),onPointerDown:b=>{a.onPointerDown?.(b),n.current=!0},onPointerUp:(0,k.mK)(a.onPointerUp,a=>{n.current||a.currentTarget?.click()}),onKeyDown:(0,k.mK)(a.onKeyDown,a=>{let b=""!==j.searchRef.current;c||b&&" "===a.key||cy.includes(a.key)&&(a.currentTarget.click(),a.preventDefault())})})});c5.displayName=c3;var c6=i.forwardRef((a,b)=>{let{__scopeMenu:c,disabled:d=!1,textValue:e,...f}=a,h=cX(c3,c),j=cK(c),m=i.useRef(null),n=(0,l.s)(b,m),[p,q]=i.useState(!1),[r,s]=i.useState("");return i.useEffect(()=>{let a=m.current;a&&s((a.textContent??"").trim())},[f.children]),(0,g.jsx)(cE.ItemSlot,{scope:c,disabled:d,textValue:e??r,children:(0,g.jsx)(bM,{asChild:!0,...j,focusable:!d,children:(0,g.jsx)(o.sG.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":d||void 0,"data-disabled":d?"":void 0,...f,ref:n,onPointerMove:(0,k.mK)(a.onPointerMove,du(a=>{d?h.onItemLeave(a):(h.onItemEnter(a),a.defaultPrevented||a.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,k.mK)(a.onPointerLeave,du(a=>h.onItemLeave(a))),onFocus:(0,k.mK)(a.onFocus,()=>q(!0)),onBlur:(0,k.mK)(a.onBlur,()=>q(!1))})})})}),c7=i.forwardRef((a,b)=>{let{checked:c=!1,onCheckedChange:d,...e}=a;return(0,g.jsx)(df,{scope:a.__scopeMenu,checked:c,children:(0,g.jsx)(c5,{role:"menuitemcheckbox","aria-checked":ds(c)?"mixed":c,...e,ref:b,"data-state":dt(c),onSelect:(0,k.mK)(e.onSelect,()=>d?.(!!ds(c)||!c),{checkForDefaultPrevented:!1})})})});c7.displayName="MenuCheckboxItem";var c8="MenuRadioGroup",[c9,da]=cH(c8,{value:void 0,onValueChange:()=>{}}),db=i.forwardRef((a,b)=>{let{value:c,onValueChange:d,...e}=a,f=w(d);return(0,g.jsx)(c9,{scope:a.__scopeMenu,value:c,onValueChange:f,children:(0,g.jsx)(c1,{...e,ref:b})})});db.displayName=c8;var dc="MenuRadioItem",dd=i.forwardRef((a,b)=>{let{value:c,...d}=a,e=da(dc,a.__scopeMenu),f=c===e.value;return(0,g.jsx)(df,{scope:a.__scopeMenu,checked:f,children:(0,g.jsx)(c5,{role:"menuitemradio","aria-checked":f,...d,ref:b,"data-state":dt(f),onSelect:(0,k.mK)(d.onSelect,()=>e.onValueChange?.(c),{checkForDefaultPrevented:!1})})})});dd.displayName=dc;var de="MenuItemIndicator",[df,dg]=cH(de,{checked:!1}),dh=i.forwardRef((a,b)=>{let{__scopeMenu:c,forceMount:d,...e}=a,f=dg(de,c);return(0,g.jsx)(by.C,{present:d||ds(f.checked)||!0===f.checked,children:(0,g.jsx)(o.sG.span,{...e,ref:b,"data-state":dt(f.checked)})})});dh.displayName=de;var di=i.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,g.jsx)(o.sG.div,{role:"separator","aria-orientation":"horizontal",...d,ref:b})});di.displayName="MenuSeparator";var dj=i.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=cJ(c);return(0,g.jsx)(bt,{...e,...d,ref:b})});dj.displayName="MenuArrow";var[dk,dl]=cH("MenuSub"),dm="MenuSubTrigger",dn=i.forwardRef((a,b)=>{let c=cM(dm,a.__scopeMenu),d=cO(dm,a.__scopeMenu),e=dl(dm,a.__scopeMenu),f=cX(dm,a.__scopeMenu),h=i.useRef(null),{pointerGraceTimerRef:j,onPointerGraceIntentChange:m}=f,n={__scopeMenu:a.__scopeMenu},o=i.useCallback(()=>{h.current&&window.clearTimeout(h.current),h.current=null},[]);return i.useEffect(()=>o,[o]),i.useEffect(()=>{let a=j.current;return()=>{window.clearTimeout(a),m(null)}},[j,m]),(0,g.jsx)(cQ,{asChild:!0,...n,children:(0,g.jsx)(c6,{id:e.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":e.contentId,"data-state":dr(c.open),...a,ref:(0,l.t)(b,e.onTriggerChange),onClick:b=>{a.onClick?.(b),a.disabled||b.defaultPrevented||(b.currentTarget.focus(),c.open||c.onOpenChange(!0))},onPointerMove:(0,k.mK)(a.onPointerMove,du(b=>{f.onItemEnter(b),!b.defaultPrevented&&(a.disabled||c.open||h.current||(f.onPointerGraceIntentChange(null),h.current=window.setTimeout(()=>{c.onOpenChange(!0),o()},100)))})),onPointerLeave:(0,k.mK)(a.onPointerLeave,du(a=>{o();let b=c.content?.getBoundingClientRect();if(b){let d=c.content?.dataset.side,e="right"===d,g=b[e?"left":"right"],h=b[e?"right":"left"];f.onPointerGraceIntentChange({area:[{x:a.clientX+(e?-5:5),y:a.clientY},{x:g,y:b.top},{x:h,y:b.top},{x:h,y:b.bottom},{x:g,y:b.bottom}],side:d}),window.clearTimeout(j.current),j.current=window.setTimeout(()=>f.onPointerGraceIntentChange(null),300)}else{if(f.onTriggerLeave(a),a.defaultPrevented)return;f.onPointerGraceIntentChange(null)}})),onKeyDown:(0,k.mK)(a.onKeyDown,b=>{let e=""!==f.searchRef.current;a.disabled||e&&" "===b.key||cB[d.dir].includes(b.key)&&(c.onOpenChange(!0),c.content?.focus(),b.preventDefault())})})})});dn.displayName=dm;var dp="MenuSubContent",dq=i.forwardRef((a,b)=>{let c=cT(cV,a.__scopeMenu),{forceMount:d=c.forceMount,...e}=a,f=cM(cV,a.__scopeMenu),h=cO(cV,a.__scopeMenu),j=dl(dp,a.__scopeMenu),m=i.useRef(null),n=(0,l.s)(b,m);return(0,g.jsx)(cE.Provider,{scope:a.__scopeMenu,children:(0,g.jsx)(by.C,{present:d||f.open,children:(0,g.jsx)(cE.Slot,{scope:a.__scopeMenu,children:(0,g.jsx)(c0,{id:j.contentId,"aria-labelledby":j.triggerId,...e,ref:n,align:"start",side:"rtl"===h.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:a=>{h.isUsingKeyboardRef.current&&m.current?.focus(),a.preventDefault()},onCloseAutoFocus:a=>a.preventDefault(),onFocusOutside:(0,k.mK)(a.onFocusOutside,a=>{a.target!==j.trigger&&f.onOpenChange(!1)}),onEscapeKeyDown:(0,k.mK)(a.onEscapeKeyDown,a=>{h.onClose(),a.preventDefault()}),onKeyDown:(0,k.mK)(a.onKeyDown,a=>{let b=a.currentTarget.contains(a.target),c=cC[h.dir].includes(a.key);b&&c&&(f.onOpenChange(!1),j.trigger?.focus(),a.preventDefault())})})})})})});function dr(a){return a?"open":"closed"}function ds(a){return"indeterminate"===a}function dt(a){return ds(a)?"indeterminate":a?"checked":"unchecked"}function du(a){return b=>"mouse"===b.pointerType?a(b):void 0}dq.displayName=dp;var dv="DropdownMenu",[dw,dx]=(0,m.A)(dv,[cI]),dy=cI(),[dz,dA]=dw(dv),dB=a=>{let{__scopeDropdownMenu:b,children:c,dir:d,open:e,defaultOpen:f,onOpenChange:h,modal:j=!0}=a,k=dy(b),l=i.useRef(null),[m,o]=(0,n.i)({prop:e,defaultProp:f??!1,onChange:h,caller:dv});return(0,g.jsx)(dz,{scope:b,triggerId:Q(),triggerRef:l,contentId:Q(),open:m,onOpenChange:o,onOpenToggle:i.useCallback(()=>o(a=>!a),[o]),modal:j,children:(0,g.jsx)(cP,{...k,open:m,onOpenChange:o,dir:d,modal:j,children:c})})};dB.displayName=dv;var dC="DropdownMenuTrigger",dD=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,disabled:d=!1,...e}=a,f=dA(dC,c),h=dy(c);return(0,g.jsx)(cQ,{asChild:!0,...h,children:(0,g.jsx)(o.sG.button,{type:"button",id:f.triggerId,"aria-haspopup":"menu","aria-expanded":f.open,"aria-controls":f.open?f.contentId:void 0,"data-state":f.open?"open":"closed","data-disabled":d?"":void 0,disabled:d,...e,ref:(0,l.t)(b,f.triggerRef),onPointerDown:(0,k.mK)(a.onPointerDown,a=>{!d&&0===a.button&&!1===a.ctrlKey&&(f.onOpenToggle(),f.open||a.preventDefault())}),onKeyDown:(0,k.mK)(a.onKeyDown,a=>{!d&&(["Enter"," "].includes(a.key)&&f.onOpenToggle(),"ArrowDown"===a.key&&f.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});dD.displayName=dC;var dE=a=>{let{__scopeDropdownMenu:b,...c}=a,d=dy(b);return(0,g.jsx)(cU,{...d,...c})};dE.displayName="DropdownMenuPortal";var dF="DropdownMenuContent",dG=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dA(dF,c),f=dy(c),h=i.useRef(!1);return(0,g.jsx)(cY,{id:e.contentId,"aria-labelledby":e.triggerId,...f,...d,ref:b,onCloseAutoFocus:(0,k.mK)(a.onCloseAutoFocus,a=>{h.current||e.triggerRef.current?.focus(),h.current=!1,a.preventDefault()}),onInteractOutside:(0,k.mK)(a.onInteractOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey,d=2===b.button||c;(!e.modal||d)&&(h.current=!0)}),style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});dG.displayName=dF,i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dy(c);return(0,g.jsx)(c1,{...e,...d,ref:b})}).displayName="DropdownMenuGroup";var dH=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dy(c);return(0,g.jsx)(c2,{...e,...d,ref:b})});dH.displayName="DropdownMenuLabel";var dI=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dy(c);return(0,g.jsx)(c5,{...e,...d,ref:b})});dI.displayName="DropdownMenuItem";var dJ=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dy(c);return(0,g.jsx)(c7,{...e,...d,ref:b})});dJ.displayName="DropdownMenuCheckboxItem";var dK=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dy(c);return(0,g.jsx)(db,{...e,...d,ref:b})});dK.displayName="DropdownMenuRadioGroup";var dL=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dy(c);return(0,g.jsx)(dd,{...e,...d,ref:b})});dL.displayName="DropdownMenuRadioItem";var dM=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dy(c);return(0,g.jsx)(dh,{...e,...d,ref:b})});dM.displayName="DropdownMenuItemIndicator";var dN=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dy(c);return(0,g.jsx)(di,{...e,...d,ref:b})});dN.displayName="DropdownMenuSeparator",i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dy(c);return(0,g.jsx)(dj,{...e,...d,ref:b})}).displayName="DropdownMenuArrow";var dO=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dy(c);return(0,g.jsx)(dn,{...e,...d,ref:b})});dO.displayName="DropdownMenuSubTrigger";var dP=i.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=dy(c);return(0,g.jsx)(dq,{...e,...d,ref:b,style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});dP.displayName="DropdownMenuSubContent";var dQ=c(33997);let dR=(0,dQ.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var dS=c(44634);let dT=(0,dQ.A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var dU=c(21026);i.forwardRef(({className:a,inset:b,children:c,...d},e)=>(0,g.jsxs)(dO,{ref:e,className:(0,dU.cn)("flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",b&&"pl-8",a),...d,children:[c,(0,g.jsx)(dR,{className:"ml-auto"})]})).displayName=dO.displayName,i.forwardRef(({className:a,...b},c)=>(0,g.jsx)(dP,{ref:c,className:(0,dU.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...b})).displayName=dP.displayName;let dV=i.forwardRef(({className:a,sideOffset:b=4,...c},d)=>(0,g.jsx)(dE,{children:(0,g.jsx)(dG,{ref:d,sideOffset:b,className:(0,dU.cn)("z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]",a),...c})}));dV.displayName=dG.displayName,i.forwardRef(({className:a,inset:b,...c},d)=>(0,g.jsx)(dI,{ref:d,className:(0,dU.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0",b&&"pl-8",a),...c})).displayName=dI.displayName,i.forwardRef(({className:a,children:b,checked:c,...d},e)=>(0,g.jsxs)(dJ,{ref:e,className:(0,dU.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),checked:c,...d,children:[(0,g.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,g.jsx)(dM,{children:(0,g.jsx)(dS.A,{className:"h-4 w-4"})})}),b]})).displayName=dJ.displayName;let dW=i.forwardRef(({className:a,children:b,...c},d)=>(0,g.jsxs)(dL,{ref:d,className:(0,dU.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,g.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,g.jsx)(dM,{children:(0,g.jsx)(dT,{className:"h-2 w-2 fill-current"})})}),b]}));dW.displayName=dL.displayName,i.forwardRef(({className:a,inset:b,...c},d)=>(0,g.jsx)(dH,{ref:d,className:(0,dU.cn)("px-2 py-1.5 text-sm font-semibold",b&&"pl-8",a),...c})).displayName=dH.displayName,i.forwardRef(({className:a,...b},c)=>(0,g.jsx)(dN,{ref:c,className:(0,dU.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=dN.displayName;let dX=(0,dQ.A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),dY=(0,dQ.A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),dZ=(0,dQ.A)("laptop",[["path",{d:"M18 5a2 2 0 0 1 2 2v8.526a2 2 0 0 0 .212.897l1.068 2.127a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45l1.068-2.127A2 2 0 0 0 4 15.526V7a2 2 0 0 1 2-2z",key:"1pdavp"}],["path",{d:"M20.054 15.987H3.946",key:"14rxg9"}]]);var d$=c(57480);let d_=()=>{let[a,b]=(0,i.useState)(!1),{theme:c,setTheme:d}=(0,d$.D)();return((0,i.useEffect)(()=>{b(!0)},[]),a)?(0,g.jsxs)(dB,{children:[(0,g.jsx)(dD,{asChild:!0,children:(0,g.jsx)(h.$,{variant:"ghost",size:"sm",children:"light"===c?(0,g.jsx)(dX,{size:16,className:"text-muted-foreground"},"light"):"dark"===c?(0,g.jsx)(dY,{size:16,className:"text-muted-foreground"},"dark"):(0,g.jsx)(dZ,{size:16,className:"text-muted-foreground"},"system")})}),(0,g.jsx)(dV,{className:"w-content",align:"start",children:(0,g.jsxs)(dK,{value:c,onValueChange:a=>d(a),children:[(0,g.jsxs)(dW,{className:"flex gap-2",value:"light",children:[(0,g.jsx)(dX,{size:16,className:"text-muted-foreground"})," ",(0,g.jsx)("span",{children:"Light"})]}),(0,g.jsxs)(dW,{className:"flex gap-2",value:"dark",children:[(0,g.jsx)(dY,{size:16,className:"text-muted-foreground"})," ",(0,g.jsx)("span",{children:"Dark"})]}),(0,g.jsxs)(dW,{className:"flex gap-2",value:"system",children:[(0,g.jsx)(dZ,{size:16,className:"text-muted-foreground"})," ",(0,g.jsx)("span",{children:"System"})]})]})})]}):null}},40607:(a,b,c)=>{"use strict";c.d(b,{X:()=>f});var d=c(84041),e=c(94844);function f(a){let[b,c]=d.useState(void 0);return(0,e.N)(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},44532:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i});var d=c(31850),e=c(36785),f=c(9829);let g=(0,c(4332).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var h=c(57432);async function i(){let a=await (0,f.U)(),{data:b,error:c}=await a.auth.getClaims();return(c||!b?.claims)&&(0,e.redirect)("/auth/login"),(0,d.jsxs)("div",{className:"flex-1 w-full flex flex-col gap-12",children:[(0,d.jsx)("div",{className:"w-full",children:(0,d.jsxs)("div",{className:"bg-accent text-sm p-3 px-5 rounded-md text-foreground flex gap-3 items-center",children:[(0,d.jsx)(g,{size:"16",strokeWidth:2}),"This is a protected page that you can only see as an authenticated user"]})}),(0,d.jsxs)("div",{className:"flex flex-col gap-2 items-start",children:[(0,d.jsx)("h2",{className:"font-bold text-2xl mb-4",children:"Your user details"}),(0,d.jsx)("pre",{className:"text-xs font-mono p-3 rounded border max-h-32 overflow-auto",children:JSON.stringify(b.claims,null,2)})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"font-bold text-2xl mb-4",children:"Next steps"}),(0,d.jsx)(h.FetchDataSteps,{})]})]})}},44634:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(33997).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},46944:(a,b,c)=>{Promise.resolve().then(c.bind(c,57432))},47655:(a,b,c)=>{Promise.resolve().then(c.bind(c,97715)),Promise.resolve().then(c.bind(c,40357)),Promise.resolve().then(c.t.bind(c,4163,23))},48067:(a,b,c)=>{"use strict";c.d(b,{A:()=>f});var d=c(84041),e=c(92536);function f(a,b=[]){let c=[],g=()=>{let b=c.map(a=>d.createContext(a));return function(c){let e=c?.[a]||b;return d.useMemo(()=>({[`__scope${a}`]:{...c,[a]:e}}),[c,e])}};return g.scopeName=a,[function(b,f){let g=d.createContext(f),h=c.length;c=[...c,f];let i=b=>{let{scope:c,children:f,...i}=b,j=c?.[a]?.[h]||g,k=d.useMemo(()=>i,Object.values(i));return(0,e.jsx)(j.Provider,{value:k,children:f})};return i.displayName=b+"Provider",[i,function(c,e){let i=e?.[a]?.[h]||g,j=d.useContext(i);if(j)return j;if(void 0!==f)return f;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let e=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return d.useMemo(()=>({[`__scope${b.scopeName}`]:e}),[e])}};return c.scopeName=b.scopeName,c}(g,...b)]}},50651:(a,b,c)=>{"use strict";c.d(b,{U:()=>e});var d=c(31896);function e(){return(0,d.createBrowserClient)("https://hmlxgkfvjzpldqhpsoya.supabase.co","sb_publishable_sKUrOhdbUU32vkHDprfP3A_0FrYxKrO")}},55591:a=>{"use strict";a.exports=require("https")},57432:(a,b,c)=>{"use strict";c.d(b,{FetchDataSteps:()=>d});let d=(0,c(90850).registerClientReference)(function(){throw Error("Attempted to call FetchDataSteps() from the server but FetchDataSteps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\via\\components\\tutorial\\fetch-data-steps.tsx","FetchDataSteps")},57713:(a,b,c)=>{"use strict";c.d(b,{i:()=>h});var d,e=c(84041),f=c(94844),g=(d||(d=c.t(e,2)))[" useInsertionEffect ".trim().toString()]||f.N;function h({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[f,h,i]=function({defaultProp:a,onChange:b}){let[c,d]=e.useState(a),f=e.useRef(c),h=e.useRef(b);return g(()=>{h.current=b},[b]),e.useEffect(()=>{f.current!==c&&(h.current?.(c),f.current=c)},[c,f]),[c,d,h]}({defaultProp:b,onChange:c}),j=void 0!==a,k=j?a:f;{let b=e.useRef(void 0!==a);e.useEffect(()=>{let a=b.current;if(a!==j){let b=j?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=j},[j,d])}return[k,e.useCallback(b=>{if(j){let c="function"==typeof b?b(a):b;c!==a&&i.current?.(c)}else h(b)},[j,a,h,i])]}Symbol("RADIX:SYNC_STATE")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66244:(a,b,c)=>{"use strict";c.d(b,{C:()=>g});var d=c(84041),e=c(11656),f=c(94844),g=a=>{let{present:b,children:c}=a,g=function(a){var b,c;let[e,g]=d.useState(),i=d.useRef(null),j=d.useRef(a),k=d.useRef("none"),[l,m]=(b=a?"mounted":"unmounted",c={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},d.useReducer((a,b)=>c[a][b]??a,b));return d.useEffect(()=>{let a=h(i.current);k.current="mounted"===l?a:"none"},[l]),(0,f.N)(()=>{let b=i.current,c=j.current;if(c!==a){let d=k.current,e=h(b);a?m("MOUNT"):"none"===e||b?.display==="none"?m("UNMOUNT"):c&&d!==e?m("ANIMATION_OUT"):m("UNMOUNT"),j.current=a}},[a,m]),(0,f.N)(()=>{if(e){let a,b=e.ownerDocument.defaultView??window,c=c=>{let d=h(i.current).includes(CSS.escape(c.animationName));if(c.target===e&&d&&(m("ANIMATION_END"),!j.current)){let c=e.style.animationFillMode;e.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===e.style.animationFillMode&&(e.style.animationFillMode=c)})}},d=a=>{a.target===e&&(k.current=h(i.current))};return e.addEventListener("animationstart",d),e.addEventListener("animationcancel",c),e.addEventListener("animationend",c),()=>{b.clearTimeout(a),e.removeEventListener("animationstart",d),e.removeEventListener("animationcancel",c),e.removeEventListener("animationend",c)}}m("ANIMATION_END")},[e,m]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:d.useCallback(a=>{i.current=a?getComputedStyle(a):null,g(a)},[])}}(b),i="function"==typeof c?c({present:g.isPresent}):d.Children.only(c),j=(0,e.s)(g.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(i));return"function"==typeof c||g.isPresent?d.cloneElement(i,{ref:j}):null};function h(a){return a?.animationName||"none"}g.displayName="Presence"},74075:a=>{"use strict";a.exports=require("zlib")},76231:(a,b,c)=>{"use strict";c.d(b,{F:()=>g});var d=c(61601);let e=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,f=d.$,g=(a,b)=>c=>{var d;if((null==b?void 0:b.variants)==null)return f(a,null==c?void 0:c.class,null==c?void 0:c.className);let{variants:g,defaultVariants:h}=b,i=Object.keys(g).map(a=>{let b=null==c?void 0:c[a],d=null==h?void 0:h[a];if(null===b)return null;let f=e(b)||e(d);return g[a][f]}),j=c&&Object.entries(c).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return f(a,i,null==b||null==(d=b.compoundVariants)?void 0:d.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...h,...j}[b]):({...h,...j})[b]===c})?[...a,c,d]:a},[]),null==c?void 0:c.class,null==c?void 0:c.className)}},76358:(a,b,c)=>{"use strict";function d(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}c.d(b,{mK:()=>d}),"undefined"!=typeof window&&window.document&&window.document.createElement},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93789:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>r});var d=c(31850),e=c(81169),f=c.n(e),g=c(13129);function h(){return(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(f(),{href:"https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&project-name=nextjs-with-supabase&repository-name=nextjs-with-supabase&demo-title=nextjs-with-supabase&demo-description=This+starter+configures+Supabase+Auth+to+use+cookies%2C+making+the+user%27s+session+available+throughout+the+entire+Next.js+app+-+Client+Components%2C+Server+Components%2C+Route+Handlers%2C+Server+Actions+and+Middleware.&demo-url=https%3A%2F%2Fdemo-nextjs-with-supabase.vercel.app%2F&external-id=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&demo-image=https%3A%2F%2Fdemo-nextjs-with-supabase.vercel.app%2Fopengraph-image.png",target:"_blank",children:(0,d.jsxs)(g.$,{className:"flex items-center gap-2",size:"sm",children:[(0,d.jsx)("svg",{className:"h-3 w-3",viewBox:"0 0 76 65",fill:"hsl(var(--background)/1)",xmlns:"http://www.w3.org/2000/svg",children:(0,d.jsx)("path",{d:"M37.5274 0L75.0548 65H0L37.5274 0Z",fill:"inherit"})}),(0,d.jsx)("span",{children:"Deploy to Vercel"})]})})})}c(62003);var i=c(76231),j=c(15292);let k=(0,i.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,j.cn)(k({variant:b}),a),...c})}function m(){return(0,d.jsxs)("div",{className:"flex gap-4 items-center",children:[(0,d.jsx)(l,{variant:"outline",className:"font-normal",children:"Supabase environment variables required"}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(g.$,{size:"sm",variant:"outline",disabled:!0,children:"Sign in"}),(0,d.jsx)(g.$,{size:"sm",variant:"default",disabled:!0,children:"Sign up"})]})]})}var n=c(9829),o=c(941);async function p(){let a=await (0,n.U)(),{data:b}=await a.auth.getClaims(),c=b?.claims;return c?(0,d.jsxs)("div",{className:"flex items-center gap-4",children:["Hey, ",c.email,"!",(0,d.jsx)(o.LogoutButton,{})]}):(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(g.$,{asChild:!0,size:"sm",variant:"outline",children:(0,d.jsx)(f(),{href:"/auth/login",children:"Sign in"})}),(0,d.jsx)(g.$,{asChild:!0,size:"sm",variant:"default",children:(0,d.jsx)(f(),{href:"/auth/sign-up",children:"Sign up"})})]})}var q=c(9949);function r({children:a}){return(0,d.jsx)("main",{className:"min-h-screen flex flex-col items-center",children:(0,d.jsxs)("div",{className:"flex-1 w-full flex flex-col gap-20 items-center",children:[(0,d.jsx)("nav",{className:"w-full flex justify-center border-b border-b-foreground/10 h-16",children:(0,d.jsxs)("div",{className:"w-full max-w-5xl flex justify-between items-center p-3 px-5 text-sm",children:[(0,d.jsxs)("div",{className:"flex gap-5 items-center font-semibold",children:[(0,d.jsx)(f(),{href:"/",children:"Next.js Supabase Starter"}),(0,d.jsx)("div",{className:"flex items-center gap-2",children:(0,d.jsx)(h,{})})]}),j.A?(0,d.jsx)(p,{}):(0,d.jsx)(m,{})]})}),(0,d.jsx)("div",{className:"flex-1 flex flex-col gap-20 max-w-5xl p-5",children:a}),(0,d.jsxs)("footer",{className:"w-full flex items-center justify-center border-t mx-auto text-center text-xs gap-8 py-16",children:[(0,d.jsxs)("p",{children:["Powered by"," ",(0,d.jsx)("a",{href:"https://supabase.com/?utm_source=create-next-app&utm_medium=template&utm_term=nextjs",target:"_blank",className:"font-bold hover:underline",rel:"noreferrer",children:"Supabase"})]}),(0,d.jsx)(q.ThemeSwitcher,{})]})]})})}},94844:(a,b,c)=>{"use strict";c.d(b,{N:()=>e});var d=c(84041),e=globalThis?.document?d.useLayoutEffect:()=>{}},97715:(a,b,c)=>{"use strict";c.d(b,{LogoutButton:()=>h});var d=c(92536),e=c(50651),f=c(15353),g=c(63414);function h(){let a=(0,g.useRouter)(),b=async()=>{let b=(0,e.U)();await b.auth.signOut(),a.push("/auth/login")};return(0,d.jsx)(f.$,{onClick:b,children:"Logout"})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[650,203,641,603,732,807],()=>b(b.s=15598));module.exports=c})();