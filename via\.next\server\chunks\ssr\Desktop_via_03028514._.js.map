{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// This check can be removed, it is just for tutorial purposes\nexport const hasEnvVars =\n  process.env.NEXT_PUBLIC_SUPABASE_URL &&\n  process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sPAAO,EAAC,IAAA,gNAAI,EAAC;AACtB;AAGO,MAAM,aACX", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,IAAA,oQAAG,EACxB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,oWAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,+TAAI,GAAG;IAC9B,qBACE,+XAAC;QACC,WAAW,IAAA,oIAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/locations/%5Bslug%5D/not-found.tsx"], "sourcesContent": ["import Link from \"next/link\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { MapPin, ArrowLeft } from \"lucide-react\";\r\n\r\nexport default function LocationNotFound() {\r\n  return (\r\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\r\n      <div className=\"max-w-md mx-auto text-center px-4\">\r\n        <div className=\"mb-8\">\r\n          <MapPin className=\"w-16 h-16 text-via-primary mx-auto mb-4\" />\r\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\r\n            Location Not Found\r\n          </h1>\r\n          <p className=\"text-lg text-gray-600 mb-8\">\r\n            The location you&apos;re looking for doesn&apos;t exist. Please check the URL or browse our available locations.\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"space-y-4\">\r\n          <Link href=\"/locations\">\r\n            <Button className=\"w-full bg-via-primary hover:bg-via-primary-dark text-white\">\r\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\r\n              View All Locations\r\n            </Button>\r\n          </Link>\r\n          \r\n          <Link href=\"/\">\r\n            <Button variant=\"outline\" className=\"w-full border-via-primary text-via-primary hover:bg-via-primary hover:text-white\">\r\n              Back to Home\r\n            </Button>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;;;;;AAEe,SAAS;IACtB,qBACE,+XAAC;QAAI,WAAU;kBACb,cAAA,+XAAC;YAAI,WAAU;;8BACb,+XAAC;oBAAI,WAAU;;sCACb,+XAAC,0TAAM;4BAAC,WAAU;;;;;;sCAClB,+XAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,+XAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,+XAAC;oBAAI,WAAU;;sCACb,+XAAC,wTAAI;4BAAC,MAAK;sCACT,cAAA,+XAAC,uJAAM;gCAAC,WAAU;;kDAChB,+XAAC,mUAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAK1C,+XAAC,wTAAI;4BAAC,MAAK;sCACT,cAAA,+XAAC,uJAAM;gCAAC,SAAQ;gCAAU,WAAU;0CAAmF;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnI", "debugId": null}}]}