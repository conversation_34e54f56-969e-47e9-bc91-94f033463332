var R=require("../../../chunks/ssr/[turbopack]_runtime.js")("server/app/locations/[slug]/page.js")
R.c("server/chunks/ssr/8fdc9_next_dist_9e9c45fc._.js")
R.c("server/chunks/ssr/712b6_@swc_helpers_cjs__interop_require_wildcard_cjs_c2d68824._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/Desktop_via_app_49759369._.js")
R.c("server/chunks/ssr/Desktop_via_app_9591883a._.js")
R.c("server/chunks/ssr/Desktop_via_app_350f5192._.js")
R.c("server/chunks/ssr/3cceb__pnpm_aa5d2298._.js")
R.c("server/chunks/ssr/[root-of-the-server]__c1c2812f._.js")
R.c("server/chunks/ssr/8fdc9_next_dist_client_components_48a20091._.js")
R.c("server/chunks/ssr/8fdc9_next_dist_client_components_builtin_forbidden_2562d45f.js")
R.c("server/chunks/ssr/8fdc9_next_dist_client_components_builtin_unauthorized_81c22823.js")
R.c("server/chunks/ssr/8fdc9_next_dist_client_components_builtin_global-error_826b77aa.js")
R.c("server/chunks/ssr/Desktop_via_app_locations_[slug]_layout_tsx_7ae84967._.js")
R.c("server/chunks/ssr/Desktop_via_app_locations_[slug]_loading_tsx_b188a156._.js")
R.c("server/chunks/ssr/3cceb__pnpm_dc607e83._.js")
R.c("server/chunks/ssr/Desktop_via_03028514._.js")
R.c("server/chunks/ssr/8fdc9_next_dist_904f1bcc._.js")
R.c("server/chunks/ssr/[root-of-the-server]__27fe67f4._.js")
R.m("[project]/Desktop/via/.next-internal/server/app/locations/[slug]/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/Desktop/via/node_modules/.pnpm/next@15.5.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/esm/build/templates/app-page.js?page=/locations/[slug]/page { GLOBAL_ERROR_MODULE => \"[project]/Desktop/via/node_modules/.pnpm/next@15.5.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Desktop/via/app/favicon.ico.mjs { IMAGE => \\\"[project]/Desktop/via/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", METADATA_1 => \"[project]/Desktop/via/app/twitter-image.png.mjs { IMAGE => \\\"[project]/Desktop/via/app/twitter-image.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", METADATA_2 => \"[project]/Desktop/via/app/opengraph-image.png.mjs { IMAGE => \\\"[project]/Desktop/via/app/opengraph-image.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Desktop/via/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Desktop/via/node_modules/.pnpm/next@15.5.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Desktop/via/node_modules/.pnpm/next@15.5.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Desktop/via/node_modules/.pnpm/next@15.5.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/Desktop/via/node_modules/.pnpm/next@15.5.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/Desktop/via/app/locations/[slug]/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_9 => \"[project]/Desktop/via/app/locations/[slug]/loading.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_10 => \"[project]/Desktop/via/app/locations/[slug]/not-found.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_11 => \"[project]/Desktop/via/app/locations/[slug]/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/Desktop/via/node_modules/.pnpm/next@15.5.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/esm/build/templates/app-page.js?page=/locations/[slug]/page { GLOBAL_ERROR_MODULE => \"[project]/Desktop/via/node_modules/.pnpm/next@15.5.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/Desktop/via/app/favicon.ico.mjs { IMAGE => \\\"[project]/Desktop/via/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", METADATA_1 => \"[project]/Desktop/via/app/twitter-image.png.mjs { IMAGE => \\\"[project]/Desktop/via/app/twitter-image.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", METADATA_2 => \"[project]/Desktop/via/app/opengraph-image.png.mjs { IMAGE => \\\"[project]/Desktop/via/app/opengraph-image.png (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/Desktop/via/app/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/Desktop/via/node_modules/.pnpm/next@15.5.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/Desktop/via/node_modules/.pnpm/next@15.5.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/Desktop/via/node_modules/.pnpm/next@15.5.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/Desktop/via/node_modules/.pnpm/next@15.5.0_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/Desktop/via/app/locations/[slug]/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_9 => \"[project]/Desktop/via/app/locations/[slug]/loading.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_10 => \"[project]/Desktop/via/app/locations/[slug]/not-found.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_11 => \"[project]/Desktop/via/app/locations/[slug]/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
