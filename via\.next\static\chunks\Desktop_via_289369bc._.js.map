{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/blog-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Calendar, User, ArrowRight, Clock, Tag } from \"lucide-react\";\nimport { fadeInUp, fadeInDown, staggerContainer, staggerItem, animationConfig } from \"@/lib/animations\";\n\n// Blog post interface\ninterface BlogPost {\n  id: number;\n  title: string;\n  excerpt: string;\n  author: string;\n  date: string;\n  category: string;\n  readTime: string;\n  image: string;\n  featured?: boolean;\n}\n\n// Sample blog posts with comprehensive content\nconst blogPosts: BlogPost[] = [\n  {\n    id: 1,\n    title: \"5 Traits of a Successful Entrepreneur: Do you have what it takes?\",\n    excerpt: \"Discover the essential characteristics that separate successful entrepreneurs from the rest. Learn how to develop these crucial traits and apply them to your business journey.\",\n    author: \"Via Team\",\n    date: \"December 15, 2024\",\n    category: \"Business Tips\",\n    readTime: \"5 min read\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg\",\n    featured: true\n  },\n  {\n    id: 2,\n    title: \"How Much Does It Cost to Rent an Office in the RGV Area\",\n    excerpt: \"Get a comprehensive breakdown of office rental costs in the Rio Grande Valley. Compare different options and find the perfect workspace for your budget.\",\n    author: \"Via Team\",\n    date: \"December 12, 2024\",\n    category: \"Market Insights\",\n    readTime: \"7 min read\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/about_us-section_1-img_1.jpg\"\n  },\n  {\n    id: 3,\n    title: \"5 Benefits of Bilingual Answering Services\",\n    excerpt: \"Explore how bilingual answering services can expand your business reach and improve customer satisfaction in diverse markets like the Rio Grande Valley.\",\n    author: \"Via Team\",\n    date: \"December 10, 2024\",\n    category: \"Business Growth\",\n    readTime: \"4 min read\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg\"\n  },\n  {\n    id: 4,\n    title: \"How to Find Small Business Space for Rent in McAllen\",\n    excerpt: \"A complete guide to finding the perfect small business space in McAllen. Tips, locations, and what to look for when choosing your next office.\",\n    author: \"Via Team\",\n    date: \"December 8, 2024\",\n    category: \"Location Guide\",\n    readTime: \"6 min read\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/about_us-section_1-img_1.jpg\"\n  },\n  {\n    id: 5,\n    title: \"When to Use a Virtual Business Address vs Executive Office\",\n    excerpt: \"Understanding the differences between virtual addresses and executive offices. Make the right choice for your business needs and budget.\",\n    author: \"Via Team\",\n    date: \"December 5, 2024\",\n    category: \"Business Solutions\",\n    readTime: \"5 min read\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg\"\n  },\n  {\n    id: 6,\n    title: \"Top 5 Reasons Why It's Better to Lease Office Space\",\n    excerpt: \"Discover the advantages of leasing office space over buying. From flexibility to cost savings, learn why leasing might be the smart choice for your business.\",\n    author: \"Via Team\",\n    date: \"December 3, 2024\",\n    category: \"Business Strategy\",\n    readTime: \"4 min read\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/about_us-section_1-img_1.jpg\"\n  },\n  {\n    id: 7,\n    title: \"What Should you Look for in Meeting Rooms in McAllen?\",\n    excerpt: \"Essential features to consider when booking meeting rooms in McAllen. From technology to location, ensure your meetings are productive and professional.\",\n    author: \"Via Team\",\n    date: \"November 28, 2024\",\n    category: \"Meeting Solutions\",\n    readTime: \"3 min read\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg\"\n  },\n  {\n    id: 8,\n    title: \"Why Rent a Fully Furnished Office Space?\",\n    excerpt: \"The benefits of choosing fully furnished office spaces for your business. Save time, money, and hassle while maintaining a professional appearance.\",\n    author: \"Via Team\",\n    date: \"November 25, 2024\",\n    category: \"Office Solutions\",\n    readTime: \"5 min read\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/about_us-section_1-img_1.jpg\"\n  },\n  {\n    id: 9,\n    title: \"Pros and Cons of Home Office vs Executive Office Rentals\",\n    excerpt: \"Compare the advantages and disadvantages of working from home versus renting an executive office. Make an informed decision for your business.\",\n    author: \"Via Team\",\n    date: \"November 22, 2024\",\n    category: \"Workspace Comparison\",\n    readTime: \"6 min read\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg\"\n  },\n  {\n    id: 10,\n    title: \"5 Tips for Renting Your First Office Space in McAllen\",\n    excerpt: \"First-time office renter? Get expert advice on what to look for, questions to ask, and how to negotiate the best deal for your new business space.\",\n    author: \"Via Team\",\n    date: \"November 20, 2024\",\n    category: \"Getting Started\",\n    readTime: \"7 min read\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/about_us-section_1-img_1.jpg\"\n  },\n  {\n    id: 11,\n    title: \"The Rise of Coworking Spaces in South Texas\",\n    excerpt: \"Explore how coworking spaces are transforming the business landscape in South Texas and why they're becoming the preferred choice for modern professionals.\",\n    author: \"Via Team\",\n    date: \"November 18, 2024\",\n    category: \"Industry Trends\",\n    readTime: \"6 min read\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg\"\n  },\n  {\n    id: 12,\n    title: \"Networking Tips for Executive Suite Tenants\",\n    excerpt: \"Maximize your business connections and build valuable relationships with fellow professionals in your executive suite building.\",\n    author: \"Via Team\",\n    date: \"November 15, 2024\",\n    category: \"Networking\",\n    readTime: \"4 min read\",\n    image: \"https://via2success.com/wp-content/uploads/2021/01/about_us-section_1-img_1.jpg\"\n  }\n];\n\nexport function BlogSection() {\n  return (\n    <>\n      {/* Blog Posts Grid */}\n      <section className=\"py-16 bg-white overflow-hidden\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          {/* Section Header */}\n          <motion.div\n            className=\"text-center mb-16\"\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={animationConfig.viewport}\n            variants={fadeInDown}\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Latest Business Insights\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Stay ahead of the curve with expert advice, industry trends, and practical tips \n              to help your business succeed in the Rio Grande Valley and beyond.\n            </p>\n          </motion.div>\n\n          {/* Blog Posts Grid */}\n          <motion.div\n            className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={animationConfig.viewport}\n            variants={staggerContainer}\n          >\n            {blogPosts.map((post, index) => (\n              <motion.article\n                key={post.id}\n                className={`bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden group cursor-pointer ${\n                  post.featured ? 'md:col-span-2 lg:col-span-1' : ''\n                }`}\n                variants={staggerItem}\n                whileHover={{\n                  y: -8,\n                  scale: 1.02,\n                  boxShadow: \"0 25px 50px -12px rgba(0, 0, 0, 0.15)\",\n                  transition: {\n                    duration: 0.3,\n                    ease: \"easeInOut\"\n                  }\n                }}\n                whileTap={{ scale: 0.98 }}\n              >\n                {/* Featured Badge */}\n                {post.featured && (\n                  <motion.div\n                    className=\"absolute top-4 left-4 z-10 bg-via-primary text-white px-3 py-1 rounded-full text-xs font-semibold\"\n                    initial={{ opacity: 0, scale: 0 }}\n                    whileInView={{ opacity: 1, scale: 1 }}\n                    viewport={animationConfig.viewport}\n                    transition={{ delay: index * 0.1 + 0.3 }}\n                  >\n                    Featured\n                  </motion.div>\n                )}\n\n                {/* Image Container */}\n                <div className=\"relative overflow-hidden\">\n                  <motion.img\n                    src={post.image}\n                    alt={post.title}\n                    className=\"w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500\"\n                    initial={{ scale: 1.1 }}\n                    whileInView={{ scale: 1 }}\n                    viewport={animationConfig.viewport}\n                    transition={{ duration: 0.8, delay: index * 0.1 }}\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                </div>\n                \n                {/* Content */}\n                <div className=\"p-6\">\n                  {/* Category and Read Time */}\n                  <div className=\"flex items-center justify-between text-sm text-gray-500 mb-3\">\n                    <motion.span\n                      className=\"bg-via-primary/10 text-via-primary px-3 py-1 rounded-full text-xs font-medium flex items-center\"\n                      whileHover={{ scale: 1.05 }}\n                    >\n                      <Tag className=\"h-3 w-3 mr-1\" />\n                      {post.category}\n                    </motion.span>\n                    <span className=\"flex items-center text-gray-400\">\n                      <Clock className=\"h-3 w-3 mr-1\" />\n                      {post.readTime}\n                    </span>\n                  </div>\n                  \n                  {/* Title */}\n                  <motion.h3\n                    className=\"text-xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-via-primary transition-colors duration-300\"\n                    whileHover={{ scale: 1.02 }}\n                  >\n                    {post.title}\n                  </motion.h3>\n                  \n                  {/* Excerpt */}\n                  <p className=\"text-gray-600 mb-4 line-clamp-3 leading-relaxed\">\n                    {post.excerpt}\n                  </p>\n                  \n                  {/* Meta Information */}\n                  <div className=\"flex items-center justify-between text-sm text-gray-500 mb-4\">\n                    <div className=\"flex items-center\">\n                      <User className=\"h-4 w-4 mr-1\" />\n                      {post.author}\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Calendar className=\"h-4 w-4 mr-1\" />\n                      {post.date}\n                    </div>\n                  </div>\n                  \n                  {/* Read More Button */}\n                  <motion.div\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <Button className=\"w-full bg-via-primary hover:bg-via-primary-dark text-white group-hover:shadow-lg transition-all duration-300\">\n                      Read More\n                      <motion.div\n                        className=\"ml-2\"\n                        whileHover={{ x: 4 }}\n                        transition={{ duration: 0.2 }}\n                      >\n                        <ArrowRight className=\"h-4 w-4\" />\n                      </motion.div>\n                    </Button>\n                  </motion.div>\n                </div>\n              </motion.article>\n            ))}\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Newsletter Section */}\n      <section className=\"py-16 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            className=\"text-center\"\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={animationConfig.viewport}\n            variants={fadeInUp}\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Stay in the Loop\n            </h2>\n            <p className=\"text-xl text-gray-600 mb-8\">\n              Get the latest business insights, office space tips, and industry updates \n              delivered straight to your inbox.\n            </p>\n            \n            <motion.div\n              className=\"flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto\"\n              variants={staggerContainer}\n              initial=\"hidden\"\n              whileInView=\"visible\"\n              viewport={animationConfig.viewport}\n            >\n              <motion.input\n                type=\"email\"\n                placeholder=\"Enter your email address\"\n                className=\"flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent transition-all duration-300\"\n                variants={staggerItem}\n                whileFocus={{ scale: 1.02 }}\n              />\n              <motion.div\n                variants={staggerItem}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <Button className=\"bg-via-primary hover:bg-via-primary-dark text-white px-6 py-3 shadow-lg hover:shadow-xl transition-all duration-300\">\n                  Subscribe\n                  <ArrowRight className=\"ml-2 h-4 w-4\" />\n                </Button>\n              </motion.div>\n            </motion.div>\n          </motion.div>\n        </div>\n      </section>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAoBA,+CAA+C;AAC/C,MAAM,YAAwB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,QAAQ;QACR,MAAM;QACN,UAAU;QACV,UAAU;QACV,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE;;0BAEE,8UAAC;gBAAQ,WAAU;0BACjB,cAAA,8UAAC;oBAAI,WAAU;;sCAEb,8UAAC,+TAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU,yJAAe,CAAC,QAAQ;4BAClC,UAAU,oJAAU;;8CAEpB,8UAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8UAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAOzD,8UAAC,+TAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAQ;4BACR,aAAY;4BACZ,UAAU,yJAAe,CAAC,QAAQ;4BAClC,UAAU,0JAAgB;sCAEzB,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8UAAC,+TAAM,CAAC,OAAO;oCAEb,WAAW,AAAC,6FAEX,OADC,KAAK,QAAQ,GAAG,gCAAgC;oCAElD,UAAU,qJAAW;oCACrB,YAAY;wCACV,GAAG,CAAC;wCACJ,OAAO;wCACP,WAAW;wCACX,YAAY;4CACV,UAAU;4CACV,MAAM;wCACR;oCACF;oCACA,UAAU;wCAAE,OAAO;oCAAK;;wCAGvB,KAAK,QAAQ,kBACZ,8UAAC,+TAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,aAAa;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CACpC,UAAU,yJAAe,CAAC,QAAQ;4CAClC,YAAY;gDAAE,OAAO,QAAQ,MAAM;4CAAI;sDACxC;;;;;;sDAMH,8UAAC;4CAAI,WAAU;;8DACb,8UAAC,+TAAM,CAAC,GAAG;oDACT,KAAK,KAAK,KAAK;oDACf,KAAK,KAAK,KAAK;oDACf,WAAU;oDACV,SAAS;wDAAE,OAAO;oDAAI;oDACtB,aAAa;wDAAE,OAAO;oDAAE;oDACxB,UAAU,yJAAe,CAAC,QAAQ;oDAClC,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;;;;;;8DAElD,8UAAC;oDAAI,WAAU;;;;;;;;;;;;sDAIjB,8UAAC;4CAAI,WAAU;;8DAEb,8UAAC;oDAAI,WAAU;;sEACb,8UAAC,+TAAM,CAAC,IAAI;4DACV,WAAU;4DACV,YAAY;gEAAE,OAAO;4DAAK;;8EAE1B,8UAAC,gTAAG;oEAAC,WAAU;;;;;;gEACd,KAAK,QAAQ;;;;;;;sEAEhB,8UAAC;4DAAK,WAAU;;8EACd,8UAAC,sTAAK;oEAAC,WAAU;;;;;;gEAChB,KAAK,QAAQ;;;;;;;;;;;;;8DAKlB,8UAAC,+TAAM,CAAC,EAAE;oDACR,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;8DAEzB,KAAK,KAAK;;;;;;8DAIb,8UAAC;oDAAE,WAAU;8DACV,KAAK,OAAO;;;;;;8DAIf,8UAAC;oDAAI,WAAU;;sEACb,8UAAC;4DAAI,WAAU;;8EACb,8UAAC,mTAAI;oEAAC,WAAU;;;;;;gEACf,KAAK,MAAM;;;;;;;sEAEd,8UAAC;4DAAI,WAAU;;8EACb,8UAAC,+TAAQ;oEAAC,WAAU;;;;;;gEACnB,KAAK,IAAI;;;;;;;;;;;;;8DAKd,8UAAC,+TAAM,CAAC,GAAG;oDACT,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;8DAExB,cAAA,8UAAC,0JAAM;wDAAC,WAAU;;4DAA+G;0EAE/H,8UAAC,+TAAM,CAAC,GAAG;gEACT,WAAU;gEACV,YAAY;oEAAE,GAAG;gEAAE;gEACnB,YAAY;oEAAE,UAAU;gEAAI;0EAE5B,cAAA,8UAAC,yUAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAjGzB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BA6GtB,8UAAC;gBAAQ,WAAU;0BACjB,cAAA,8UAAC;oBAAI,WAAU;8BACb,cAAA,8UAAC,+TAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAQ;wBACR,aAAY;wBACZ,UAAU,yJAAe,CAAC,QAAQ;wBAClC,UAAU,kJAAQ;;0CAElB,8UAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8UAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAK1C,8UAAC,+TAAM,CAAC,GAAG;gCACT,WAAU;gCACV,UAAU,0JAAgB;gCAC1B,SAAQ;gCACR,aAAY;gCACZ,UAAU,yJAAe,CAAC,QAAQ;;kDAElC,8UAAC,+TAAM,CAAC,KAAK;wCACX,MAAK;wCACL,aAAY;wCACZ,WAAU;wCACV,UAAU,qJAAW;wCACrB,YAAY;4CAAE,OAAO;wCAAK;;;;;;kDAE5B,8UAAC,+TAAM,CAAC,GAAG;wCACT,UAAU,qJAAW;wCACrB,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,8UAAC,0JAAM;4CAAC,WAAU;;gDAAsH;8DAEtI,8UAAC,yUAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC;KA7LgB", "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/blog/blog-client.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { ChevronRight, Home } from \"lucide-react\";\nimport { BlogSection } from \"@/components/blog-section\";\nimport { heroTitle, heroSubtitle, pageTransition, animationConfig, fadeInUp } from \"@/lib/animations\";\n\nexport function BlogPageClient() {\n  return (\n    <motion.div\n      className=\"min-h-screen overflow-hidden\"\n      initial=\"hidden\"\n      animate=\"visible\"\n      exit=\"exit\"\n      variants={pageTransition}\n    >\n      {/* Hero Section */}\n      <section className=\"relative bg-gradient-to-r from-via-primary via-via-primary-light to-via-primary text-white py-24 lg:py-32 overflow-hidden\">\n        {/* Background Pattern */}\n        <motion.div\n          className=\"absolute inset-0 opacity-10\"\n          style={{\n            backgroundImage: \"url('data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')\"\n          }}\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 0.1 }}\n          transition={{ duration: 1, delay: 0.5 }}\n        />\n        \n        {/* Gradient Overlay */}\n        <div className=\"absolute inset-0 bg-black/20\"></div>\n        \n        {/* Content */}\n        <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <motion.h1\n            className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight\"\n            initial=\"hidden\"\n            animate=\"visible\"\n            variants={heroTitle}\n          >\n            Latest News &\n            <br />\n            <span className=\"text-blue-200\">Insights</span>\n          </motion.h1>\n\n          <motion.p\n            className=\"text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto\"\n            initial=\"hidden\"\n            animate=\"visible\"\n            variants={heroSubtitle}\n          >\n            Expert insights, business tips, and industry trends to help your business thrive in today's competitive landscape\n          </motion.p>\n        </div>\n      </section>\n\n      {/* Breadcrumb Navigation */}\n      <motion.section\n        className=\"bg-gray-50 py-4 border-b border-gray-200\"\n        initial=\"hidden\"\n        animate=\"visible\"\n        variants={fadeInUp}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <nav className=\"flex items-center space-x-2 text-sm\">\n            <Link\n              href=\"/\"\n              className=\"flex items-center text-gray-500 hover:text-via-primary transition-colors duration-200\"\n            >\n              <Home className=\"h-4 w-4 mr-1\" />\n              Home\n            </Link>\n            <ChevronRight className=\"h-4 w-4 text-gray-400\" />\n            <span className=\"text-via-primary font-medium\">Blog</span>\n          </nav>\n        </div>\n      </motion.section>\n\n      {/* Blog Section */}\n      <BlogSection />\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,qBACE,8UAAC,+TAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAQ;QACR,SAAQ;QACR,MAAK;QACL,UAAU,wJAAc;;0BAGxB,8UAAC;gBAAQ,WAAU;;kCAEjB,8UAAC,+TAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,iBAAiB;wBACnB;wBACA,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAI;wBACxB,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAI;;;;;;kCAIxC,8UAAC;wBAAI,WAAU;;;;;;kCAGf,8UAAC;wBAAI,WAAU;;0CACb,8UAAC,+TAAM,CAAC,EAAE;gCACR,WAAU;gCACV,SAAQ;gCACR,SAAQ;gCACR,UAAU,mJAAS;;oCACpB;kDAEC,8UAAC;;;;;kDACD,8UAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAGlC,8UAAC,+TAAM,CAAC,CAAC;gCACP,WAAU;gCACV,SAAQ;gCACR,SAAQ;gCACR,UAAU,sJAAY;0CACvB;;;;;;;;;;;;;;;;;;0BAOL,8UAAC,+TAAM,CAAC,OAAO;gBACb,WAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,UAAU,kJAAQ;0BAElB,cAAA,8UAAC;oBAAI,WAAU;8BACb,cAAA,8UAAC;wBAAI,WAAU;;0CACb,8UAAC,2TAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,8UAAC,oTAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,8UAAC,+UAAY;gCAAC,WAAU;;;;;;0CACxB,8UAAC;gCAAK,WAAU;0CAA+B;;;;;;;;;;;;;;;;;;;;;;0BAMrD,8UAAC,kKAAW;;;;;;;;;;;AAGlB;KA3EgB", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/chevron-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }]];\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('chevron-right', __iconNode);\n\nexport default ChevronRight;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA;AAa9E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 782, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/house.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/house.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8', key: '5wwlr5' }],\n  [\n    'path',\n    {\n      d: 'M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z',\n      key: '1d0kgt',\n    },\n  ],\n];\n\n/**\n * @component @name House\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMjF2LThhMSAxIDAgMCAwLTEtMWgtNGExIDEgMCAwIDAtMSAxdjgiIC8+CiAgPHBhdGggZD0iTTMgMTBhMiAyIDAgMCAxIC43MDktMS41MjhsNy01Ljk5OWEyIDIgMCAwIDEgMi41ODIgMGw3IDUuOTk5QTIgMiAwIDAgMSAyMSAxMHY5YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0yeiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/house\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst House = createLucideIcon('house', __iconNode);\n\nexport default House;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3E;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/calendar.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/calendar.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 2v4', key: '1cmpym' }],\n  ['path', { d: 'M16 2v4', key: '4m81vk' }],\n  ['rect', { width: '18', height: '18', x: '3', y: '4', rx: '2', key: '1hopcy' }],\n  ['path', { d: 'M3 10h18', key: '8toen8' }],\n];\n\n/**\n * @component @name Calendar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAydjQiIC8+CiAgPHBhdGggZD0iTTE2IDJ2NCIgLz4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik0zIDEwaDE4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/calendar\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calendar = createLucideIcon('calendar', __iconNode);\n\nexport default Calendar;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/user.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/user.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2', key: '975kel' }],\n  ['circle', { cx: '12', cy: '7', r: '4', key: '17ys0d' }],\n];\n\n/**\n * @component @name User\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMjF2LTJhNCA0IDAgMCAwLTQtNEg5YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst User = createLucideIcon('user', __iconNode);\n\nexport default User;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/arrow-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n];\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('arrow-right', __iconNode);\n\nexport default ArrowRight;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/clock.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/tag.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/tag.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z',\n      key: 'vktsd0',\n    },\n  ],\n  ['circle', { cx: '7.5', cy: '7.5', r: '.5', fill: 'currentColor', key: 'kqv944' }],\n];\n\n/**\n * @component @name Tag\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuNTg2IDIuNTg2QTIgMiAwIDAgMCAxMS4xNzIgMkg0YTIgMiAwIDAgMC0yIDJ2Ny4xNzJhMiAyIDAgMCAwIC41ODYgMS40MTRsOC43MDQgOC43MDRhMi40MjYgMi40MjYgMCAwIDAgMy40MiAwbDYuNTgtNi41OGEyLjQyNiAyLjQyNiAwIDAgMCAwLTMuNDJ6IiAvPgogIDxjaXJjbGUgY3g9IjcuNSIgY3k9IjcuNSIgcj0iLjUiIGZpbGw9ImN1cnJlbnRDb2xvciIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/tag\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Tag = createLucideIcon('tag', __iconNode);\n\nexport default Tag;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAE;YAAA,CAAA,CAAA,EAAI,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACnF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}