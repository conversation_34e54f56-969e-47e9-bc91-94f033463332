{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about/about-hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { heroTitle } from \"@/lib/animations\";\n\nexport function AboutHero() {\n  return (\n    <section \n      className=\"relative bg-primary text-white py-24 lg:py-32 overflow-hidden\"\n      style={{\n        backgroundImage: \"url('https://viatosuccess.com/wp-content/uploads/2021/01/about_us-header-bg_img.jpg')\",\n        backgroundPosition: \"center\",\n        backgroundRepeat: \"no-repeat\",\n        backgroundSize: \"cover\"\n      }}\n    >\n      {/* Blue Overlay */}\n      <motion.div\n        className=\"absolute inset-0 bg-primary bg-opacity-80\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1 }}\n      />\n\n      {/* Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <motion.h1\n            className=\"text-4xl md:text-5xl lg:text-6xl font-bold\"\n            initial=\"hidden\"\n            animate=\"visible\"\n            variants={heroTitle}\n          >\n            About us\n          </motion.h1>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKO,SAAS;IACd,qBACE,+XAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB;YACjB,oBAAoB;YACpB,kBAAkB;YAClB,gBAAgB;QAClB;;0BAGA,+XAAC,4TAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;gBAAE;;;;;;0BAI5B,+XAAC;gBAAI,WAAU;0BACb,cAAA,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC,4TAAM,CAAC,EAAE;wBACR,WAAU;wBACV,SAAQ;wBACR,SAAQ;wBACR,UAAU,gJAAS;kCACpB;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about/company-story.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\nimport { motion } from \"framer-motion\";\nimport { fadeInLeft, fadeInRight, animationConfig } from \"@/lib/animations\";\n\nexport function CompanyStory() {\n  return (\n    <section className=\"py-16 bg-white border-b border-gray-200 overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <motion.div\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={animationConfig.viewport}\n            variants={fadeInLeft}\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n              Via Executive Suites\n            </h2>\n\n            <div className=\"prose prose-lg text-gray-600\">\n              <p className=\"leading-relaxed\">\n                VIA Executive Suites delivers affordable and professional workspace solutions for startups, small businesses, satellite offices, and freelancers through four all-inclusive business centers and an array of shared amenities. At VIA Executive Suites, businesses find everything they need to succeed, including networking opportunities, dedicated personnel, state-of-the-art office space, front desk services, break rooms, mediation rooms, flexible leases, and office furniture rental. With every convenience available in each of our 4 business centers, we make better work achievable throughout the Valley.\n              </p>\n            </div>\n          </motion.div>\n          \n          {/* Image */}\n          <motion.div\n            className=\"relative\"\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={animationConfig.viewport}\n            variants={fadeInRight}\n          >\n            <motion.div\n              className=\"aspect-w-4 aspect-h-3 rounded-lg overflow-hidden shadow-xl\"\n              whileHover={{\n                scale: 1.02,\n                transition: { duration: 0.3 }\n              }}\n            >\n              <Image\n                src=\"https://viatosuccess.com/wp-content/uploads/2021/01/about_us-section_1-img_1.jpg\"\n                alt=\"Modern office space at Via Executive Suites\"\n                width={600}\n                height={400}\n                className=\"w-full h-full object-cover\"\n              />\n            </motion.div>\n\n            {/* Decorative elements */}\n            <motion.div\n              className=\"absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full -z-10\"\n              animate={{\n                y: [0, -10, 0],\n                transition: {\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              }}\n            />\n            <motion.div\n              className=\"absolute -bottom-4 -left-4 w-32 h-32 bg-blue-100 rounded-full -z-10\"\n              animate={{\n                y: [0, 10, 0],\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              }}\n            />\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,qBACE,+XAAC;QAAQ,WAAU;kBACjB,cAAA,+XAAC;YAAI,WAAU;sBACb,cAAA,+XAAC;gBAAI,WAAU;;kCAEb,+XAAC,4TAAM,CAAC,GAAG;wBACT,SAAQ;wBACR,aAAY;wBACZ,UAAU,sJAAe,CAAC,QAAQ;wBAClC,UAAU,iJAAU;;0CAEpB,+XAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAIlE,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAE,WAAU;8CAAkB;;;;;;;;;;;;;;;;;kCAOnC,+XAAC,4TAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAQ;wBACR,aAAY;wBACZ,UAAU,sJAAe,CAAC,QAAQ;wBAClC,UAAU,kJAAW;;0CAErB,+XAAC,4TAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCACV,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;0CAEA,cAAA,+XAAC,yRAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAKd,+XAAC,4TAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;oCACd,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;gCACF;;;;;;0CAEF,+XAAC,4TAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;oCACb,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;gCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className,\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nInput.displayName = \"Input\";\n\nexport { Input };\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,oWAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,+XAAC;QACC,MAAM;QACN,WAAW,IAAA,oIAAE,EACX,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/ui/label.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n);\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n));\nLabel.displayName = LabelPrimitive.Root.displayName;\n\nexport { Label };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,IAAA,oQAAG,EACvB;AAGF,MAAM,sBAAQ,oWAAgB,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,wSAAmB;QAClB,KAAK;QACL,WAAW,IAAA,oIAAE,EAAC,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,wSAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAIA,MAAM,yBAAW,oWAAgB,CAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,+XAAC;QACC,WAAW,IAAA,oIAAE,EACX,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about/about-contact-form.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\n\nexport function AboutContactForm() {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    phone: \"\",\n    company: \"\",\n    message: \"\"\n  });\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Handle form submission here\n    console.log(\"About form submitted:\", formData);\n  };\n\n  return (\n    <section \n      className=\"py-16 bg-cover bg-center bg-no-repeat relative\"\n      style={{\n        backgroundImage: \"url('https://viatosuccess.com/wp-content/uploads/2021/01/home-section_6-bg_img_1.jpg')\"\n      }}\n    >\n      {/* Overlay */}\n      <div className=\"absolute inset-0 bg-black bg-opacity-50\"></div>\n      \n      <div className=\"relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n            Get Your Free Consultation Today\n          </h2>\n          <p className=\"text-lg text-gray-200\">\n            Reach us at any of our four locations. Fill out the form below and one of our representatives will call you back ASAP.\n          </p>\n        </div>\n\n        {/* Contact Form */}\n        <div className=\"bg-white rounded-lg shadow-xl p-8 max-w-2xl mx-auto\">\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"name\" className=\"text-gray-700\">Full Name *</Label>\n                <Input\n                  id=\"name\"\n                  name=\"name\"\n                  type=\"text\"\n                  required\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  className=\"mt-1\"\n                  placeholder=\"Your full name\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"email\" className=\"text-gray-700\">Email *</Label>\n                <Input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  required\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  className=\"mt-1\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <Label htmlFor=\"phone\" className=\"text-gray-700\">Phone Number</Label>\n                <Input\n                  id=\"phone\"\n                  name=\"phone\"\n                  type=\"tel\"\n                  value={formData.phone}\n                  onChange={handleInputChange}\n                  className=\"mt-1\"\n                  placeholder=\"(*************\"\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"company\" className=\"text-gray-700\">Company</Label>\n                <Input\n                  id=\"company\"\n                  name=\"company\"\n                  type=\"text\"\n                  value={formData.company}\n                  onChange={handleInputChange}\n                  className=\"mt-1\"\n                  placeholder=\"Your company name\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <Label htmlFor=\"message\" className=\"text-gray-700\">Message</Label>\n              <Textarea\n                id=\"message\"\n                name=\"message\"\n                rows={4}\n                value={formData.message}\n                onChange={handleInputChange}\n                className=\"mt-1\"\n                placeholder=\"Tell us about your office space needs...\"\n              />\n            </div>\n\n            <Button \n              type=\"submit\"\n              size=\"lg\"\n              className=\"w-full bg-primary hover:bg-primary/90 text-white py-3 text-lg font-semibold rounded-md transition-all duration-300\"\n            >\n              Send Message\n            </Button>\n          </form>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,kWAAQ,EAAC;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,yBAAyB;IACvC;IAEA,qBACE,+XAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB;QACnB;;0BAGA,+XAAC;gBAAI,WAAU;;;;;;0BAEf,+XAAC;gBAAI,WAAU;;kCACb,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,+XAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAMvC,+XAAC;wBAAI,WAAU;kCACb,cAAA,+XAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;;8DACC,+XAAC,qJAAK;oDAAC,SAAQ;oDAAO,WAAU;8DAAgB;;;;;;8DAChD,+XAAC,qJAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,QAAQ;oDACR,OAAO,SAAS,IAAI;oDACpB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAGhB,+XAAC;;8DACC,+XAAC,qJAAK;oDAAC,SAAQ;oDAAQ,WAAU;8DAAgB;;;;;;8DACjD,+XAAC,qJAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,QAAQ;oDACR,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,+XAAC;oCAAI,WAAU;;sDACb,+XAAC;;8DACC,+XAAC,qJAAK;oDAAC,SAAQ;oDAAQ,WAAU;8DAAgB;;;;;;8DACjD,+XAAC,qJAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAGhB,+XAAC;;8DACC,+XAAC,qJAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAgB;;;;;;8DACnD,+XAAC,qJAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,+XAAC;;sDACC,+XAAC,qJAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAgB;;;;;;sDACnD,+XAAC,2JAAQ;4CACP,IAAG;4CACH,MAAK;4CACL,MAAM;4CACN,OAAO,SAAS,OAAO;4CACvB,UAAU;4CACV,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,+XAAC,uJAAM;oCACL,MAAK;oCACL,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/%40radix-ui%2Breact-primitive%402_d8cfd13fd91704f6b3f9497ef3b6609e/node_modules/%40radix-ui/react-primitive/src/primitive.tsx"], "sourcesContent": ["import * as React from 'react';\nimport * as ReactDOM from 'react-dom';\nimport { createSlot } from '@radix-ui/react-slot';\n\nconst NODES = [\n  'a',\n  'button',\n  'div',\n  'form',\n  'h2',\n  'h3',\n  'img',\n  'input',\n  'label',\n  'li',\n  'nav',\n  'ol',\n  'p',\n  'select',\n  'span',\n  'svg',\n  'ul',\n] as const;\n\ntype Primitives = { [E in (typeof NODES)[number]]: PrimitiveForwardRefComponent<E> };\ntype PrimitivePropsWithRef<E extends React.ElementType> = React.ComponentPropsWithRef<E> & {\n  asChild?: boolean;\n};\n\ninterface PrimitiveForwardRefComponent<E extends React.ElementType>\n  extends React.ForwardRefExoticComponent<PrimitivePropsWithRef<E>> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Primitive\n * -----------------------------------------------------------------------------------------------*/\n\nconst Primitive = NODES.reduce((primitive, node) => {\n  const Slot = createSlot(`Primitive.${node}`);\n  const Node = React.forwardRef((props: PrimitivePropsWithRef<typeof node>, forwardedRef: any) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp: any = asChild ? Slot : node;\n\n    if (typeof window !== 'undefined') {\n      (window as any)[Symbol.for('radix-ui')] = true;\n    }\n\n    return <Comp {...primitiveProps} ref={forwardedRef} />;\n  });\n\n  Node.displayName = `Primitive.${node}`;\n\n  return { ...primitive, [node]: Node };\n}, {} as Primitives);\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Flush custom event dispatch\n * https://github.com/radix-ui/primitives/pull/1378\n *\n * React batches *all* event handlers since version 18, this introduces certain considerations when using custom event types.\n *\n * Internally, React prioritises events in the following order:\n *  - discrete\n *  - continuous\n *  - default\n *\n * https://github.com/facebook/react/blob/a8a4742f1c54493df00da648a3f9d26e3db9c8b5/packages/react-dom/src/events/ReactDOMEventListener.js#L294-L350\n *\n * `discrete` is an  important distinction as updates within these events are applied immediately.\n * React however, is not able to infer the priority of custom event types due to how they are detected internally.\n * Because of this, it's possible for updates from custom events to be unexpectedly batched when\n * dispatched by another `discrete` event.\n *\n * In order to ensure that updates from custom events are applied predictably, we need to manually flush the batch.\n * This utility should be used when dispatching a custom event from within another `discrete` event, this utility\n * is not necessary when dispatching known event types, or if dispatching a custom type inside a non-discrete event.\n * For example:\n *\n * dispatching a known click 👎\n * target.dispatchEvent(new Event(‘click’))\n *\n * dispatching a custom type within a non-discrete event 👎\n * onScroll={(event) => event.target.dispatchEvent(new CustomEvent(‘customType’))}\n *\n * dispatching a custom type within a `discrete` event 👍\n * onPointerDown={(event) => dispatchDiscreteCustomEvent(event.target, new CustomEvent(‘customType’))}\n *\n * Note: though React classifies `focus`, `focusin` and `focusout` events as `discrete`, it's  not recommended to use\n * this utility with them. This is because it's possible for those handlers to be called implicitly during render\n * e.g. when focus is within a component as it is unmounted, or when managing focus on mount.\n */\n\nfunction dispatchDiscreteCustomEvent<E extends CustomEvent>(target: E['target'], event: E) {\n  if (target) ReactDOM.flushSync(() => target.dispatchEvent(event));\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Primitive;\n\nexport {\n  Primitive,\n  //\n  Root,\n  //\n  dispatchDiscreteCustomEvent,\n};\nexport type { PrimitivePropsWithRef };\n"], "names": [], "mappings": ";;;;;;;;;AAAA,YAAY,WAAW;AACvB,YAAY,cAAc;AAC1B,SAAS,kBAAkB;AA4ChB;;;;;AA1CX,IAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAcA,IAAM,YAAY,MAAM,MAAA,CAAO,CAAC,WAAW,SAAS;IAClD,MAAM,WAAO,qUAAA,EAAW,CAAA,UAAA,EAAa,IAAI,EAAE;IAC3C,MAAM,OAAa,oWAAA,CAAW,CAAC,OAA2C,iBAAsB;QAC9F,MAAM,EAAE,OAAA,EAAS,GAAG,eAAe,CAAA,GAAI;QACvC,MAAM,OAAY,UAAU,OAAO;QAEnC,IAAI,OAAO,WAAW,aAAa;;QAInC,OAAO,aAAA,GAAA,IAAA,+WAAA,EAAC,MAAA;YAAM,GAAG,cAAA;YAAgB,KAAK;QAAA,CAAc;IACtD,CAAC;IAED,KAAK,WAAA,GAAc,CAAA,UAAA,EAAa,IAAI,EAAA;IAEpC,OAAO;QAAE,GAAG,SAAA;QAAW,CAAC,IAAI,CAAA,EAAG;IAAK;AACtC,GAAG,CAAC,CAAe;AA2CnB,SAAS,4BAAmD,MAAA,EAAqB,KAAA,EAAU;IACzF,IAAI,OAAQ,CAAS,0WAAA,CAAU,IAAM,OAAO,aAAA,CAAc,KAAK,CAAC;AAClE;AAIA,IAAM,OAAO", "debugId": null}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/%40radix-ui%2Breact-label%402.1.7_490c3f10ee65aaea0fa95cfecfcd415a/node_modules/%40radix-ui/react-label/src/label.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Label\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Label';\n\ntype LabelElement = React.ComponentRef<typeof Primitive.label>;\ntype PrimitiveLabelProps = React.ComponentPropsWithoutRef<typeof Primitive.label>;\ninterface LabelProps extends PrimitiveLabelProps {}\n\nconst Label = React.forwardRef<LabelElement, LabelProps>((props, forwardedRef) => {\n  return (\n    <Primitive.label\n      {...props}\n      ref={forwardedRef}\n      onMouseDown={(event) => {\n        // only prevent text selection if clicking inside the label itself\n        const target = event.target as HTMLElement;\n        if (target.closest('button, input, select, textarea')) return;\n\n        props.onMouseDown?.(event);\n        // prevent text selection when double clicking label\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }}\n    />\n  );\n});\n\nLabel.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Label;\n\nexport {\n  Label,\n  //\n  Root,\n};\nexport type { LabelProps };\n"], "names": [], "mappings": ";;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,iBAAiB;AActB;;;;;AARJ,IAAM,OAAO;AAMb,IAAM,QAAc,oWAAA,CAAqC,CAAC,OAAO,iBAAiB;IAChF,OACE,aAAA,GAAA,IAAA,+WAAA,EAAC,2SAAA,CAAU,KAAA,EAAV;QACE,GAAG,KAAA;QACJ,KAAK;QACL,aAAa,CAAC,UAAU;YAEtB,MAAM,SAAS,MAAM,MAAA;YACrB,IAAI,OAAO,OAAA,CAAQ,iCAAiC,EAAG,CAAA;YAEvD,MAAM,WAAA,GAAc,KAAK;YAEzB,IAAI,CAAC,MAAM,gBAAA,IAAoB,MAAM,MAAA,GAAS,EAAG,CAAA,MAAM,cAAA,CAAe;QACxE;IAAA;AAGN,CAAC;AAED,MAAM,WAAA,GAAc;AAIpB,IAAM,OAAO", "debugId": null}}]}