{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/blog/blog-client.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BlogPageClient = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPageClient() from the server but BlogPageClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/app/blog/blog-client.tsx <module evaluation>\",\n    \"BlogPageClient\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,iBAAiB,IAAA,yZAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,sEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/blog/blog-client.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BlogPageClient = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPageClient() from the server but BlogPageClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Desktop/via/app/blog/blog-client.tsx\",\n    \"BlogPageClient\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,iBAAiB,IAAA,yZAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/blog/page.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\r\nimport { BlogPageClient } from \"./blog-client\";\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Latest News & Insights - Via Executive Suites\",\r\n  description: \"Stay updated with the latest news, insights, and tips for business success from Via Executive Suites. Expert advice on office spaces, entrepreneurship, and business growth.\",\r\n  keywords: \"business blog, office space tips, entrepreneurship, business insights, Rio Grande Valley, executive suites, virtual offices, workspace trends\",\r\n  openGraph: {\r\n    title: \"Latest News & Insights - Via Executive Suites\",\r\n    description: \"Stay updated with the latest news, insights, and tips for business success from Via Executive Suites.\",\r\n    type: \"website\",\r\n    locale: \"en_US\",\r\n  },\r\n};\r\n\r\nexport default function BlogPage() {\r\n  return <BlogPageClient />;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AACA;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;AACF;AAEe,SAAS;IACtB,qBAAO,+XAAC,kKAAc;;;;;AACxB", "debugId": null}}]}