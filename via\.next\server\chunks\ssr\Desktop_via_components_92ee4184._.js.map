{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/hero-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { heroAnimations, heroTitle, heroSubtitle, heroButtons, animationConfig } from \"@/lib/animations\";\n\nexport function HeroSection() {\n  return (\n    <section className=\"relative bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden\">\n      {/* Background Image Overlay */}\n      <motion.div\n        className=\"absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30\"\n        style={{\n          backgroundImage: \"url('https://via2success.com/wp-content/uploads/2021/01/home-hero-bg.jpg')\"\n        }}\n        initial={{ scale: 1.1 }}\n        animate={{ scale: 1 }}\n        transition={{ duration: 1.5, ease: \"easeOut\" }}\n      />\n      \n      {/* Content */}\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32 overflow-hidden\">\n        <motion.div\n          className=\"text-center\"\n          initial=\"hidden\"\n          animate=\"visible\"\n          variants={heroAnimations}\n          viewport={animationConfig.viewport}\n        >\n          <motion.h1\n            className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight\"\n            variants={heroTitle}\n          >\n            Modern Executive\n            <br />\n            <span className=\"text-blue-400\">Office Suites</span>\n          </motion.h1>\n\n          <motion.p\n            className=\"text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto\"\n            variants={heroSubtitle}\n          >\n            Office Workspace Solutions!\n          </motion.p>\n\n          <motion.div\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n            variants={heroButtons}\n          >\n            <Link href=\"/locations\">\n              <motion.div\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <Button\n                  size=\"lg\"\n                  className=\"bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg font-semibold rounded-md transition-all duration-300\"\n                >\n                  See All Locations\n                </Button>\n              </motion.div>\n            </Link>\n\n            <Link href=\"/connect\">\n              <motion.div\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <Button\n                  variant=\"outline\"\n                  size=\"lg\"\n                  className=\"px-8 py-3 text-lg font-semibold border-2 border-white text-white hover:bg-white hover:text-gray-900 transition-all duration-300\"\n                >\n                  Schedule a Tour\n                </Button>\n              </motion.div>\n            </Link>\n          </motion.div>\n        </motion.div>\n      </div>\n      \n      {/* Bottom Wave */}\n      <div className=\"absolute bottom-0 left-0 right-0\">\n        <svg \n          className=\"w-full h-12 text-white\" \n          viewBox=\"0 0 1200 120\" \n          preserveAspectRatio=\"none\"\n        >\n          <path \n            d=\"M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z\" \n            opacity=\".25\" \n            fill=\"currentColor\"\n          />\n          <path \n            d=\"M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z\" \n            opacity=\".5\" \n            fill=\"currentColor\"\n          />\n          <path \n            d=\"M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z\" \n            fill=\"currentColor\"\n          />\n        </svg>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOO,SAAS;IACd,qBACE,+XAAC;QAAQ,WAAU;;0BAEjB,+XAAC,4TAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,iBAAiB;gBACnB;gBACA,SAAS;oBAAE,OAAO;gBAAI;gBACtB,SAAS;oBAAE,OAAO;gBAAE;gBACpB,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAU;;;;;;0BAI/C,+XAAC;gBAAI,WAAU;0BACb,cAAA,+XAAC,4TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,UAAU,qJAAc;oBACxB,UAAU,sJAAe,CAAC,QAAQ;;sCAElC,+XAAC,4TAAM,CAAC,EAAE;4BACR,WAAU;4BACV,UAAU,gJAAS;;gCACpB;8CAEC,+XAAC;;;;;8CACD,+XAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAGlC,+XAAC,4TAAM,CAAC,CAAC;4BACP,WAAU;4BACV,UAAU,mJAAY;sCACvB;;;;;;sCAID,+XAAC,4TAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU,kJAAW;;8CAErB,+XAAC,wTAAI;oCAAC,MAAK;8CACT,cAAA,+XAAC,4TAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,+XAAC,uJAAM;4CACL,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;8CAML,+XAAC,wTAAI;oCAAC,MAAK;8CACT,cAAA,+XAAC,4TAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,+XAAC,uJAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,+XAAC;gBAAI,WAAU;0BACb,cAAA,+XAAC;oBACC,WAAU;oBACV,SAAQ;oBACR,qBAAoB;;sCAEpB,+XAAC;4BACC,GAAE;4BACF,SAAQ;4BACR,MAAK;;;;;;sCAEP,+XAAC;4BACC,GAAE;4BACF,SAAQ;4BACR,MAAK;;;;;;sCAEP,+XAAC;4BACC,GAAE;4BACF,MAAK;;;;;;;;;;;;;;;;;;;;;;;AAMjB", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/features-grid.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Building2, Layers, MapPin, DollarSign, Users, TrendingUp, Network, Star } from \"lucide-react\";\nimport { fadeInUp, staggerContainer, staggerItem, animationConfig } from \"@/lib/animations\";\n\nconst features = [\n  {\n    icon: Building2,\n    title: \"Office Suites\",\n    description: \"Choose from four strategically located, all-inclusive office suites and business centers that include convenient common areas, state-of-the-art workspaces, and suitable prices for all budgets.\"\n  },\n  {\n    icon: Layers,\n    title: \"Workspace Variety\",\n    description: \"Choose from a variety of workspaces (including mobile executive suites) to customize your office environment and make your business more efficient.\"\n  },\n  {\n    icon: MapPin,\n    title: \"Conveniently Located\",\n    description: \"Work in the heart of the Arts District in McAllen; grow your business in downtown Edinburg; take your enterprise to new heights in La Costa, or relocate your company to McAllen. All of our locations are chosen for convenience and access.\"\n  },\n  {\n    icon: DollarSign,\n    title: \"Inclusive Pricing\",\n    description: \"Pay a single price to enjoy everything our workspaces have to offer. Enjoy a business address, waiting room, front desk staff, conference rooms, break rooms, wi-fi, amenities, and more, for a single affordable price.\"\n  },\n  {\n    icon: Users,\n    title: \"Dedicated Personnel\",\n    description: \"Bilingual staff provide professional and courteous service to make you and your guests feel welcome around the clock.\"\n  },\n  {\n    icon: TrendingUp,\n    title: \"Productivity\",\n    description: \"Help your team reach maximum productivity with quality services, fully furnished workspaces, state-of-the-art lighting, and ambient music.\"\n  },\n  {\n    icon: Network,\n    title: \"Networking\",\n    description: \"Build valuable partnerships with networking events aimed at generating business opportunities through communication and synergy.\"\n  },\n  {\n    icon: Star,\n    title: \"Added Values\",\n    description: \"Enjoy an array of amenities designed to create a more enjoyable workspace. Meditation rooms, complimentary Starbucks coffee, patio space, and more make better work possible in each of our locations.\"\n  }\n];\n\nexport function FeaturesGrid() {\n  return (\n    <section className=\"py-16 bg-white overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-12\"\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={animationConfig.viewport}\n          variants={fadeInUp}\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Your Perfect Office Spaces in the valley\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed\">\n            Create an easier path to business growth through Via Executive Suites. Designed to promote the success of local startups and economies,\n            our beautiful, professional, and affordable office spaces offer an array of possibilities to meet your every need.\n            From furnished private offices, business center and conference rooms, to coworking and virtual offices,\n            we provide a unique, dynamic solution to your office space rental needs.\n          </p>\n        </motion.div>\n\n        <motion.div\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\"\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={animationConfig.viewport}\n          variants={staggerContainer}\n        >\n          {features.map((feature, index) => {\n            const IconComponent = feature.icon;\n            return (\n              <motion.div\n                key={index}\n                className=\"text-center p-6 rounded-lg hover:shadow-lg transition-shadow duration-300 group\"\n                variants={staggerItem}\n                whileHover={{\n                  y: -8,\n                  transition: { duration: 0.2 }\n                }}\n              >\n                <motion.div\n                  className=\"inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4 group-hover:bg-primary/20 transition-colors duration-300\"\n                  whileHover={{\n                    scale: 1.1,\n                    rotate: 5,\n                    transition: { duration: 0.2 }\n                  }}\n                >\n                  <IconComponent className=\"w-8 h-8 text-primary\" />\n                </motion.div>\n\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">\n                  {feature.title}\n                </h3>\n\n                <p className=\"text-gray-600 text-sm leading-relaxed\">\n                  {feature.description}\n                </p>\n              </motion.div>\n            );\n          })}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,WAAW;IACf;QACE,MAAM,mUAAS;QACf,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sTAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,0TAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sUAAU;QAChB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,mTAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sUAAU;QAChB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,yTAAO;QACb,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,gTAAI;QACV,OAAO;QACP,aAAa;IACf;CACD;AAEM,SAAS;IACd,qBACE,+XAAC;QAAQ,WAAU;kBACjB,cAAA,+XAAC;YAAI,WAAU;;8BACb,+XAAC,4TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU,sJAAe,CAAC,QAAQ;oBAClC,UAAU,+IAAQ;;sCAElB,+XAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,+XAAC;4BAAE,WAAU;sCAA0D;;;;;;;;;;;;8BAQzE,+XAAC,4TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU,sJAAe,CAAC,QAAQ;oBAClC,UAAU,uJAAgB;8BAEzB,SAAS,GAAG,CAAC,CAAC,SAAS;wBACtB,MAAM,gBAAgB,QAAQ,IAAI;wBAClC,qBACE,+XAAC,4TAAM,CAAC,GAAG;4BAET,WAAU;4BACV,UAAU,kJAAW;4BACrB,YAAY;gCACV,GAAG,CAAC;gCACJ,YAAY;oCAAE,UAAU;gCAAI;4BAC9B;;8CAEA,+XAAC,4TAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCACV,OAAO;wCACP,QAAQ;wCACR,YAAY;4CAAE,UAAU;wCAAI;oCAC9B;8CAEA,cAAA,+XAAC;wCAAc,WAAU;;;;;;;;;;;8CAG3B,+XAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAGhB,+XAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAxBjB;;;;;oBA4BX;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/about-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { motion } from \"framer-motion\";\nimport { Button } from \"@/components/ui/button\";\nimport { fadeInLeft, fadeInRight, animationConfig } from \"@/lib/animations\";\n\nexport function AboutSection() {\n  return (\n    <section className=\"py-16 bg-gray-50 overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <motion.div\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={animationConfig.viewport}\n            variants={fadeInLeft}\n          >\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\n              Office Space for Rent\n            </h2>\n\n            <div className=\"prose prose-lg text-gray-600 mb-8\">\n              <p className=\"mb-4\">\n                Via Executive Suites provides all-inclusive and customizable office space solutions that reduce overhead and costly rental expenses for small businesses. We take pride in helping Rio Grande Valley entrepreneurs and startups achieve success and strengthen the local economy by enhancing their enterprise, identity, and integrity.\n              </p>\n\n              <p className=\"text-lg font-medium text-gray-900\">\n                We don&apos;t just make work possible. We make better work achievable.\n              </p>\n            </div>\n\n            <Link href=\"/about\">\n              <motion.div\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <Button\n                  size=\"lg\"\n                  className=\"bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg font-semibold rounded-md transition-all duration-300\"\n                >\n                  About Via Executive Suites\n                </Button>\n              </motion.div>\n            </Link>\n          </motion.div>\n          \n          {/* Image */}\n          <motion.div\n            className=\"relative\"\n            initial=\"hidden\"\n            whileInView=\"visible\"\n            viewport={animationConfig.viewport}\n            variants={fadeInRight}\n          >\n            <motion.div\n              className=\"aspect-w-4 aspect-h-3 rounded-lg overflow-hidden shadow-xl\"\n              whileHover={{\n                scale: 1.02,\n                transition: { duration: 0.3 }\n              }}\n            >\n              <Image\n                src=\"https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg\"\n                alt=\"Modern office space interior\"\n                width={600}\n                height={400}\n                className=\"w-full h-full object-cover\"\n              />\n            </motion.div>\n\n            {/* Decorative elements */}\n            <motion.div\n              className=\"absolute -top-4 -left-4 w-24 h-24 bg-primary/10 rounded-full -z-10\"\n              animate={{\n                y: [0, -10, 0],\n                transition: {\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              }}\n            />\n            <motion.div\n              className=\"absolute -bottom-4 -right-4 w-32 h-32 bg-blue-100 rounded-full -z-10\"\n              animate={{\n                y: [0, 10, 0],\n                transition: {\n                  duration: 4,\n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }\n              }}\n            />\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,qBACE,+XAAC;QAAQ,WAAU;kBACjB,cAAA,+XAAC;YAAI,WAAU;sBACb,cAAA,+XAAC;gBAAI,WAAU;;kCAEb,+XAAC,4TAAM,CAAC,GAAG;wBACT,SAAQ;wBACR,aAAY;wBACZ,UAAU,sJAAe,CAAC,QAAQ;wBAClC,UAAU,iJAAU;;0CAEpB,+XAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAIlE,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;wCAAE,WAAU;kDAAO;;;;;;kDAIpB,+XAAC;wCAAE,WAAU;kDAAoC;;;;;;;;;;;;0CAKnD,+XAAC,wTAAI;gCAAC,MAAK;0CACT,cAAA,+XAAC,4TAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,+XAAC,uJAAM;wCACL,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;kCAQP,+XAAC,4TAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAQ;wBACR,aAAY;wBACZ,UAAU,sJAAe,CAAC,QAAQ;wBAClC,UAAU,kJAAW;;0CAErB,+XAAC,4TAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCACV,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;0CAEA,cAAA,+XAAC,yRAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAKd,+XAAC,4TAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;oCACd,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;gCACF;;;;;;0CAEF,+XAAC,4TAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;oCACb,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;gCACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/gallery-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { motion } from \"framer-motion\";\nimport { fadeInUp, staggerContainer, staggerItem, animationConfig } from \"@/lib/animations\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ExternalLink } from \"lucide-react\";\n\nconst galleryImages = [\n  {\n    src: \"https://via2success.com/wp-content/uploads/2021/01/home-section_4-img_1.jpg\",\n    alt: \"Modern office reception area\",\n    title: \"Reception Area\"\n  },\n  {\n    src: \"https://via2success.com/wp-content/uploads/2021/01/home-section_4-img_2.jpg\",\n    alt: \"Executive office suite\",\n    title: \"Executive Suite\"\n  },\n  {\n    src: \"https://via2success.com/wp-content/uploads/2021/01/home-section_4-img_3.jpg\",\n    alt: \"Conference room\",\n    title: \"Conference Room\"\n  },\n  {\n    src: \"https://via2success.com/wp-content/uploads/2021/01/home-section_4-img_4.jpg\",\n    alt: \"Coworking space\",\n    title: \"Coworking Space\"\n  },\n  {\n    src: \"https://via2success.com/wp-content/uploads/2021/01/home-section_4-img_5.jpg\",\n    alt: \"Private office\",\n    title: \"Private Office\"\n  },\n  {\n    src: \"https://via2success.com/wp-content/uploads/2021/01/home-section_4-img_6.jpg\",\n    alt: \"Break room\",\n    title: \"Break Room\"\n  }\n];\n\nexport function GallerySection() {\n  return (\n    <section className=\"py-16 bg-white overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-12\"\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={animationConfig.viewport}\n          variants={fadeInUp}\n        >\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Tour Your New Office\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n            Get a glimpse of the professional, all-inclusive executive workspaces available at each of our four convenient locations.\n          </p>\n        </motion.div>\n\n        {/* Image Grid */}\n        <motion.div\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12\"\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={animationConfig.viewport}\n          variants={staggerContainer}\n        >\n          {galleryImages.map((image, index) => (\n            <motion.div\n              key={index}\n              className=\"group relative overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-all duration-300\"\n              variants={staggerItem}\n              whileHover={{\n                y: -8,\n                scale: 1.02,\n                transition: { duration: 0.2 }\n              }}\n            >\n              <div className=\"aspect-w-4 aspect-h-3\">\n                <Image\n                  src={image.src}\n                  alt={image.alt}\n                  width={400}\n                  height={300}\n                  className=\"w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300\"\n                />\n              </div>\n\n              {/* Overlay */}\n              <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center\">\n                <motion.div\n                  className=\"text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                  initial={{ scale: 0.8 }}\n                  whileHover={{ scale: 1 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  <h3 className=\"text-lg font-semibold mb-2\">{image.title}</h3>\n                  <ExternalLink className=\"w-6 h-6 mx-auto\" />\n                </motion.div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Action Buttons */}\n        <motion.div\n          className=\"text-center space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center\"\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={animationConfig.viewport}\n          variants={fadeInUp}\n        >\n          <Link href=\"/locations\">\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <Button\n                size=\"lg\"\n                className=\"bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg font-semibold rounded-md transition-all duration-300\"\n              >\n                Tour Locations\n              </Button>\n            </motion.div>\n          </Link>\n\n          <Link href=\"/gallery\">\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                className=\"border-2 border-primary text-primary hover:bg-primary hover:text-white px-8 py-3 text-lg font-semibold rounded-md transition-all duration-300\"\n              >\n                See Full Gallery\n              </Button>\n            </motion.div>\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,gBAAgB;IACpB;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA;QACE,KAAK;QACL,KAAK;QACL,OAAO;IACT;CACD;AAEM,SAAS;IACd,qBACE,+XAAC;QAAQ,WAAU;kBACjB,cAAA,+XAAC;YAAI,WAAU;;8BACb,+XAAC,4TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU,sJAAe,CAAC,QAAQ;oBAClC,UAAU,+IAAQ;;sCAElB,+XAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,+XAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,+XAAC,4TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU,sJAAe,CAAC,QAAQ;oBAClC,UAAU,uJAAgB;8BAEzB,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,+XAAC,4TAAM,CAAC,GAAG;4BAET,WAAU;4BACV,UAAU,kJAAW;4BACrB,YAAY;gCACV,GAAG,CAAC;gCACJ,OAAO;gCACP,YAAY;oCAAE,UAAU;gCAAI;4BAC9B;;8CAEA,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC,yRAAK;wCACJ,KAAK,MAAM,GAAG;wCACd,KAAK,MAAM,GAAG;wCACd,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;8CAKd,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC,4TAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,OAAO;wCAAI;wCACtB,YAAY;4CAAE,OAAO;wCAAE;wCACvB,YAAY;4CAAE,UAAU;wCAAI;;0DAE5B,+XAAC;gDAAG,WAAU;0DAA8B,MAAM,KAAK;;;;;;0DACvD,+XAAC,4UAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;2BA5BvB;;;;;;;;;;8BAoCX,+XAAC,4TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU,sJAAe,CAAC,QAAQ;oBAClC,UAAU,+IAAQ;;sCAElB,+XAAC,wTAAI;4BAAC,MAAK;sCACT,cAAA,+XAAC,4TAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,+XAAC,uJAAM;oCACL,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;sCAML,+XAAC,wTAAI;4BAAC,MAAK;sCACT,cAAA,+XAAC,4TAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,+XAAC,uJAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 850, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className,\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nInput.displayName = \"Input\";\n\nexport { Input };\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,oWAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,+XAAC;QACC,MAAM;QACN,WAAW,IAAA,oIAAE,EACX,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/ui/label.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n);\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n));\nLabel.displayName = LabelPrimitive.Root.displayName;\n\nexport { Label };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,IAAA,oQAAG,EACvB;AAGF,MAAM,sBAAQ,oWAAgB,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,+XAAC,wSAAmB;QAClB,KAAK;QACL,WAAW,IAAA,oIAAE,EAAC,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,wSAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 909, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAIA,MAAM,yBAAW,oWAAgB,CAC/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,+XAAC;QACC,WAAW,IAAA,oIAAE,EACX,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 936, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/contact-form-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Phone, Mail, MapPin } from \"lucide-react\";\n\nexport function ContactFormSection() {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    phone: \"\",\n    company: \"\",\n    message: \"\"\n  });\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Handle form submission here\n    console.log(\"Form submitted:\", formData);\n  };\n\n  return (\n    <section \n      className=\"py-16 bg-cover bg-center bg-no-repeat relative\"\n      style={{\n        backgroundImage: \"url('https://via2success.com/wp-content/uploads/2021/01/home-section_6-bg_img_1.jpg')\"\n      }}\n    >\n      {/* Overlay */}\n      <div className=\"absolute inset-0 bg-black bg-opacity-50\"></div>\n      \n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n            Get Your Free Consultation Today\n          </h2>\n          <p className=\"text-lg text-gray-200 max-w-3xl mx-auto\">\n            Reach us at any of our four locations. Fill out the form below and one of our representatives will call you back ASAP.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          {/* Contact Form */}\n          <div className=\"bg-white rounded-lg shadow-xl p-8\">\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"name\" className=\"text-gray-700\">Full Name *</Label>\n                  <Input\n                    id=\"name\"\n                    name=\"name\"\n                    type=\"text\"\n                    required\n                    value={formData.name}\n                    onChange={handleInputChange}\n                    className=\"mt-1\"\n                    placeholder=\"Your full name\"\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"email\" className=\"text-gray-700\">Email *</Label>\n                  <Input\n                    id=\"email\"\n                    name=\"email\"\n                    type=\"email\"\n                    required\n                    value={formData.email}\n                    onChange={handleInputChange}\n                    className=\"mt-1\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <Label htmlFor=\"phone\" className=\"text-gray-700\">Phone Number</Label>\n                  <Input\n                    id=\"phone\"\n                    name=\"phone\"\n                    type=\"tel\"\n                    value={formData.phone}\n                    onChange={handleInputChange}\n                    className=\"mt-1\"\n                    placeholder=\"(*************\"\n                  />\n                </div>\n                <div>\n                  <Label htmlFor=\"company\" className=\"text-gray-700\">Company</Label>\n                  <Input\n                    id=\"company\"\n                    name=\"company\"\n                    type=\"text\"\n                    value={formData.company}\n                    onChange={handleInputChange}\n                    className=\"mt-1\"\n                    placeholder=\"Your company name\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <Label htmlFor=\"message\" className=\"text-gray-700\">Message</Label>\n                <Textarea\n                  id=\"message\"\n                  name=\"message\"\n                  rows={4}\n                  value={formData.message}\n                  onChange={handleInputChange}\n                  className=\"mt-1\"\n                  placeholder=\"Tell us about your office space needs...\"\n                />\n              </div>\n\n              <Button \n                type=\"submit\"\n                size=\"lg\"\n                className=\"w-full bg-primary hover:bg-primary/90 text-white py-3 text-lg font-semibold rounded-md transition-all duration-300\"\n              >\n                Send Message\n              </Button>\n            </form>\n          </div>\n\n          {/* Contact Information */}\n          <div className=\"text-white\">\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8 mb-8\">\n              <h3 className=\"text-2xl font-bold mb-6\">Contact Information</h3>\n              \n              <div className=\"space-y-4\">\n                <div className=\"flex items-center\">\n                  <Phone className=\"w-6 h-6 mr-4 text-blue-300\" />\n                  <div>\n                    <div className=\"font-semibold\">Main Office</div>\n                    <div className=\"text-gray-200\">(*************</div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-center\">\n                  <Mail className=\"w-6 h-6 mr-4 text-blue-300\" />\n                  <div>\n                    <div className=\"font-semibold\">Email</div>\n                    <div className=\"text-gray-200\"><EMAIL></div>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start\">\n                  <MapPin className=\"w-6 h-6 mr-4 text-blue-300 mt-1\" />\n                  <div>\n                    <div className=\"font-semibold\">Locations</div>\n                    <div className=\"text-gray-200 text-sm\">\n                      McAllen • Edinburg • Pharr\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-8\">\n              <h4 className=\"text-xl font-bold mb-4\">Business Hours</h4>\n              <div className=\"space-y-2 text-gray-200\">\n                <div className=\"flex justify-between\">\n                  <span>Monday - Friday:</span>\n                  <span>8:00 AM - 5:00 PM</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>Saturday:</span>\n                  <span>By Appointment</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span>Sunday:</span>\n                  <span>Closed</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,kWAAQ,EAAC;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,mBAAmB;IACjC;IAEA,qBACE,+XAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB;QACnB;;0BAGA,+XAAC;gBAAI,WAAU;;;;;;0BAEf,+XAAC;gBAAI,WAAU;;kCACb,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,+XAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAKzD,+XAAC;wBAAI,WAAU;;0CAEb,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;;sEACC,+XAAC,qJAAK;4DAAC,SAAQ;4DAAO,WAAU;sEAAgB;;;;;;sEAChD,+XAAC,qJAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,+XAAC;;sEACC,+XAAC,qJAAK;4DAAC,SAAQ;4DAAQ,WAAU;sEAAgB;;;;;;sEACjD,+XAAC,qJAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;;sEACC,+XAAC,qJAAK;4DAAC,SAAQ;4DAAQ,WAAU;sEAAgB;;;;;;sEACjD,+XAAC,qJAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,+XAAC;;sEACC,+XAAC,qJAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAgB;;;;;;sEACnD,+XAAC,qJAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,+XAAC;;8DACC,+XAAC,qJAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAgB;;;;;;8DACnD,+XAAC,2JAAQ;oDACP,IAAG;oDACH,MAAK;oDACL,MAAM;oDACN,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,+XAAC,uJAAM;4CACL,MAAK;4CACL,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAOL,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;wCAAI,WAAU;;0DACb,+XAAC;gDAAG,WAAU;0DAA0B;;;;;;0DAExC,+XAAC;gDAAI,WAAU;;kEACb,+XAAC;wDAAI,WAAU;;0EACb,+XAAC,mTAAK;gEAAC,WAAU;;;;;;0EACjB,+XAAC;;kFACC,+XAAC;wEAAI,WAAU;kFAAgB;;;;;;kFAC/B,+XAAC;wEAAI,WAAU;kFAAgB;;;;;;;;;;;;;;;;;;kEAInC,+XAAC;wDAAI,WAAU;;0EACb,+XAAC,gTAAI;gEAAC,WAAU;;;;;;0EAChB,+XAAC;;kFACC,+XAAC;wEAAI,WAAU;kFAAgB;;;;;;kFAC/B,+XAAC;wEAAI,WAAU;kFAAgB;;;;;;;;;;;;;;;;;;kEAInC,+XAAC;wDAAI,WAAU;;0EACb,+XAAC,0TAAM;gEAAC,WAAU;;;;;;0EAClB,+XAAC;;kFACC,+XAAC;wEAAI,WAAU;kFAAgB;;;;;;kFAC/B,+XAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/C,+XAAC;wCAAI,WAAU;;0DACb,+XAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,+XAAC;gDAAI,WAAU;;kEACb,+XAAC;wDAAI,WAAU;;0EACb,+XAAC;0EAAK;;;;;;0EACN,+XAAC;0EAAK;;;;;;;;;;;;kEAER,+XAAC;wDAAI,WAAU;;0EACb,+XAAC;0EAAK;;;;;;0EACN,+XAAC;0EAAK;;;;;;;;;;;;kEAER,+XAAC;wDAAI,WAAU;;0EACb,+XAAC;0EAAK;;;;;;0EACN,+XAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}, {"offset": {"line": 1491, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/locations-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { MapPin, Phone, Clock } from \"lucide-react\";\nimport {\n  fadeInUp,\n  fadeInDown,\n  scaleIn,\n  staggerContainer,\n  staggerItem,\n  animationConfig\n} from \"@/lib/animations\";\nimport { Location, LocationSlugs } from \"@/lib/types/location\";\n\nconst locations: Record<LocationSlugs, Location> = {\n  \"adbc\": {\n    name: \"ADBC\",\n    fullName: \"Via Executive Suites ADBC\",\n    address: \"813 N. Main St., McAllen, Texas 78501\",\n    phone: \"(*************\",\n    hours: \"Mon-Fri: 8:00 AM - 5:00 PM\",\n    email: \"<EMAIL>\",\n    coordinates: { lat: 26.212138647510635, lng: -98.23348265965645 },\n    description: \"Located in the heart of McAllen, our ADBC location offers premium office spaces with easy access to major highways and business districts.\",\n    features: [\n      \"Private office suites\",\n      \"Conference rooms\",\n      \"Reception services\",\n      \"High-speed internet\",\n      \"Free parking\",\n      \"Break room amenities\",\n      \"Mail handling\",\n      \"24/7 building access\"\n    ],\n    image: \"https://via2success.com/wp-content/uploads/2021/01/location-adbc.jpg\"\n  },\n  \"la-costa\": {\n    name: \"La Costa\",\n    fullName: \"Via Executive Suites La Costa\",\n    address: \"214 N 16th St, McAllen, Texas 78501\",\n    phone: \"(*************\",\n    hours: \"Mon-Fri: 8:00 AM - 5:00 PM\",\n    email: \"<EMAIL>\",\n    coordinates: { lat: 26.20654823351708, lng: -98.2359421731485 },\n    description: \"Our La Costa location provides modern office solutions in a vibrant business community, perfect for growing companies and entrepreneurs.\",\n    features: [\n      \"Flexible office spaces\",\n      \"Virtual office services\",\n      \"Meeting facilities\",\n      \"Business support services\",\n      \"Secure access\",\n      \"Professional environment\",\n      \"Networking opportunities\",\n      \"Convenient location\"\n    ],\n    image: \"https://via2success.com/wp-content/uploads/2021/01/location-la-costa.jpg\"\n  },\n  \"23rd\": {\n    name: \"23rd\",\n    fullName: \"Via Executive Suites 23rd\",\n    address: \"1821 N 23rd Street, McAllen, Texas 78501\",\n    phone: \"(*************\",\n    hours: \"Mon-Fri: 8:00 AM - 5:00 PM\",\n    email: \"<EMAIL>\",\n    coordinates: { lat: 26.22271557411775, lng: -98.24298783082033 },\n    description: \"The 23rd Street location offers spacious office environments with premium amenities, ideal for established businesses seeking professional workspace.\",\n    features: [\n      \"Executive office suites\",\n      \"Boardroom facilities\",\n      \"Premium amenities\",\n      \"Dedicated support staff\",\n      \"Advanced technology\",\n      \"Professional services\",\n      \"Business networking\",\n      \"Premium location\"\n    ],\n    image: \"https://via2success.com/wp-content/uploads/2021/01/location-23rd.jpg\"\n  },\n  \"edinburg\": {\n    name: \"Edinburg\",\n    fullName: \"Via Executive Suites Edinburg\",\n    address: \"1409 S 9th Ave, Edinburg, Texas 78539\",\n    phone: \"(*************\",\n    hours: \"Mon-Fri: 8:00 AM - 5:00 PM\",\n    email: \"<EMAIL>\",\n    coordinates: { lat: 26.28915674741617, lng: -98.16747817499945 },\n    description: \"Our Edinburg location serves the northern Rio Grande Valley with professional office solutions and comprehensive business support services.\",\n    features: [\n      \"Modern office spaces\",\n      \"Collaborative areas\",\n      \"Technology infrastructure\",\n      \"Business services\",\n      \"Community events\",\n      \"Professional development\",\n      \"Local partnerships\",\n      \"Strategic positioning\"\n    ],\n    image: \"https://via2success.com/wp-content/uploads/2021/01/location-edinburg.jpg\"\n  }\n};\n\nexport function LocationsSection() {\n  return (\n    <section className=\"py-16 bg-white overflow-hidden\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Animated Header */}\n        <motion.div\n          className=\"text-center mb-12\"\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={animationConfig.viewport}\n          variants={staggerContainer}\n        >\n          <motion.h2\n            className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\"\n            variants={fadeInDown}\n          >\n            Locations\n          </motion.h2>\n          <motion.p\n            className=\"text-lg text-gray-600 max-w-3xl mx-auto\"\n            variants={fadeInUp}\n          >\n            Our 4 locations are situated throughout the RGV for maximum convenience.\n          </motion.p>\n        </motion.div>\n\n        {/* Animated Map Placeholder */}\n        <motion.div\n          className=\"mb-12\"\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={animationConfig.viewport}\n          variants={scaleIn}\n        >\n          <motion.div\n            className=\"bg-gray-200 rounded-lg h-96 flex items-center justify-center overflow-hidden\"\n            whileHover={{\n              scale: 1.02,\n              transition: { duration: 0.3, ease: \"easeInOut\" }\n            }}\n          >\n            <motion.div\n              className=\"text-center text-gray-500\"\n              variants={fadeInUp}\n            >\n              <motion.div\n                whileHover={{\n                  scale: 1.1,\n                  rotate: 5,\n                  transition: { duration: 0.2 }\n                }}\n              >\n                <MapPin className=\"w-12 h-12 mx-auto mb-4\" />\n              </motion.div>\n              <p className=\"text-lg font-medium\">Interactive Map</p>\n              <p className=\"text-sm\">Map integration would be implemented here</p>\n            </motion.div>\n          </motion.div>\n        </motion.div>\n\n        {/* Animated Location Cards */}\n        <motion.div\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\"\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={animationConfig.viewport}\n          variants={staggerContainer}\n        >\n          {Object.entries(locations).map(([slug, location]) => (\n            <motion.div\n              key={slug}\n              className=\"bg-white border border-gray-200 rounded-lg shadow-md p-6 cursor-pointer\"\n              variants={staggerItem}\n              whileHover={{\n                y: -8,\n                scale: 1.02,\n                boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)\",\n                transition: {\n                  duration: 0.3,\n                  ease: \"easeInOut\"\n                }\n              }}\n              whileTap={{ scale: 0.98 }}\n            >\n              <motion.h3\n                className=\"text-xl font-semibold text-via-primary mb-4\"\n                whileHover={{\n                  scale: 1.05,\n                  transition: { duration: 0.2 }\n                }}\n              >\n                {location.name}\n              </motion.h3>\n\n              <div className=\"space-y-3 text-sm text-gray-600\">\n                <motion.div\n                  className=\"flex items-start\"\n                  whileHover={{ x: 4, transition: { duration: 0.2 } }}\n                >\n                  <MapPin className=\"w-4 h-4 mr-2 mt-0.5 text-via-primary flex-shrink-0\" />\n                  <div>\n                    <div className=\"font-medium text-gray-900\">{location.fullName}</div>\n                    <div>{location.address}</div>\n                  </div>\n                </motion.div>\n\n                <motion.div\n                  className=\"flex items-center\"\n                  whileHover={{ x: 4, transition: { duration: 0.2 } }}\n                >\n                  <Phone className=\"w-4 h-4 mr-2 text-via-primary flex-shrink-0\" />\n                  <div>{location.phone}</div>\n                </motion.div>\n\n                <motion.div\n                  className=\"flex items-center\"\n                  whileHover={{ x: 4, transition: { duration: 0.2 } }}\n                >\n                  <Clock className=\"w-4 h-4 mr-2 text-via-primary flex-shrink-0\" />\n                  <div>{location.hours}</div>\n                </motion.div>\n              </div>\n              \n              <div className=\"mt-6 space-y-2\">\n                <Link href={`/locations/${slug}`}>\n                  <motion.div\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      className=\"w-full border-via-primary text-via-primary hover:bg-via-primary hover:text-white transition-all duration-300\"\n                    >\n                      View Details\n                    </Button>\n                  </motion.div>\n                </Link>\n\n                <motion.div\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"w-full text-gray-600 hover:text-via-primary transition-all duration-300\"\n                    onClick={() => {\n                      // Open Google Maps directions\n                      const encodedAddress = encodeURIComponent(location.address);\n                      window.open(`https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`, '_blank');\n                    }}\n                  >\n                    Get Directions\n                  </Button>\n                </motion.div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Animated Bottom CTA */}\n        <motion.div\n          className=\"text-center mt-12\"\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={animationConfig.viewport}\n          variants={fadeInUp}\n        >\n          <Link href=\"/locations\">\n            <motion.div\n              whileHover={{\n                scale: 1.05,\n                transition: { duration: 0.2, ease: \"easeInOut\" }\n              }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <Button\n                size=\"lg\"\n                className=\"bg-via-primary hover:bg-via-primary-dark text-white px-8 py-3 text-lg font-semibold rounded-md transition-all duration-300\"\n              >\n                View All Locations\n              </Button>\n            </motion.div>\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AANA;;;;;;;AAgBA,MAAM,YAA6C;IACjD,QAAQ;QACN,MAAM;QACN,UAAU;QACV,SAAS;QACT,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;YAAE,KAAK;YAAoB,KAAK,CAAC;QAAkB;QAChE,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;IACA,YAAY;QACV,MAAM;QACN,UAAU;QACV,SAAS;QACT,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;YAAE,KAAK;YAAmB,KAAK,CAAC;QAAiB;QAC9D,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,SAAS;QACT,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;YAAE,KAAK;YAAmB,KAAK,CAAC;QAAkB;QAC/D,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;IACA,YAAY;QACV,MAAM;QACN,UAAU;QACV,SAAS;QACT,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;YAAE,KAAK;YAAmB,KAAK,CAAC;QAAkB;QAC/D,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;AACF;AAEO,SAAS;IACd,qBACE,+XAAC;QAAQ,WAAU;kBACjB,cAAA,+XAAC;YAAI,WAAU;;8BAEb,+XAAC,4TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU,sJAAe,CAAC,QAAQ;oBAClC,UAAU,uJAAgB;;sCAE1B,+XAAC,4TAAM,CAAC,EAAE;4BACR,WAAU;4BACV,UAAU,iJAAU;sCACrB;;;;;;sCAGD,+XAAC,4TAAM,CAAC,CAAC;4BACP,WAAU;4BACV,UAAU,+IAAQ;sCACnB;;;;;;;;;;;;8BAMH,+XAAC,4TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU,sJAAe,CAAC,QAAQ;oBAClC,UAAU,8IAAO;8BAEjB,cAAA,+XAAC,4TAAM,CAAC,GAAG;wBACT,WAAU;wBACV,YAAY;4BACV,OAAO;4BACP,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAY;wBACjD;kCAEA,cAAA,+XAAC,4TAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU,+IAAQ;;8CAElB,+XAAC,4TAAM,CAAC,GAAG;oCACT,YAAY;wCACV,OAAO;wCACP,QAAQ;wCACR,YAAY;4CAAE,UAAU;wCAAI;oCAC9B;8CAEA,cAAA,+XAAC,0TAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,+XAAC;oCAAE,WAAU;8CAAsB;;;;;;8CACnC,+XAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;;;;;;;;;;;8BAM7B,+XAAC,4TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU,sJAAe,CAAC,QAAQ;oBAClC,UAAU,uJAAgB;8BAEzB,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC,MAAM,SAAS,iBAC9C,+XAAC,4TAAM,CAAC,GAAG;4BAET,WAAU;4BACV,UAAU,kJAAW;4BACrB,YAAY;gCACV,GAAG,CAAC;gCACJ,OAAO;gCACP,WAAW;gCACX,YAAY;oCACV,UAAU;oCACV,MAAM;gCACR;4BACF;4BACA,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,+XAAC,4TAAM,CAAC,EAAE;oCACR,WAAU;oCACV,YAAY;wCACV,OAAO;wCACP,YAAY;4CAAE,UAAU;wCAAI;oCAC9B;8CAEC,SAAS,IAAI;;;;;;8CAGhB,+XAAC;oCAAI,WAAU;;sDACb,+XAAC,4TAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,GAAG;gDAAG,YAAY;oDAAE,UAAU;gDAAI;4CAAE;;8DAElD,+XAAC,0TAAM;oDAAC,WAAU;;;;;;8DAClB,+XAAC;;sEACC,+XAAC;4DAAI,WAAU;sEAA6B,SAAS,QAAQ;;;;;;sEAC7D,+XAAC;sEAAK,SAAS,OAAO;;;;;;;;;;;;;;;;;;sDAI1B,+XAAC,4TAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,GAAG;gDAAG,YAAY;oDAAE,UAAU;gDAAI;4CAAE;;8DAElD,+XAAC,mTAAK;oDAAC,WAAU;;;;;;8DACjB,+XAAC;8DAAK,SAAS,KAAK;;;;;;;;;;;;sDAGtB,+XAAC,4TAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,GAAG;gDAAG,YAAY;oDAAE,UAAU;gDAAI;4CAAE;;8DAElD,+XAAC,mTAAK;oDAAC,WAAU;;;;;;8DACjB,+XAAC;8DAAK,SAAS,KAAK;;;;;;;;;;;;;;;;;;8CAIxB,+XAAC;oCAAI,WAAU;;sDACb,+XAAC,wTAAI;4CAAC,MAAM,CAAC,WAAW,EAAE,MAAM;sDAC9B,cAAA,+XAAC,4TAAM,CAAC,GAAG;gDACT,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;0DAExB,cAAA,+XAAC,uJAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;sDAML,+XAAC,4TAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,+XAAC,uJAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;oDACP,8BAA8B;oDAC9B,MAAM,iBAAiB,mBAAmB,SAAS,OAAO;oDAC1D,OAAO,IAAI,CAAC,CAAC,mDAAmD,EAAE,gBAAgB,EAAE;gDACtF;0DACD;;;;;;;;;;;;;;;;;;2BAlFA;;;;;;;;;;8BA4FX,+XAAC,4TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU,sJAAe,CAAC,QAAQ;oBAClC,UAAU,+IAAQ;8BAElB,cAAA,+XAAC,wTAAI;wBAAC,MAAK;kCACT,cAAA,+XAAC,4TAAM,CAAC,GAAG;4BACT,YAAY;gCACV,OAAO;gCACP,YAAY;oCAAE,UAAU;oCAAK,MAAM;gCAAY;4BACjD;4BACA,UAAU;gCAAE,OAAO;4BAAK;sCAExB,cAAA,+XAAC,uJAAM;gCACL,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}