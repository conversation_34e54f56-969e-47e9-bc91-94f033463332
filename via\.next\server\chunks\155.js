"use strict";exports.id=155,exports.ids=[155],exports.modules={42366:(a,b,c)=>{c.d(b,{LocationsSection:()=>n});var d=c(92536),e=c(4163),f=c.n(e),g=c(49216),h=c(15353),i=c(65529),j=c(84742),k=c(69108),l=c(94552);let m={adbc:{name:"ADBC",fullName:"Via Executive Suites ADBC",address:"813 N. Main St., McAllen, Texas 78501",phone:"(*************",hours:"Mon-Fri: 8:00 AM - 5:00 PM",email:"<EMAIL>",coordinates:{lat:26.212138647510635,lng:-98.23348265965645},description:"Located in the heart of McAllen, our ADBC location offers premium office spaces with easy access to major highways and business districts.",features:["Private office suites","Conference rooms","Reception services","High-speed internet","Free parking","Break room amenities","Mail handling","24/7 building access"],image:"https://via2success.com/wp-content/uploads/2021/01/location-adbc.jpg"},"la-costa":{name:"La Costa",fullName:"Via Executive Suites La Costa",address:"214 N 16th St, McAllen, Texas 78501",phone:"(*************",hours:"Mon-Fri: 8:00 AM - 5:00 PM",email:"<EMAIL>",coordinates:{lat:26.20654823351708,lng:-98.2359421731485},description:"Our La Costa location provides modern office solutions in a vibrant business community, perfect for growing companies and entrepreneurs.",features:["Flexible office spaces","Virtual office services","Meeting facilities","Business support services","Secure access","Professional environment","Networking opportunities","Convenient location"],image:"https://via2success.com/wp-content/uploads/2021/01/location-la-costa.jpg"},"23rd":{name:"23rd",fullName:"Via Executive Suites 23rd",address:"1821 N 23rd Street, McAllen, Texas 78501",phone:"(*************",hours:"Mon-Fri: 8:00 AM - 5:00 PM",email:"<EMAIL>",coordinates:{lat:26.22271557411775,lng:-98.24298783082033},description:"The 23rd Street location offers spacious office environments with premium amenities, ideal for established businesses seeking professional workspace.",features:["Executive office suites","Boardroom facilities","Premium amenities","Dedicated support staff","Advanced technology","Professional services","Business networking","Premium location"],image:"https://via2success.com/wp-content/uploads/2021/01/location-23rd.jpg"},edinburg:{name:"Edinburg",fullName:"Via Executive Suites Edinburg",address:"1409 S 9th Ave, Edinburg, Texas 78539",phone:"(*************",hours:"Mon-Fri: 8:00 AM - 5:00 PM",email:"<EMAIL>",coordinates:{lat:26.28915674741617,lng:-98.16747817499945},description:"Our Edinburg location serves the northern Rio Grande Valley with professional office solutions and comprehensive business support services.",features:["Modern office spaces","Collaborative areas","Technology infrastructure","Business services","Community events","Professional development","Local partnerships","Strategic positioning"],image:"https://via2success.com/wp-content/uploads/2021/01/location-edinburg.jpg"}};function n(){return(0,d.jsx)("section",{className:"py-16 bg-white overflow-hidden",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,d.jsxs)(g.P.div,{className:"text-center mb-12",initial:"hidden",whileInView:"visible",viewport:l.xb.viewport,variants:l.bK,children:[(0,d.jsx)(g.P.h2,{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",variants:l.Ce,children:"Locations"}),(0,d.jsx)(g.P.p,{className:"text-lg text-gray-600 max-w-3xl mx-auto",variants:l.tE,children:"Our 4 locations are situated throughout the RGV for maximum convenience."})]}),(0,d.jsx)(g.P.div,{className:"mb-12",initial:"hidden",whileInView:"visible",viewport:l.xb.viewport,variants:l.Yo,children:(0,d.jsx)(g.P.div,{className:"bg-gray-200 rounded-lg h-96 flex items-center justify-center overflow-hidden",whileHover:{scale:1.02,transition:{duration:.3,ease:"easeInOut"}},children:(0,d.jsxs)(g.P.div,{className:"text-center text-gray-500",variants:l.tE,children:[(0,d.jsx)(g.P.div,{whileHover:{scale:1.1,rotate:5,transition:{duration:.2}},children:(0,d.jsx)(i.A,{className:"w-12 h-12 mx-auto mb-4"})}),(0,d.jsx)("p",{className:"text-lg font-medium",children:"Interactive Map"}),(0,d.jsx)("p",{className:"text-sm",children:"Map integration would be implemented here"})]})})}),(0,d.jsx)(g.P.div,{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",initial:"hidden",whileInView:"visible",viewport:l.xb.viewport,variants:l.bK,children:Object.entries(m).map(([a,b])=>(0,d.jsxs)(g.P.div,{className:"bg-white border border-gray-200 rounded-lg shadow-md p-6 cursor-pointer",variants:l.Rf,whileHover:{y:-8,scale:1.02,boxShadow:"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",transition:{duration:.3,ease:"easeInOut"}},whileTap:{scale:.98},children:[(0,d.jsx)(g.P.h3,{className:"text-xl font-semibold text-via-primary mb-4",whileHover:{scale:1.05,transition:{duration:.2}},children:b.name}),(0,d.jsxs)("div",{className:"space-y-3 text-sm text-gray-600",children:[(0,d.jsxs)(g.P.div,{className:"flex items-start",whileHover:{x:4,transition:{duration:.2}},children:[(0,d.jsx)(i.A,{className:"w-4 h-4 mr-2 mt-0.5 text-via-primary flex-shrink-0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-gray-900",children:b.fullName}),(0,d.jsx)("div",{children:b.address})]})]}),(0,d.jsxs)(g.P.div,{className:"flex items-center",whileHover:{x:4,transition:{duration:.2}},children:[(0,d.jsx)(j.A,{className:"w-4 h-4 mr-2 text-via-primary flex-shrink-0"}),(0,d.jsx)("div",{children:b.phone})]}),(0,d.jsxs)(g.P.div,{className:"flex items-center",whileHover:{x:4,transition:{duration:.2}},children:[(0,d.jsx)(k.A,{className:"w-4 h-4 mr-2 text-via-primary flex-shrink-0"}),(0,d.jsx)("div",{children:b.hours})]})]}),(0,d.jsxs)("div",{className:"mt-6 space-y-2",children:[(0,d.jsx)(f(),{href:`/locations/${a}`,children:(0,d.jsx)(g.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,d.jsx)(h.$,{variant:"outline",size:"sm",className:"w-full border-via-primary text-via-primary hover:bg-via-primary hover:text-white transition-all duration-300",children:"View Details"})})}),(0,d.jsx)(g.P.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:(0,d.jsx)(h.$,{variant:"ghost",size:"sm",className:"w-full text-gray-600 hover:text-via-primary transition-all duration-300",onClick:()=>{let a=encodeURIComponent(b.address);window.open(`https://www.google.com/maps/dir/?api=1&destination=${a}`,"_blank")},children:"Get Directions"})})]})]},a))}),(0,d.jsx)(g.P.div,{className:"text-center mt-12",initial:"hidden",whileInView:"visible",viewport:l.xb.viewport,variants:l.tE,children:(0,d.jsx)(f(),{href:"/locations",children:(0,d.jsx)(g.P.div,{whileHover:{scale:1.05,transition:{duration:.2,ease:"easeInOut"}},whileTap:{scale:.95},children:(0,d.jsx)(h.$,{size:"lg",className:"bg-via-primary hover:bg-via-primary-dark text-white px-8 py-3 text-lg font-semibold rounded-md transition-all duration-300",children:"View All Locations"})})})})]})})}},44604:(a,b,c)=>{c.d(b,{LocationsSection:()=>d});let d=(0,c(90850).registerClientReference)(function(){throw Error("Attempted to call LocationsSection() from the server but LocationsSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\via\\components\\locations-section.tsx","LocationsSection")},65529:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(33997).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},69108:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(33997).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},84742:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(33997).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])}};