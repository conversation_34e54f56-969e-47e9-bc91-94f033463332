module.exports = [
"[project]/Desktop/via/app/locations/[slug]/layout.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>LocationLayout,
    "generateMetadata",
    ()=>generateMetadata,
    "generateStaticParams",
    ()=>generateStaticParams
]);
// Location data - in a real app, this would come from a database
const locationData = {
    "adbc": {
        name: "ADBC",
        fullName: "Via Executive Suites ADBC",
        address: "813 N. Main St., McAllen, Texas 78501"
    },
    "la-costa": {
        name: "La Costa",
        fullName: "Via Executive Suites La Costa",
        address: "214 N 16th St, McAllen, Texas 78501"
    },
    "23rd": {
        name: "23rd",
        fullName: "Via Executive Suites 23rd",
        address: "1821 N 23rd Street, McAllen, Texas 78501"
    },
    "edinburg": {
        name: "Edinburg",
        fullName: "Via Executive Suites Edinburg",
        address: "1409 S 9th Ave, Edinburg, Texas 78539"
    }
};
async function generateStaticParams() {
    return Object.keys(locationData).map((slug)=>({
            slug
        }));
}
async function generateMetadata({ params }) {
    const resolvedParams = await params;
    const location = locationData[resolvedParams.slug];
    if (!location) {
        return {
            title: "Location Not Found - Via Executive Suites"
        };
    }
    return {
        title: `${location.fullName} - Via Executive Suites`,
        description: `Visit ${location.fullName} at ${location.address}. Professional office spaces with premium amenities and business support services.`,
        keywords: `${location.name}, office space, McAllen, Edinburg, Rio Grande Valley, executive suites`,
        openGraph: {
            title: `${location.fullName} - Via Executive Suites`,
            description: `Visit ${location.fullName} at ${location.address}. Professional office spaces with premium amenities.`,
            type: "website",
            locale: "en_US"
        }
    };
}
function LocationLayout({ children }) {
    return children;
}
}),
];

//# sourceMappingURL=Desktop_via_app_locations_%5Bslug%5D_layout_tsx_7ae84967._.js.map