import { Variants } from "framer-motion";

// Animation variants for consistent animations across the site
export const fadeInUp: Variants = {
  hidden: {
    opacity: 0,
    y: 30, // Reduced from 60 to prevent overflow
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.6, -0.05, 0.01, 0.99],
    },
  },
};

export const fadeInDown: Variants = {
  hidden: {
    opacity: 0,
    y: -30, // Reduced from -60 to prevent overflow
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.6, -0.05, 0.01, 0.99],
    },
  },
};

export const fadeInLeft: Variants = {
  hidden: {
    opacity: 0,
    x: -30, // Reduced from -60 to prevent overflow
  },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.6,
      ease: [0.6, -0.05, 0.01, 0.99],
    },
  },
};

export const fadeInRight: Variants = {
  hidden: {
    opacity: 0,
    x: 30, // Reduced from 60 to prevent overflow
  },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.6,
      ease: [0.6, -0.05, 0.01, 0.99],
    },
  },
};

export const scaleIn: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.9, // Reduced from 0.8 to prevent overflow
  },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: [0.6, -0.05, 0.01, 0.99],
    },
  },
};

export const staggerContainer: Variants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    },
  },
};

export const staggerItem: Variants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: [0.6, -0.05, 0.01, 0.99],
    },
  },
};

// Page transition variants
export const pageTransition: Variants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: [0.6, -0.05, 0.01, 0.99],
    },
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: {
      duration: 0.3,
      ease: [0.6, -0.05, 0.01, 0.99],
    },
  },
};

// Hover effects
export const hoverScale = {
  scale: 1.05,
  transition: {
    duration: 0.2,
    ease: "easeInOut",
  },
};

export const hoverLift = {
  y: -8,
  transition: {
    duration: 0.2,
    ease: "easeInOut",
  },
};

// Parallax effect
export const parallaxVariants: Variants = {
  hidden: {
    y: 0,
  },
  visible: {
    y: -25, // Reduced from -50 to prevent overflow
    transition: {
      duration: 0.8,
      ease: "easeOut",
    },
  },
};

// Animation configuration
export const animationConfig = {
  // Viewport settings for scroll-triggered animations
  viewport: {
    once: true,
    margin: "-100px",
    amount: 0.3,
  },
  
  // Reduced motion settings
  reducedMotion: {
    duration: 0.01,
    ease: "linear" as const,
  },
};

// Utility function to check for reduced motion preference
export const shouldReduceMotion = () => {
  if (typeof window !== "undefined") {
    return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
  }
  return false;
};

// Utility function to get animation variants with reduced motion support
export const getAnimationVariants = (variants: Variants): Variants => {
  if (shouldReduceMotion()) {
    const reducedVariants: Variants = {};
    Object.keys(variants).forEach((key) => {
      reducedVariants[key] = {
        ...variants[key],
        transition: animationConfig.reducedMotion,
      };
    });
    return reducedVariants;
  }
  return variants;
};

// Custom easing curves
export const easings = {
  easeInOutCubic: [0.4, 0, 0.2, 1],
  easeOutCubic: [0.33, 1, 0.68, 1],
  easeInCubic: [0.32, 0, 0.67, 0],
  easeOutQuart: [0.25, 1, 0.5, 1],
  easeInOutQuart: [0.76, 0, 0.24, 1],
  spring: {
    type: "spring",
    damping: 25,
    stiffness: 120,
  },
};

// Hero section specific animations
export const heroAnimations: Variants = {
  hidden: {},
  visible: {
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.1,
    },
  },
};

export const heroTitle: Variants = {
  hidden: {
    opacity: 0,
    y: 50, // Reduced from 100 to prevent overflow
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      ease: [0.6, -0.05, 0.01, 0.99],
    },
  },
};

export const heroSubtitle: Variants = {
  hidden: {
    opacity: 0,
    y: 25, // Reduced from 50 to prevent overflow
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.6, -0.05, 0.01, 0.99],
      delay: 0.2,
    },
  },
};

export const heroButtons: Variants = {
  hidden: {
    opacity: 0,
    y: 20, // Reduced from 30 to prevent overflow
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: [0.6, -0.05, 0.01, 0.99],
      delay: 0.4,
    },
  },
};
