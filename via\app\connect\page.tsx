"use client";

import { Phone, Mail, MapPin, Clock } from "lucide-react";
import { AnimatedHeroSection } from "@/components/connect/animated-hero-section";
import { AnimatedContactInfo } from "@/components/connect/animated-contact-info";
import { EnhancedContactForm } from "@/components/connect/enhanced-contact-form";
import { AnimatedLocationsSection } from "@/components/connect/animated-locations-section";
import { AnimatedCTASection } from "@/components/connect/animated-cta-section";
import { ContactInfo, LocationInfo, ContactFormData } from "@/types/connect";
import { submitContactForm, sendAutoReply } from "@/lib/contact-form";

// Contact information data
const contactInfo: ContactInfo[] = [
  {
    title: "Phone",
    value: "(*************",
    description: "Call us during business hours",
    icon: Phone,
    href: "tel:+***********",
  },
  {
    title: "Email",
    value: "<EMAIL>",
    description: "Send us a message anytime",
    icon: Mail,
    href: "mailto:<EMAIL>",
  },
  {
    title: "Main Office",
    value: "813 N. Main St., McAllen, TX 78501",
    description: "Visit our flagship location",
    icon: MapPin,
    href: "https://maps.google.com/?q=813+N.+Main+St.,+McAllen,+TX+78501",
  },
  {
    title: "Business Hours",
    value: "Monday - Friday: 8:00 AM - 6:00 PM",
    description: "Saturday: 9:00 AM - 2:00 PM",
    icon: Clock,
  },
];

// Locations data
const locations: LocationInfo[] = [
  {
    name: "ADBC Location",
    address: "813 N. Main St., McAllen, Texas 78501",
    phone: "(*************",
    hours: "Mon-Fri: 8AM-6PM, Sat: 9AM-2PM",
    mapUrl: "https://maps.google.com/?q=813+N.+Main+St.,+McAllen,+Texas+78501",
  },
  {
    name: "La Costa Location",
    address: "214 N 16th St, McAllen, Texas 78501",
    phone: "(*************",
    hours: "Mon-Fri: 8AM-6PM, Sat: 9AM-2PM",
    mapUrl: "https://maps.google.com/?q=214+N+16th+St,+McAllen,+Texas+78501",
  },
  {
    name: "23rd Street Location",
    address: "1821 N 23rd Street, McAllen, Texas 78501",
    phone: "(*************",
    hours: "Mon-Fri: 8AM-6PM, Sat: 9AM-2PM",
    mapUrl: "https://maps.google.com/?q=1821+N+23rd+Street,+McAllen,+Texas+78501",
  },
  {
    name: "Edinburg Location",
    address: "1409 S 9th Ave, Edinburg, Texas 78539",
    phone: "(*************",
    hours: "Mon-Fri: 8AM-6PM, Sat: 9AM-2PM",
    mapUrl: "https://maps.google.com/?q=1409+S+9th+Ave,+Edinburg,+Texas+78539",
  },
];

export default function ConnectPage() {
  // Handle button clicks
  const handleScheduleTour = () => {
    // Open scheduling modal or redirect to scheduling page
    window.open('tel:+***********', '_self');
  };

  const handleContactUs = () => {
    // Scroll to contact form
    const formElement = document.querySelector('#contact-form');
    if (formElement) {
      formElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleFormSubmit = async (formData: ContactFormData) => {
    try {
      // Submit form to Supabase
      const response = await submitContactForm(formData);

      if (response.success) {
        // Send auto-reply email
        try {
          await sendAutoReply(formData.email, `${formData.firstName} ${formData.lastName}`);
        } catch (emailError) {
          console.error('Auto-reply failed:', emailError);
          // Don't fail the form submission if auto-reply fails
        }
      }

      // The form component will handle the response display
      if (!response.success) {
        throw new Error(response.message);
      }
    } catch (error) {
      console.error('Form submission error:', error);
      throw error; // Re-throw to let the form component handle it
    }
  };

  return (
    <div className="min-h-screen">
      {/* Enhanced Hero Section */}
      <AnimatedHeroSection
        title="Let's Connect"
        subtitle="Ready to find your perfect workspace? Get in touch with us today."
        primaryButtonText="Schedule a Tour"
        secondaryButtonText="Contact Us"
        onPrimaryClick={handleScheduleTour}
        onSecondaryClick={handleContactUs}
      />

      {/* Enhanced Contact Information */}
      <AnimatedContactInfo
        title="Get in Touch"
        subtitle="We're here to help you find the perfect workspace solution. Reach out to us through any of these channels."
        contactInfo={contactInfo}
      />

      {/* Enhanced Contact Form */}
      <div id="contact-form">
        <EnhancedContactForm onSubmit={handleFormSubmit} />
      </div>

      {/* Enhanced Locations Section */}
      <AnimatedLocationsSection
        title="Our Locations"
        subtitle="Visit any of our convenient locations throughout the Rio Grande Valley."
        locations={locations}
      />

      {/* Enhanced CTA Section */}
      <AnimatedCTASection
        title="Ready to Get Started?"
        subtitle="Schedule a tour today and see how Via Executive Suites can help your business thrive."
        primaryButtonText="Schedule a Tour"
        secondaryButtonText="Call Now"
        onPrimaryClick={handleScheduleTour}
        onSecondaryClick={handleContactUs}
      />
    </div>
  );
}
