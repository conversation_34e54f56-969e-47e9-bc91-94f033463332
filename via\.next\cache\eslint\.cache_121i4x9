[{"C:\\Users\\<USER>\\Desktop\\via\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\confirm\\route.ts": "2", "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\error\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\forgot-password\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\login\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\sign-up\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\sign-up-success\\page.tsx": "7", "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\update-password\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\via\\app\\contact\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\via\\app\\layout.tsx": "10", "C:\\Users\\<USER>\\Desktop\\via\\app\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\via\\app\\protected\\layout.tsx": "12", "C:\\Users\\<USER>\\Desktop\\via\\app\\protected\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\via\\app\\services\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\about-contact-form.tsx": "15", "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\about-hero.tsx": "16", "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\about-testimonials.tsx": "17", "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\all-inclusive-services.tsx": "18", "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\company-story.tsx": "19", "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\mission-vision.tsx": "20", "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\starbucks-feature.tsx": "21", "C:\\Users\\<USER>\\Desktop\\via\\components\\about-section.tsx": "22", "C:\\Users\\<USER>\\Desktop\\via\\components\\auth-button.tsx": "23", "C:\\Users\\<USER>\\Desktop\\via\\components\\contact-form-section.tsx": "24", "C:\\Users\\<USER>\\Desktop\\via\\components\\deploy-button.tsx": "25", "C:\\Users\\<USER>\\Desktop\\via\\components\\env-var-warning.tsx": "26", "C:\\Users\\<USER>\\Desktop\\via\\components\\features-grid.tsx": "27", "C:\\Users\\<USER>\\Desktop\\via\\components\\footer.tsx": "28", "C:\\Users\\<USER>\\Desktop\\via\\components\\forgot-password-form.tsx": "29", "C:\\Users\\<USER>\\Desktop\\via\\components\\gallery-section.tsx": "30", "C:\\Users\\<USER>\\Desktop\\via\\components\\hero-section.tsx": "31", "C:\\Users\\<USER>\\Desktop\\via\\components\\hero.tsx": "32", "C:\\Users\\<USER>\\Desktop\\via\\components\\locations-section.tsx": "33", "C:\\Users\\<USER>\\Desktop\\via\\components\\login-form.tsx": "34", "C:\\Users\\<USER>\\Desktop\\via\\components\\logout-button.tsx": "35", "C:\\Users\\<USER>\\Desktop\\via\\components\\navbar.tsx": "36", "C:\\Users\\<USER>\\Desktop\\via\\components\\next-logo.tsx": "37", "C:\\Users\\<USER>\\Desktop\\via\\components\\page-transition.tsx": "38", "C:\\Users\\<USER>\\Desktop\\via\\components\\sign-up-form.tsx": "39", "C:\\Users\\<USER>\\Desktop\\via\\components\\supabase-logo.tsx": "40", "C:\\Users\\<USER>\\Desktop\\via\\components\\testimonials-section.tsx": "41", "C:\\Users\\<USER>\\Desktop\\via\\components\\theme-switcher.tsx": "42", "C:\\Users\\<USER>\\Desktop\\via\\components\\tutorial\\code-block.tsx": "43", "C:\\Users\\<USER>\\Desktop\\via\\components\\tutorial\\connect-supabase-steps.tsx": "44", "C:\\Users\\<USER>\\Desktop\\via\\components\\tutorial\\fetch-data-steps.tsx": "45", "C:\\Users\\<USER>\\Desktop\\via\\components\\tutorial\\sign-up-user-steps.tsx": "46", "C:\\Users\\<USER>\\Desktop\\via\\components\\tutorial\\tutorial-step.tsx": "47", "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\badge.tsx": "48", "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\button.tsx": "49", "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\card.tsx": "50", "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\checkbox.tsx": "51", "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\dropdown-menu.tsx": "52", "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\input.tsx": "53", "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\label.tsx": "54", "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\textarea.tsx": "55", "C:\\Users\\<USER>\\Desktop\\via\\components\\update-password-form.tsx": "56", "C:\\Users\\<USER>\\Desktop\\via\\components\\workspace-types.tsx": "57", "C:\\Users\\<USER>\\Desktop\\via\\lib\\animations.ts": "58", "C:\\Users\\<USER>\\Desktop\\via\\lib\\supabase\\client.ts": "59", "C:\\Users\\<USER>\\Desktop\\via\\lib\\supabase\\middleware.ts": "60", "C:\\Users\\<USER>\\Desktop\\via\\lib\\supabase\\server.ts": "61", "C:\\Users\\<USER>\\Desktop\\via\\lib\\utils.ts": "62", "C:\\Users\\<USER>\\Desktop\\via\\app\\locations\\page.tsx": "63", "C:\\Users\\<USER>\\Desktop\\via\\app\\locations\\[slug]\\layout.tsx": "64", "C:\\Users\\<USER>\\Desktop\\via\\app\\locations\\[slug]\\loading.tsx": "65", "C:\\Users\\<USER>\\Desktop\\via\\app\\locations\\[slug]\\not-found.tsx": "66", "C:\\Users\\<USER>\\Desktop\\via\\app\\locations\\[slug]\\page.tsx": "67", "C:\\Users\\<USER>\\Desktop\\via\\components\\services-section.tsx": "68", "C:\\Users\\<USER>\\Desktop\\via\\lib\\types\\location.ts": "69"}, {"size": 1538, "mtime": 1755772229324, "results": "70", "hashOfConfig": "71"}, {"size": 1005, "mtime": 1755737742000, "results": "72", "hashOfConfig": "71"}, {"size": 1042, "mtime": 1755737742000, "results": "73", "hashOfConfig": "71"}, {"size": 303, "mtime": 1755737742000, "results": "74", "hashOfConfig": "71"}, {"size": 275, "mtime": 1755737742000, "results": "75", "hashOfConfig": "71"}, {"size": 279, "mtime": 1755737742000, "results": "76", "hashOfConfig": "71"}, {"size": 916, "mtime": 1755737742000, "results": "77", "hashOfConfig": "71"}, {"size": 303, "mtime": 1755737742000, "results": "78", "hashOfConfig": "71"}, {"size": 3881, "mtime": 1755769891399, "results": "79", "hashOfConfig": "71"}, {"size": 1998, "mtime": 1755775185014, "results": "80", "hashOfConfig": "71"}, {"size": 815, "mtime": 1755770838694, "results": "81", "hashOfConfig": "71"}, {"size": 1736, "mtime": 1755737742000, "results": "82", "hashOfConfig": "71"}, {"size": 1225, "mtime": 1755737742000, "results": "83", "hashOfConfig": "71"}, {"size": 2223, "mtime": 1755777090193, "results": "84", "hashOfConfig": "71"}, {"size": 4480, "mtime": 1755772183618, "results": "85", "hashOfConfig": "71"}, {"size": 1133, "mtime": 1755775310876, "results": "86", "hashOfConfig": "71"}, {"size": 2748, "mtime": 1755772136540, "results": "87", "hashOfConfig": "71"}, {"size": 3566, "mtime": 1755772109267, "results": "88", "hashOfConfig": "71"}, {"size": 3223, "mtime": 1755775310876, "results": "89", "hashOfConfig": "71"}, {"size": 2976, "mtime": 1755772285808, "results": "90", "hashOfConfig": "71"}, {"size": 1256, "mtime": 1755772295152, "results": "91", "hashOfConfig": "71"}, {"size": 3646, "mtime": 1755775219144, "results": "92", "hashOfConfig": "71"}, {"size": 805, "mtime": 1755737742000, "results": "93", "hashOfConfig": "71"}, {"size": 6952, "mtime": 1755770684820, "results": "94", "hashOfConfig": "71"}, {"size": 1321, "mtime": 1755737742000, "results": "95", "hashOfConfig": "71"}, {"size": 537, "mtime": 1755737742000, "results": "96", "hashOfConfig": "71"}, {"size": 4943, "mtime": 1755775227429, "results": "97", "hashOfConfig": "71"}, {"size": 5819, "mtime": 1755771442607, "results": "98", "hashOfConfig": "71"}, {"size": 3564, "mtime": 1755737742000, "results": "99", "hashOfConfig": "71"}, {"size": 5133, "mtime": 1755775244123, "results": "100", "hashOfConfig": "71"}, {"size": 4209, "mtime": 1755775196725, "results": "101", "hashOfConfig": "71"}, {"size": 1451, "mtime": 1755737742000, "results": "102", "hashOfConfig": "71"}, {"size": 10511, "mtime": 1755776962885, "results": "103", "hashOfConfig": "71"}, {"size": 3526, "mtime": 1755737742000, "results": "104", "hashOfConfig": "71"}, {"size": 422, "mtime": 1755737742000, "results": "105", "hashOfConfig": "71"}, {"size": 8818, "mtime": 1755771403481, "results": "106", "hashOfConfig": "71"}, {"size": 3993, "mtime": 1755737742000, "results": "107", "hashOfConfig": "71"}, {"size": 671, "mtime": 1755775210542, "results": "108", "hashOfConfig": "71"}, {"size": 4659, "mtime": 1755767077616, "results": "109", "hashOfConfig": "71"}, {"size": 7226, "mtime": 1755737742000, "results": "110", "hashOfConfig": "71"}, {"size": 3922, "mtime": 1755770651941, "results": "111", "hashOfConfig": "71"}, {"size": 2287, "mtime": 1755737742000, "results": "112", "hashOfConfig": "71"}, {"size": 1365, "mtime": 1755737742000, "results": "113", "hashOfConfig": "71"}, {"size": 2128, "mtime": 1755737742000, "results": "114", "hashOfConfig": "71"}, {"size": 3506, "mtime": 1755777251632, "results": "115", "hashOfConfig": "71"}, {"size": 3606, "mtime": 1755737742000, "results": "116", "hashOfConfig": "71"}, {"size": 683, "mtime": 1755737742000, "results": "117", "hashOfConfig": "71"}, {"size": 1147, "mtime": 1755737742000, "results": "118", "hashOfConfig": "71"}, {"size": 1915, "mtime": 1755737742000, "results": "119", "hashOfConfig": "71"}, {"size": 1857, "mtime": 1755737742000, "results": "120", "hashOfConfig": "71"}, {"size": 1035, "mtime": 1755737742000, "results": "121", "hashOfConfig": "71"}, {"size": 7647, "mtime": 1755737742000, "results": "122", "hashOfConfig": "71"}, {"size": 776, "mtime": 1755737742000, "results": "123", "hashOfConfig": "71"}, {"size": 734, "mtime": 1755737742000, "results": "124", "hashOfConfig": "71"}, {"size": 756, "mtime": 1755771787260, "results": "125", "hashOfConfig": "71"}, {"size": 2488, "mtime": 1755737742000, "results": "126", "hashOfConfig": "71"}, {"size": 4504, "mtime": 1755771115786, "results": "127", "hashOfConfig": "71"}, {"size": 4868, "mtime": 1755775172251, "results": "128", "hashOfConfig": "71"}, {"size": 230, "mtime": 1755737742000, "results": "129", "hashOfConfig": "71"}, {"size": 2999, "mtime": 1755772829255, "results": "130", "hashOfConfig": "71"}, {"size": 982, "mtime": 1755737742000, "results": "131", "hashOfConfig": "71"}, {"size": 361, "mtime": 1755737742000, "results": "132", "hashOfConfig": "71"}, {"size": 1568, "mtime": 1755775474600, "results": "133", "hashOfConfig": "71"}, {"size": 2154, "mtime": 1755776874483, "results": "134", "hashOfConfig": "71"}, {"size": 3608, "mtime": 1755775746336, "results": "135", "hashOfConfig": "71"}, {"size": 1355, "mtime": 1755776962883, "results": "136", "hashOfConfig": "71"}, {"size": 10453, "mtime": 1755776543153, "results": "137", "hashOfConfig": "71"}, {"size": 8258, "mtime": 1755777067425, "results": "138", "hashOfConfig": "71"}, {"size": 488, "mtime": 1755776518237, "results": "139", "hashOfConfig": "71"}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "114tno0", {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\via\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\confirm\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\error\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\forgot-password\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\sign-up\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\sign-up-success\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\auth\\update-password\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\protected\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\protected\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\services\\page.tsx", ["347"], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\about-contact-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\about-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\about-testimonials.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\all-inclusive-services.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\company-story.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\mission-vision.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\about\\starbucks-feature.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\about-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\auth-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\contact-form-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\deploy-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\env-var-warning.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\features-grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\forgot-password-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\gallery-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\locations-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\login-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\logout-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\next-logo.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\page-transition.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\sign-up-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\supabase-logo.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\testimonials-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\theme-switcher.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\tutorial\\code-block.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\tutorial\\connect-supabase-steps.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\tutorial\\fetch-data-steps.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\tutorial\\sign-up-user-steps.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\tutorial\\tutorial-step.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\update-password-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\workspace-types.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\lib\\animations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\via\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\via\\lib\\supabase\\middleware.ts", [], [], "C:\\Users\\<USER>\\Desktop\\via\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\Desktop\\via\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\locations\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\locations\\[slug]\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\locations\\[slug]\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\locations\\[slug]\\not-found.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\app\\locations\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\components\\services-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\via\\lib\\types\\location.ts", [], [], {"ruleId": "348", "severity": 2, "message": "349", "line": 6, "column": 10, "nodeType": null, "messageId": "350", "endLine": 6, "endColumn": 18}, "@typescript-eslint/no-unused-vars", "'Metadata' is defined but never used.", "unusedVar"]