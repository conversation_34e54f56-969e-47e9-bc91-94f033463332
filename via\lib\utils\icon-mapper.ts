import {
  Building2,
  Wifi,
  Users,
  Phone,
  Car,
  Coffee,
  Shield,
  MapPin,
  Mail,
  Monitor,
  Scissors,
  Zap,
  Clock,
  Heart,
  Check,
  ArrowRight
} from 'lucide-react';

const iconMap: Record<string, React.ComponentType<any>> = {
  Building2,
  Wifi,
  Users,
  Phone,
  Car,
  Coffee,
  Shield,
  MapPin,
  Mail,
  Monitor,
  Scissors,
  Zap,
  Clock,
  Heart,
  Check,
  ArrowRight
};

export function getIconComponent(iconName: string): React.ComponentType<any> {
  return iconMap[iconName] || Building2; // Default fallback
}

export function formatPrice(price: number, period: string): string {
  if (period === 'monthly') {
    return `$${price.toLocaleString()}`;
  } else if (period === 'hourly') {
    return `$${price}`;
  } else if (period === 'daily') {
    return `$${price}`;
  } else if (period === 'weekly') {
    return `$${price}`;
  } else if (period === 'yearly') {
    return `$${price.toLocaleString()}`;
  }
  return `$${price}`;
}

export function formatPeriod(period: string): string {
  switch (period) {
    case 'hourly':
      return '/hour';
    case 'daily':
      return '/day';
    case 'weekly':
      return '/week';
    case 'monthly':
      return '/month';
    case 'yearly':
      return '/year';
    default:
      return '';
  }
}
