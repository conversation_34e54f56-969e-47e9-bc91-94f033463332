"use client";

import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, Phone } from "lucide-react";
import { HeroSectionProps } from "@/types/connect";
import { fadeInUp, fadeInDown, staggerContainer, staggerItem } from "@/lib/animations";
import { cn } from "@/lib/utils";

export function AnimatedHeroSection({
  title,
  subtitle,
  primaryButtonText,
  secondaryButtonText,
  onPrimaryClick,
  onSecondaryClick,
  className
}: HeroSectionProps) {
  return (
    <motion.section
      className={cn(
        "relative bg-gradient-to-r from-via-primary via-via-primary-light to-via-primary text-white py-16 sm:py-20 md:py-24 lg:py-32 overflow-hidden",
        className
      )}
      initial="hidden"
      animate="visible"
      variants={staggerContainer}
    >
      {/* Background overlay */}
      <div className="absolute inset-0 bg-black/20"></div>
      
      {/* Animated background elements */}
      <motion.div
        className="absolute inset-0 opacity-10"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 0.1 }}
        transition={{ duration: 2, ease: "easeOut" }}
      >
        <div className="absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 bg-white rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white rounded-full blur-3xl"></div>
      </motion.div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.h1
          className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6"
          variants={fadeInDown}
        >
          {title}
        </motion.h1>

        <motion.p
          className="text-lg sm:text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto mb-6 sm:mb-8 px-4"
          variants={fadeInUp}
        >
          {subtitle}
        </motion.p>
        
        <motion.div
          className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center px-4"
          variants={staggerItem}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="w-full sm:w-auto"
          >
            <Button
              size="lg"
              className="w-full sm:w-auto bg-white text-via-primary hover:bg-gray-100 px-6 sm:px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300"
              onClick={onPrimaryClick}
            >
              <Calendar className="mr-2 h-4 sm:h-5 w-4 sm:w-5" />
              {primaryButtonText}
            </Button>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="w-full sm:w-auto"
          >
            <Button
              size="lg"
              variant="outline"
              className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-via-primary px-6 sm:px-8 py-3 border-2 transition-all duration-300"
              onClick={onSecondaryClick}
            >
              <Phone className="mr-2 h-4 sm:h-5 w-4 sm:w-5" />
              {secondaryButtonText}
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </motion.section>
  );
}
