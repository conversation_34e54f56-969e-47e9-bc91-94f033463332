"use client";

import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import { fadeInUp, staggerContainer, staggerItem, animationConfig } from "@/lib/animations";
import { But<PERSON> } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";

const galleryImages = [
  {
    src: "https://via2success.com/wp-content/uploads/2021/01/home-section_4-img_1.jpg",
    alt: "Modern office reception area",
    title: "Reception Area"
  },
  {
    src: "https://via2success.com/wp-content/uploads/2021/01/home-section_4-img_2.jpg",
    alt: "Executive office suite",
    title: "Executive Suite"
  },
  {
    src: "https://via2success.com/wp-content/uploads/2021/01/home-section_4-img_3.jpg",
    alt: "Conference room",
    title: "Conference Room"
  },
  {
    src: "https://via2success.com/wp-content/uploads/2021/01/home-section_4-img_4.jpg",
    alt: "Coworking space",
    title: "Coworking Space"
  },
  {
    src: "https://via2success.com/wp-content/uploads/2021/01/home-section_4-img_5.jpg",
    alt: "Private office",
    title: "Private Office"
  },
  {
    src: "https://via2success.com/wp-content/uploads/2021/01/home-section_4-img_6.jpg",
    alt: "Break room",
    title: "Break Room"
  }
];

export function GallerySection() {
  return (
    <section className="py-16 bg-white overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial="hidden"
          whileInView="visible"
          viewport={animationConfig.viewport}
          variants={fadeInUp}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Tour Your New Office
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Get a glimpse of the professional, all-inclusive executive workspaces available at each of our four convenient locations.
          </p>
        </motion.div>

        {/* Image Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12"
          initial="hidden"
          whileInView="visible"
          viewport={animationConfig.viewport}
          variants={staggerContainer}
        >
          {galleryImages.map((image, index) => (
            <motion.div
              key={index}
              className="group relative overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-all duration-300"
              variants={staggerItem}
              whileHover={{
                y: -8,
                scale: 1.02,
                transition: { duration: 0.2 }
              }}
            >
              <div className="aspect-w-4 aspect-h-3">
                <Image
                  src={image.src}
                  alt={image.alt}
                  width={400}
                  height={300}
                  className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                />
              </div>

              {/* Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                <motion.div
                  className="text-white text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  initial={{ scale: 0.8 }}
                  whileHover={{ scale: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <h3 className="text-lg font-semibold mb-2">{image.title}</h3>
                  <ExternalLink className="w-6 h-6 mx-auto" />
                </motion.div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Action Buttons */}
        <motion.div
          className="text-center space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center"
          initial="hidden"
          whileInView="visible"
          viewport={animationConfig.viewport}
          variants={fadeInUp}
        >
          <Link href="/locations">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                size="lg"
                className="bg-primary hover:bg-primary/90 text-white px-8 py-3 text-lg font-semibold rounded-md transition-all duration-300"
              >
                Tour Locations
              </Button>
            </motion.div>
          </Link>

          <Link href="/gallery">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-primary text-primary hover:bg-primary hover:text-white px-8 py-3 text-lg font-semibold rounded-md transition-all duration-300"
              >
                See Full Gallery
              </Button>
            </motion.div>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
