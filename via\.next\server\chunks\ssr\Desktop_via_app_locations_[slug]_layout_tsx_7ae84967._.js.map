{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/locations/%5Bslug%5D/layout.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\r\nimport { LocationSlugs } from \"@/lib/types/location\";\r\n\r\n// Location data - in a real app, this would come from a database\r\nconst locationData: Record<LocationSlugs, {\r\n  name: string;\r\n  fullName: string;\r\n  address: string;\r\n}> = {\r\n  \"adbc\": {\r\n    name: \"ADBC\",\r\n    fullName: \"Via Executive Suites ADBC\",\r\n    address: \"813 N. Main St., McAllen, Texas 78501\",\r\n  },\r\n  \"la-costa\": {\r\n    name: \"La Costa\",\r\n    fullName: \"Via Executive Suites La Costa\",\r\n    address: \"214 N 16th St, McAllen, Texas 78501\",\r\n  },\r\n  \"23rd\": {\r\n    name: \"23rd\",\r\n    fullName: \"Via Executive Suites 23rd\",\r\n    address: \"1821 N 23rd Street, McAllen, Texas 78501\",\r\n  },\r\n  \"edinburg\": {\r\n    name: \"Edinburg\",\r\n    fullName: \"Via Executive Suites Edinburg\",\r\n    address: \"1409 S 9th Ave, Edinburg, Texas 78539\",\r\n  }\r\n};\r\n\r\ninterface LocationLayoutProps {\r\n  children: React.ReactNode;\r\n  params: Promise<{\r\n    slug: string;\r\n  }>;\r\n}\r\n\r\n// Generate static paths for all location slugs\r\nexport async function generateStaticParams() {\r\n  return Object.keys(locationData).map((slug) => ({\r\n    slug,\r\n  }));\r\n}\r\n\r\nexport async function generateMetadata({ params }: LocationLayoutProps): Promise<Metadata> {\r\n  const resolvedParams = await params;\r\n  const location = locationData[resolvedParams.slug as LocationSlugs];\r\n  \r\n  if (!location) {\r\n    return {\r\n      title: \"Location Not Found - Via Executive Suites\",\r\n    };\r\n  }\r\n\r\n  return {\r\n    title: `${location.fullName} - Via Executive Suites`,\r\n    description: `Visit ${location.fullName} at ${location.address}. Professional office spaces with premium amenities and business support services.`,\r\n    keywords: `${location.name}, office space, McAllen, Edinburg, Rio Grande Valley, executive suites`,\r\n    openGraph: {\r\n      title: `${location.fullName} - Via Executive Suites`,\r\n      description: `Visit ${location.fullName} at ${location.address}. Professional office spaces with premium amenities.`,\r\n      type: \"website\",\r\n      locale: \"en_US\",\r\n    },\r\n  };\r\n}\r\n\r\nexport default function LocationLayout({ children }: LocationLayoutProps) {\r\n  return children;\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAGA,iEAAiE;AACjE,MAAM,eAID;IACH,QAAQ;QACN,MAAM;QACN,UAAU;QACV,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,UAAU;QACV,SAAS;IACX;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,UAAU;QACV,SAAS;IACX;AACF;AAUO,eAAe;IACpB,OAAO,OAAO,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,OAAS,CAAC;YAC9C;QACF,CAAC;AACH;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAuB;IACpE,MAAM,iBAAiB,MAAM;IAC7B,MAAM,WAAW,YAAY,CAAC,eAAe,IAAI,CAAkB;IAEnE,IAAI,CAAC,UAAU;QACb,OAAO;YACL,OAAO;QACT;IACF;IAEA,OAAO;QACL,OAAO,GAAG,SAAS,QAAQ,CAAC,uBAAuB,CAAC;QACpD,aAAa,CAAC,MAAM,EAAE,SAAS,QAAQ,CAAC,IAAI,EAAE,SAAS,OAAO,CAAC,kFAAkF,CAAC;QAClJ,UAAU,GAAG,SAAS,IAAI,CAAC,sEAAsE,CAAC;QAClG,WAAW;YACT,OAAO,GAAG,SAAS,QAAQ,CAAC,uBAAuB,CAAC;YACpD,aAAa,CAAC,MAAM,EAAE,SAAS,QAAQ,CAAC,IAAI,EAAE,SAAS,OAAO,CAAC,oDAAoD,CAAC;YACpH,MAAM;YACN,QAAQ;QACV;IACF;AACF;AAEe,SAAS,eAAe,EAAE,QAAQ,EAAuB;IACtE,OAAO;AACT", "debugId": null}}]}