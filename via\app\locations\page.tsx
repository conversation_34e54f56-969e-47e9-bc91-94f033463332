import { Metadata } from "next";
import { LocationsSection } from "@/components/locations-section";

export const metadata: Metadata = {
  title: "Locations - Via Executive Suites",
  description: "Discover our 4 convenient locations throughout the Rio Grande Valley. Professional office spaces in McAllen and Edinburg, Texas.",
  keywords: "office space locations, McAllen, Edinburg, Rio Grande Valley, executive suites",
  openGraph: {
    title: "Locations - Via Executive Suites",
    description: "Discover our 4 convenient locations throughout the Rio Grande Valley. Professional office spaces in McAllen and Edinburg, Texas.",
    type: "website",
    locale: "en_US",
  },
};

export default function LocationsPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-via-primary via-via-primary-light to-via-primary text-white py-24 lg:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
            Our Locations
          </h1>
          <p className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto">
            Four convenient locations throughout the Rio Grande Valley, designed to serve your business needs
          </p>
        </div>
      </section>

      {/* Locations Section */}
      <LocationsSection />
    </div>
  );
}
