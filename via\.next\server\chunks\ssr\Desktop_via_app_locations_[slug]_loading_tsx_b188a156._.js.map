{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/locations/%5Bslug%5D/loading.tsx"], "sourcesContent": ["export default function LocationLoading() {\r\n  return (\r\n    <div className=\"min-h-screen\">\r\n      {/* Hero Section Skeleton */}\r\n      <section className=\"relative bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 py-24 lg:py-32 overflow-hidden\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\r\n          <div className=\"animate-pulse\">\r\n            <div className=\"h-16 bg-gray-300 rounded-lg mb-6 max-w-2xl mx-auto\"></div>\r\n            <div className=\"h-8 bg-gray-300 rounded-lg max-w-3xl mx-auto\"></div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Content Skeleton */}\r\n      <section className=\"py-16 bg-white overflow-hidden\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\">\r\n            {/* Left Column Skeleton */}\r\n            <div className=\"space-y-8\">\r\n              <div className=\"animate-pulse\">\r\n                <div className=\"h-8 bg-gray-200 rounded-lg mb-6 w-3/4\"></div>\r\n                <div className=\"space-y-3\">\r\n                  <div className=\"h-4 bg-gray-200 rounded w-full\"></div>\r\n                  <div className=\"h-4 bg-gray-200 rounded w-5/6\"></div>\r\n                  <div className=\"h-4 bg-gray-200 rounded w-4/6\"></div>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"space-y-4\">\r\n                <div className=\"h-6 bg-gray-200 rounded-lg w-1/2\"></div>\r\n                <div className=\"space-y-3\">\r\n                  {[1, 2, 3, 4].map((i) => (\r\n                    <div key={i} className=\"flex items-center space-x-3\">\r\n                      <div className=\"w-5 h-5 bg-gray-200 rounded-full\"></div>\r\n                      <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex flex-col sm:flex-row gap-4\">\r\n                {[1, 2, 3].map((i) => (\r\n                  <div key={i} className=\"h-10 bg-gray-200 rounded-lg w-24\"></div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Right Column Skeleton */}\r\n            <div className=\"space-y-6\">\r\n              <div className=\"h-7 bg-gray-200 rounded-lg w-1/2\"></div>\r\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (\r\n                  <div key={i} className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\r\n                    <div className=\"w-2 h-2 bg-gray-200 rounded-full\"></div>\r\n                    <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n\r\n              <div className=\"bg-gray-200 rounded-lg h-64\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section Skeleton */}\r\n      <section className=\"py-16 bg-gray-50 overflow-hidden\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\r\n          <div className=\"animate-pulse\">\r\n            <div className=\"h-8 bg-gray-200 rounded-lg mb-6 max-w-md mx-auto\"></div>\r\n            <div className=\"h-6 bg-gray-200 rounded-lg mb-8 max-w-2xl mx-auto\"></div>\r\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n              <div className=\"h-12 bg-gray-200 rounded-lg w-40\"></div>\r\n              <div className=\"h-12 bg-gray-200 rounded-lg w-40\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,qBACE,+XAAC;QAAI,WAAU;;0BAEb,+XAAC;gBAAQ,WAAU;0BACjB,cAAA,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAI,WAAU;;;;;;0CACf,+XAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMrB,+XAAC;gBAAQ,WAAU;0BACjB,cAAA,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC;wBAAI,WAAU;;0CAEb,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;wCAAI,WAAU;;0DACb,+XAAC;gDAAI,WAAU;;;;;;0DACf,+XAAC;gDAAI,WAAU;;kEACb,+XAAC;wDAAI,WAAU;;;;;;kEACf,+XAAC;wDAAI,WAAU;;;;;;kEACf,+XAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;kDAInB,+XAAC;wCAAI,WAAU;;0DACb,+XAAC;gDAAI,WAAU;;;;;;0DACf,+XAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAG;oDAAG;oDAAG;iDAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,+XAAC;wDAAY,WAAU;;0EACrB,+XAAC;gEAAI,WAAU;;;;;;0EACf,+XAAC;gEAAI,WAAU;;;;;;;uDAFP;;;;;;;;;;;;;;;;kDAQhB,+XAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAG;4CAAG;yCAAE,CAAC,GAAG,CAAC,CAAC,kBACd,+XAAC;gDAAY,WAAU;+CAAb;;;;;;;;;;;;;;;;0CAMhB,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;wCAAI,WAAU;;;;;;kDACf,+XAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAG;4CAAG;4CAAG;4CAAG;4CAAG;4CAAG;4CAAG;yCAAE,CAAC,GAAG,CAAC,CAAC,kBAC7B,+XAAC;gDAAY,WAAU;;kEACrB,+XAAC;wDAAI,WAAU;;;;;;kEACf,+XAAC;wDAAI,WAAU;;;;;;;+CAFP;;;;;;;;;;kDAOd,+XAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,+XAAC;gBAAQ,WAAU;0BACjB,cAAA,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAI,WAAU;;;;;;0CACf,+XAAC;gCAAI,WAAU;;;;;;0CACf,+XAAC;gCAAI,WAAU;;kDACb,+XAAC;wCAAI,WAAU;;;;;;kDACf,+XAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7B", "debugId": null}}]}