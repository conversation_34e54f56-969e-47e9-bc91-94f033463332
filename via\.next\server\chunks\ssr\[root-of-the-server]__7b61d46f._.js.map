{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// This check can be removed, it is just for tutorial purposes\nexport const hasEnvVars =\n  process.env.NEXT_PUBLIC_SUPABASE_URL &&\n  process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sPAAO,EAAC,IAAA,gNAAI,EAAC;AACtB;AAGO,MAAM,aACX", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { Slot } from \"@radix-ui/react-slot\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\";\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nButton.displayName = \"Button\";\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,IAAA,oQAAG,EACxB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,oWAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,+TAAI,GAAG;IAC9B,qBACE,+XAAC;QACC,WAAW,IAAA,oIAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/services/executive-suites/page.tsx"], "sourcesContent": ["import { <PERSON>ada<PERSON> } from \"next\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Check, Building2, Wifi, Users, Phone, Car, Coffee, Shield } from \"lucide-react\";\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Executive Suites - Via Executive Suites\",\r\n  description: \"Professional executive suites with premium amenities, reception services, and meeting rooms. Perfect for established businesses seeking a prestigious address in the Rio Grande Valley.\",\r\n  keywords: \"executive suites, office space, professional workspace, meeting rooms, reception services, Rio Grande Valley\",\r\n  openGraph: {\r\n    title: \"Executive Suites - Via Executive Suites\",\r\n    description: \"Professional executive suites with premium amenities, reception services, and meeting rooms. Perfect for established businesses seeking a prestigious address in the Rio Grande Valley.\",\r\n    type: \"website\",\r\n    locale: \"en_US\",\r\n  },\r\n};\r\n\r\nconst features = [\r\n  {\r\n    icon: Building2,\r\n    title: \"Furnished Offices\",\r\n    description: \"Move-in ready offices with professional furniture and modern design\"\r\n  },\r\n  {\r\n    icon: Wifi,\r\n    title: \"High-Speed Internet\",\r\n    description: \"Reliable, fast internet connectivity for seamless business operations\"\r\n  },\r\n  {\r\n    icon: Users,\r\n    title: \"Reception Services\",\r\n    description: \"Professional reception staff to greet clients and handle inquiries\"\r\n  },\r\n  {\r\n    icon: Phone,\r\n    title: \"Phone Systems\",\r\n    description: \"Professional phone answering and call forwarding services\"\r\n  },\r\n  {\r\n    icon: Car,\r\n    title: \"Free Parking\",\r\n    description: \"Convenient parking for you and your clients\"\r\n  },\r\n  {\r\n    icon: Coffee,\r\n    title: \"Break Room Access\",\r\n    description: \"Shared break rooms with coffee, tea, and refreshments\"\r\n  },\r\n  {\r\n    icon: Shield,\r\n    title: \"Security & Access\",\r\n    description: \"24/7 building access with secure entry systems\"\r\n  }\r\n];\r\n\r\nconst pricingPlans = [\r\n  {\r\n    name: \"Starter Suite\",\r\n    price: \"$599\",\r\n    period: \"/month\",\r\n    description: \"Perfect for solo entrepreneurs and small businesses\",\r\n    features: [\r\n      \"150-200 sq ft office\",\r\n      \"Basic furniture included\",\r\n      \"High-speed internet\",\r\n      \"Reception services\",\r\n      \"Mail handling\",\r\n      \"Meeting room access (2 hours/month)\"\r\n    ],\r\n    popular: false\r\n  },\r\n  {\r\n    name: \"Professional Suite\",\r\n    price: \"$899\",\r\n    period: \"/month\",\r\n    description: \"Ideal for growing businesses and small teams\",\r\n    features: [\r\n      \"250-350 sq ft office\",\r\n      \"Premium furniture included\",\r\n      \"High-speed internet\",\r\n      \"Full reception services\",\r\n      \"Phone answering service\",\r\n      \"Meeting room access (5 hours/month)\",\r\n      \"Kitchen access\"\r\n    ],\r\n    popular: true\r\n  },\r\n  {\r\n    name: \"Executive Suite\",\r\n    price: \"$1,299\",\r\n    period: \"/month\",\r\n    description: \"Premium space for established businesses\",\r\n    features: [\r\n      \"400-500 sq ft office\",\r\n      \"Luxury furniture included\",\r\n      \"High-speed internet\",\r\n      \"Dedicated receptionist\",\r\n      \"Full phone system\",\r\n      \"Unlimited meeting room access\",\r\n      \"Private kitchen access\",\r\n      \"Priority parking\"\r\n    ],\r\n    popular: false\r\n  }\r\n];\r\n\r\nexport default function ExecutiveSuitesPage() {\r\n  return (\r\n    <div className=\"min-h-screen\">\r\n      {/* Hero Section */}\r\n      <section className=\"relative bg-gradient-to-r from-via-primary via-via-primary-light to-via-primary text-white py-24 lg:py-32 overflow-hidden\">\r\n        <div className=\"absolute inset-0 bg-black/20\"></div>\r\n        <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\r\n          <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6\">\r\n            Executive Suites\r\n          </h1>\r\n          <p className=\"text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto\">\r\n            Professional office spaces with premium amenities and services designed for business success\r\n          </p>\r\n          <div className=\"mt-8\">\r\n            <Button size=\"lg\" className=\"bg-white text-via-primary hover:bg-gray-100 px-8 py-3 mr-4\">\r\n              Schedule a Tour\r\n            </Button>\r\n            <Button size=\"lg\" variant=\"outline\" className=\"border-white text-white hover:bg-white hover:text-via-primary px-8 py-3\">\r\n              View Pricing\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Overview Section */}\r\n      <section className=\"py-16 bg-white overflow-hidden\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\r\n            <div>\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-6\">\r\n                Why Choose Via Executive Suites?\r\n              </h2>\r\n              <p className=\"text-lg text-gray-600 mb-6\">\r\n                Our executive suites provide everything you need to run a successful business without the hassle of traditional office management. From move-in ready spaces to comprehensive business services, we handle the details so you can focus on what matters most.\r\n              </p>\r\n              <p className=\"text-lg text-gray-600 mb-8\">\r\n                Located in prime areas throughout the Rio Grande Valley, our suites offer prestigious addresses that enhance your business image while providing the flexibility to scale as your business grows.\r\n              </p>\r\n              <Button size=\"lg\" className=\"bg-via-primary hover:bg-via-primary-dark text-white px-8 py-3\">\r\n                Schedule a Tour\r\n              </Button>\r\n            </div>\r\n            <div className=\"relative\">\r\n              <div className=\"aspect-w-4 aspect-h-3 rounded-lg overflow-hidden shadow-xl\">\r\n                <img\r\n                  src=\"https://via2success.com/wp-content/uploads/2021/01/home-section_3-img_1.jpg\"\r\n                  alt=\"Professional executive suite interior\"\r\n                  className=\"w-full h-full object-cover\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Features Section */}\r\n      <section className=\"py-16 bg-gray-50 overflow-hidden\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center mb-16\">\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n              Everything You Need to Succeed\r\n            </h2>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n              Our executive suites come with comprehensive amenities and services designed to support your business growth.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n            {features.map((feature) => {\r\n              const IconComponent = feature.icon;\r\n              return (\r\n                <div key={feature.title} className=\"bg-white p-6 rounded-lg shadow-md border border-gray-200\">\r\n                  <div className=\"w-12 h-12 bg-via-primary/10 rounded-lg flex items-center justify-center mb-4\">\r\n                    <IconComponent className=\"h-6 w-6 text-via-primary\" />\r\n                  </div>\r\n                  <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">{feature.title}</h3>\r\n                  <p className=\"text-gray-600\">{feature.description}</p>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Pricing Section */}\r\n      <section className=\"py-16 bg-white overflow-hidden\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center mb-16\">\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n              Flexible Pricing Plans\r\n            </h2>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n              Choose the perfect plan for your business needs. All plans include our core amenities with flexible terms.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n            {pricingPlans.map((plan) => (\r\n              <div key={plan.name} className={`relative bg-white rounded-lg shadow-lg border-2 overflow-hidden ${\r\n                plan.popular ? 'border-via-primary' : 'border-gray-200'\r\n              }`}>\r\n                {plan.popular && (\r\n                  <div className=\"absolute top-0 right-0 bg-via-primary text-white px-4 py-2 text-sm font-semibold\">\r\n                    Most Popular\r\n                  </div>\r\n                )}\r\n                \r\n                <div className=\"p-6\">\r\n                  <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">{plan.name}</h3>\r\n                  <p className=\"text-gray-600 mb-4\">{plan.description}</p>\r\n                  \r\n                  <div className=\"mb-6\">\r\n                    <span className=\"text-4xl font-bold text-via-primary\">{plan.price}</span>\r\n                    <span className=\"text-gray-600\">{plan.period}</span>\r\n                  </div>\r\n                  \r\n                  <ul className=\"space-y-3 mb-8\">\r\n                    {plan.features.map((feature) => (\r\n                      <li key={feature} className=\"flex items-center text-gray-700\">\r\n                        <Check className=\"h-5 w-5 text-via-primary mr-3 flex-shrink-0\" />\r\n                        {feature}\r\n                      </li>\r\n                    ))}\r\n                  </ul>\r\n                  \r\n                  <Button className={`w-full ${\r\n                    plan.popular \r\n                      ? 'bg-via-primary hover:bg-via-primary-dark text-white' \r\n                      : 'bg-gray-100 hover:bg-gray-200 text-gray-900'\r\n                  }`}>\r\n                    Get Started\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-16 bg-via-primary text-white overflow-hidden\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\r\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\r\n            Ready to Elevate Your Business?\r\n          </h2>\r\n          <p className=\"text-xl text-via-primary-light mb-8 max-w-2xl mx-auto\">\r\n            Join the growing number of successful businesses that call Via Executive Suites home. Schedule a tour today and see how we can help your business thrive.\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n            <Button size=\"lg\" className=\"bg-white text-via-primary hover:bg-gray-100 px-8 py-3\">\r\n              Schedule a Tour\r\n            </Button>\r\n            <Button size=\"lg\" variant=\"outline\" className=\"border-white text-white hover:bg-white hover:text-via-primary px-8 py-3\">\r\n              Contact Us\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;AACF;AAEA,MAAM,WAAW;IACf;QACE,MAAM,mUAAS;QACf,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,gTAAI;QACV,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,mTAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,mTAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,6SAAG;QACT,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sTAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sTAAM;QACZ,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,eAAe;IACnB;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;CACD;AAEc,SAAS;IACtB,qBACE,+XAAC;QAAI,WAAU;;0BAEb,+XAAC;gBAAQ,WAAU;;kCACjB,+XAAC;wBAAI,WAAU;;;;;;kCACf,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAGhE,+XAAC;gCAAE,WAAU;0CAAsD;;;;;;0CAGnE,+XAAC;gCAAI,WAAU;;kDACb,+XAAC,uJAAM;wCAAC,MAAK;wCAAK,WAAU;kDAA6D;;;;;;kDAGzF,+XAAC,uJAAM;wCAAC,MAAK;wCAAK,SAAQ;wCAAU,WAAU;kDAA0E;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9H,+XAAC;gBAAQ,WAAU;0BACjB,cAAA,+XAAC;oBAAI,WAAU;8BACb,cAAA,+XAAC;wBAAI,WAAU;;0CACb,+XAAC;;kDACC,+XAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,+XAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,+XAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,+XAAC,uJAAM;wCAAC,MAAK;wCAAK,WAAU;kDAAgE;;;;;;;;;;;;0CAI9F,+XAAC;gCAAI,WAAU;0CACb,cAAA,+XAAC;oCAAI,WAAU;8CACb,cAAA,+XAAC;wCACC,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStB,+XAAC;gBAAQ,WAAU;0BACjB,cAAA,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAI,WAAU;;8CACb,+XAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,+XAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,+XAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,gBAAgB,QAAQ,IAAI;gCAClC,qBACE,+XAAC;oCAAwB,WAAU;;sDACjC,+XAAC;4CAAI,WAAU;sDACb,cAAA,+XAAC;gDAAc,WAAU;;;;;;;;;;;sDAE3B,+XAAC;4CAAG,WAAU;sDAA4C,QAAQ,KAAK;;;;;;sDACvE,+XAAC;4CAAE,WAAU;sDAAiB,QAAQ,WAAW;;;;;;;mCALzC,QAAQ,KAAK;;;;;4BAQ3B;;;;;;;;;;;;;;;;;0BAMN,+XAAC;gBAAQ,WAAU;0BACjB,cAAA,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAI,WAAU;;8CACb,+XAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,+XAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,+XAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,+XAAC;oCAAoB,WAAW,CAAC,gEAAgE,EAC/F,KAAK,OAAO,GAAG,uBAAuB,mBACtC;;wCACC,KAAK,OAAO,kBACX,+XAAC;4CAAI,WAAU;sDAAmF;;;;;;sDAKpG,+XAAC;4CAAI,WAAU;;8DACb,+XAAC;oDAAG,WAAU;8DAAyC,KAAK,IAAI;;;;;;8DAChE,+XAAC;oDAAE,WAAU;8DAAsB,KAAK,WAAW;;;;;;8DAEnD,+XAAC;oDAAI,WAAU;;sEACb,+XAAC;4DAAK,WAAU;sEAAuC,KAAK,KAAK;;;;;;sEACjE,+XAAC;4DAAK,WAAU;sEAAiB,KAAK,MAAM;;;;;;;;;;;;8DAG9C,+XAAC;oDAAG,WAAU;8DACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,+XAAC;4DAAiB,WAAU;;8EAC1B,+XAAC,mTAAK;oEAAC,WAAU;;;;;;gEAChB;;2DAFM;;;;;;;;;;8DAOb,+XAAC,uJAAM;oDAAC,WAAW,CAAC,OAAO,EACzB,KAAK,OAAO,GACR,wDACA,+CACJ;8DAAE;;;;;;;;;;;;;mCA/BE,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;0BA0C3B,+XAAC;gBAAQ,WAAU;0BACjB,cAAA,+XAAC;oBAAI,WAAU;;sCACb,+XAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,+XAAC;4BAAE,WAAU;sCAAwD;;;;;;sCAGrE,+XAAC;4BAAI,WAAU;;8CACb,+XAAC,uJAAM;oCAAC,MAAK;oCAAK,WAAU;8CAAwD;;;;;;8CAGpF,+XAAC,uJAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAU,WAAU;8CAA0E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpI", "debugId": null}}]}