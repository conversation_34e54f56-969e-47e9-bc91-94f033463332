{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/connect/animated-hero-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Calendar, Phone } from \"lucide-react\";\nimport { HeroSectionProps } from \"@/types/connect\";\nimport { fadeInUp, fadeInDown, staggerContainer, staggerItem } from \"@/lib/animations\";\nimport { cn } from \"@/lib/utils\";\n\nexport function AnimatedHeroSection({\n  title,\n  subtitle,\n  primaryButtonText,\n  secondaryButtonText,\n  onPrimaryClick,\n  onSecondaryClick,\n  className\n}: HeroSectionProps) {\n  return (\n    <motion.section\n      className={cn(\n        \"relative bg-gradient-to-r from-via-primary via-via-primary-light to-via-primary text-white py-16 sm:py-20 md:py-24 lg:py-32 overflow-hidden\",\n        className\n      )}\n      initial=\"hidden\"\n      animate=\"visible\"\n      variants={staggerContainer}\n    >\n      {/* Background overlay */}\n      <div className=\"absolute inset-0 bg-black/20\"></div>\n      \n      {/* Animated background elements */}\n      <motion.div\n        className=\"absolute inset-0 opacity-10\"\n        initial={{ scale: 0.8, opacity: 0 }}\n        animate={{ scale: 1, opacity: 0.1 }}\n        transition={{ duration: 2, ease: \"easeOut\" }}\n      >\n        <div className=\"absolute top-20 left-10 w-32 h-32 bg-white rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-20 right-10 w-48 h-48 bg-white rounded-full blur-3xl\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white rounded-full blur-3xl\"></div>\n      </motion.div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.h1\n          className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 sm:mb-6\"\n          variants={fadeInDown}\n        >\n          {title}\n        </motion.h1>\n\n        <motion.p\n          className=\"text-lg sm:text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto mb-6 sm:mb-8 px-4\"\n          variants={fadeInUp}\n        >\n          {subtitle}\n        </motion.p>\n        \n        <motion.div\n          className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center px-4\"\n          variants={staggerItem}\n        >\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"w-full sm:w-auto\"\n          >\n            <Button\n              size=\"lg\"\n              className=\"w-full sm:w-auto bg-white text-via-primary hover:bg-gray-100 px-6 sm:px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300\"\n              onClick={onPrimaryClick}\n            >\n              <Calendar className=\"mr-2 h-4 sm:h-5 w-4 sm:w-5\" />\n              {primaryButtonText}\n            </Button>\n          </motion.div>\n\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"w-full sm:w-auto\"\n          >\n            <Button\n              size=\"lg\"\n              variant=\"outline\"\n              className=\"w-full sm:w-auto border-white text-white hover:bg-white hover:text-via-primary px-6 sm:px-8 py-3 border-2 transition-all duration-300\"\n              onClick={onSecondaryClick}\n            >\n              <Phone className=\"mr-2 h-4 sm:h-5 w-4 sm:w-5\" />\n              {secondaryButtonText}\n            </Button>\n          </motion.div>\n        </motion.div>\n      </div>\n    </motion.section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAEA;AACA;AAPA;;;;;;;AASO,SAAS,oBAAoB,KAQjB;QARiB,EAClC,KAAK,EACL,QAAQ,EACR,iBAAiB,EACjB,mBAAmB,EACnB,cAAc,EACd,gBAAgB,EAChB,SAAS,EACQ,GARiB;IASlC,qBACE,8UAAC,+TAAM,CAAC,OAAO;QACb,WAAW,IAAA,uIAAE,EACX,+IACA;QAEF,SAAQ;QACR,SAAQ;QACR,UAAU,0JAAgB;;0BAG1B,8UAAC;gBAAI,WAAU;;;;;;0BAGf,8UAAC,+TAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAI;gBAClC,YAAY;oBAAE,UAAU;oBAAG,MAAM;gBAAU;;kCAE3C,8UAAC;wBAAI,WAAU;;;;;;kCACf,8UAAC;wBAAI,WAAU;;;;;;kCACf,8UAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8UAAC;gBAAI,WAAU;;kCACb,8UAAC,+TAAM,CAAC,EAAE;wBACR,WAAU;wBACV,UAAU,oJAAU;kCAEnB;;;;;;kCAGH,8UAAC,+TAAM,CAAC,CAAC;wBACP,WAAU;wBACV,UAAU,kJAAQ;kCAEjB;;;;;;kCAGH,8UAAC,+TAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU,qJAAW;;0CAErB,8UAAC,+TAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CAEV,cAAA,8UAAC,0JAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS;;sDAET,8UAAC,+TAAQ;4CAAC,WAAU;;;;;;wCACnB;;;;;;;;;;;;0CAIL,8UAAC,+TAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CAEV,cAAA,8UAAC,0JAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;;sDAET,8UAAC,sTAAK;4CAAC,WAAU;;;;;;wCAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;KAvFgB", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/connect/animated-contact-info.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { ContactInfo, ContactSectionProps } from \"@/types/connect\";\nimport { staggerContainer, staggerItem } from \"@/lib/animations\";\nimport { cn } from \"@/lib/utils\";\n\ninterface AnimatedContactCardProps {\n  info: ContactInfo;\n  index: number;\n}\n\nfunction AnimatedContactCard({ info, index }: AnimatedContactCardProps) {\n  const IconComponent = info.icon;\n  \n  return (\n    <motion.div\n      className=\"text-center group\"\n      variants={staggerItem}\n      whileHover={{\n        y: -8,\n        transition: { duration: 0.2, ease: \"easeOut\" }\n      }}\n    >\n      <motion.div \n        className=\"w-16 h-16 bg-via-primary/10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-via-primary/20 transition-colors duration-300\"\n        whileHover={{\n          scale: 1.1,\n          rotate: 5,\n          transition: { duration: 0.2 }\n        }}\n      >\n        <IconComponent className=\"h-8 w-8 text-via-primary\" />\n      </motion.div>\n      \n      <motion.h3 \n        className=\"text-lg font-semibold text-gray-900 mb-2\"\n        whileHover={{\n          scale: 1.05,\n          transition: { duration: 0.2 }\n        }}\n      >\n        {info.title}\n      </motion.h3>\n      \n      <motion.div\n        initial={{ opacity: 0.8 }}\n        whileHover={{ opacity: 1 }}\n        transition={{ duration: 0.2 }}\n      >\n        {info.href ? (\n          <a \n            href={info.href}\n            className=\"text-via-primary font-medium mb-2 block hover:text-via-primary-dark transition-colors duration-200\"\n          >\n            {info.value}\n          </a>\n        ) : (\n          <p className=\"text-via-primary font-medium mb-2\">{info.value}</p>\n        )}\n        <p className=\"text-gray-600 text-sm\">{info.description}</p>\n      </motion.div>\n    </motion.div>\n  );\n}\n\nexport function AnimatedContactInfo({\n  title,\n  subtitle,\n  contactInfo,\n  className\n}: ContactSectionProps) {\n  return (\n    <motion.section\n      className={cn(\"py-12 sm:py-16 lg:py-20 bg-white overflow-hidden\", className)}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: \"-100px\" }}\n      variants={staggerContainer}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-12 sm:mb-16\"\n          variants={staggerItem}\n        >\n          <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            {title}\n          </h2>\n          {subtitle && (\n            <p className=\"text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto\">\n              {subtitle}\n            </p>\n          )}\n        </motion.div>\n\n        <motion.div\n          className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8\"\n          variants={staggerContainer}\n        >\n          {contactInfo.map((info, index) => (\n            <AnimatedContactCard\n              key={info.title}\n              info={info}\n              index={index}\n            />\n          ))}\n        </motion.div>\n      </div>\n    </motion.section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AALA;;;;;AAYA,SAAS,oBAAoB,KAAyC;QAAzC,EAAE,IAAI,EAAE,KAAK,EAA4B,GAAzC;IAC3B,MAAM,gBAAgB,KAAK,IAAI;IAE/B,qBACE,8UAAC,+TAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU,qJAAW;QACrB,YAAY;YACV,GAAG,CAAC;YACJ,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;;0BAEA,8UAAC,+TAAM,CAAC,GAAG;gBACT,WAAU;gBACV,YAAY;oBACV,OAAO;oBACP,QAAQ;oBACR,YAAY;wBAAE,UAAU;oBAAI;gBAC9B;0BAEA,cAAA,8UAAC;oBAAc,WAAU;;;;;;;;;;;0BAG3B,8UAAC,+TAAM,CAAC,EAAE;gBACR,WAAU;gBACV,YAAY;oBACV,OAAO;oBACP,YAAY;wBAAE,UAAU;oBAAI;gBAC9B;0BAEC,KAAK,KAAK;;;;;;0BAGb,8UAAC,+TAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAI;gBACxB,YAAY;oBAAE,SAAS;gBAAE;gBACzB,YAAY;oBAAE,UAAU;gBAAI;;oBAE3B,KAAK,IAAI,iBACR,8UAAC;wBACC,MAAM,KAAK,IAAI;wBACf,WAAU;kCAET,KAAK,KAAK;;;;;6CAGb,8UAAC;wBAAE,WAAU;kCAAqC,KAAK,KAAK;;;;;;kCAE9D,8UAAC;wBAAE,WAAU;kCAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;AAI9D;KApDS;AAsDF,SAAS,oBAAoB,KAKd;QALc,EAClC,KAAK,EACL,QAAQ,EACR,WAAW,EACX,SAAS,EACW,GALc;IAMlC,qBACE,8UAAC,+TAAM,CAAC,OAAO;QACb,WAAW,IAAA,uIAAE,EAAC,oDAAoD;QAClE,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;QACzC,UAAU,0JAAgB;kBAE1B,cAAA,8UAAC;YAAI,WAAU;;8BACb,8UAAC,+TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU,qJAAW;;sCAErB,8UAAC;4BAAG,WAAU;sCACX;;;;;;wBAEF,0BACC,8UAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAKP,8UAAC,+TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU,0JAAgB;8BAEzB,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8UAAC;4BAEC,MAAM;4BACN,OAAO;2BAFF,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;AAS7B;MA5CgB", "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className,\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  },\n);\nInput.displayName = \"Input\";\n\nexport { Input };\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,4TAAgB,MAC5B,QAAgC;QAA/B,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO;IAC5B,qBACE,8UAAC;QACC,MAAM;QACN,WAAW,IAAA,uIAAE,EACX,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/ui/label.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as LabelPrimitive from \"@radix-ui/react-label\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n);\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n));\nLabel.displayName = LabelPrimitive.Root.displayName;\n\nexport { Label };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,IAAA,uQAAG,EACvB;AAGF,MAAM,sBAAQ,4TAAgB,MAI5B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,8UAAC,2SAAmB;QAClB,KAAK;QACL,WAAW,IAAA,uIAAE,EAAC,iBAAiB;QAC9B,GAAG,KAAK;;;;;;;;AAGb,MAAM,WAAW,GAAG,2SAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAIA,MAAM,yBAAW,4TAAgB,MAC/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;IACtB,qBACE,8UAAC;QACC,WAAW,IAAA,uIAAE,EACX,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/connect/enhanced-contact-form.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useCallback } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { MessageCircle, Loader2, CheckCircle, AlertCircle } from \"lucide-react\";\nimport { \n  ContactFormData, \n  ContactFormErrors, \n  FormSubmissionState,\n  ContactFormProps \n} from \"@/types/connect\";\nimport { staggerContainer, staggerItem } from \"@/lib/animations\";\nimport { cn } from \"@/lib/utils\";\n\n// Form validation utility\nconst validateForm = (data: ContactFormData): ContactFormErrors => {\n  const errors: ContactFormErrors = {};\n\n  if (!data.firstName.trim()) {\n    errors.firstName = \"First name is required\";\n  } else if (data.firstName.length < 2) {\n    errors.firstName = \"First name must be at least 2 characters\";\n  }\n\n  if (!data.lastName.trim()) {\n    errors.lastName = \"Last name is required\";\n  } else if (data.lastName.length < 2) {\n    errors.lastName = \"Last name must be at least 2 characters\";\n  }\n\n  if (!data.email.trim()) {\n    errors.email = \"Email is required\";\n  } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.email)) {\n    errors.email = \"Please enter a valid email address\";\n  }\n\n  if (data.phone && !/^[\\+]?[1-9][\\d]{0,15}$/.test(data.phone.replace(/[\\s\\-\\(\\)]/g, ''))) {\n    errors.phone = \"Please enter a valid phone number\";\n  }\n\n  if (!data.message.trim()) {\n    errors.message = \"Message is required\";\n  } else if (data.message.length < 10) {\n    errors.message = \"Message must be at least 10 characters\";\n  }\n\n  return errors;\n};\n\nexport function EnhancedContactForm({ \n  onSubmit, \n  className,\n  showCompanyField = true,\n  showPhoneField = true \n}: ContactFormProps) {\n  const [formData, setFormData] = useState<ContactFormData>({\n    firstName: \"\",\n    lastName: \"\",\n    email: \"\",\n    phone: \"\",\n    company: \"\",\n    message: \"\"\n  });\n\n  const [errors, setErrors] = useState<ContactFormErrors>({});\n  const [submissionState, setSubmissionState] = useState<FormSubmissionState>({\n    isLoading: false,\n    isSuccess: false,\n    error: null\n  });\n\n  const handleInputChange = useCallback((\n    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>\n  ) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n    \n    // Clear error for this field when user starts typing\n    if (errors[name as keyof ContactFormErrors]) {\n      setErrors(prev => ({ ...prev, [name]: undefined }));\n    }\n  }, [errors]);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    // Validate form\n    const validationErrors = validateForm(formData);\n    if (Object.keys(validationErrors).length > 0) {\n      setErrors(validationErrors);\n      return;\n    }\n\n    setSubmissionState({ isLoading: true, isSuccess: false, error: null });\n\n    try {\n      if (onSubmit) {\n        await onSubmit(formData);\n      } else {\n        // Default submission logic\n        await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call\n        console.log(\"Form submitted:\", formData);\n      }\n      \n      setSubmissionState({ isLoading: false, isSuccess: true, error: null });\n      \n      // Reset form after successful submission\n      setTimeout(() => {\n        setFormData({\n          firstName: \"\",\n          lastName: \"\",\n          email: \"\",\n          phone: \"\",\n          company: \"\",\n          message: \"\"\n        });\n        setSubmissionState({ isLoading: false, isSuccess: false, error: null });\n      }, 3000);\n      \n    } catch (error) {\n      setSubmissionState({\n        isLoading: false,\n        isSuccess: false,\n        error: error instanceof Error ? error.message : \"An error occurred\"\n      });\n    }\n  };\n\n  return (\n    <motion.section\n      className={cn(\"py-12 sm:py-16 lg:py-20 bg-gray-50 overflow-hidden\", className)}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: \"-100px\" }}\n      variants={staggerContainer}\n    >\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-8 sm:mb-12\"\n          variants={staggerItem}\n        >\n          <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Send Us a Message\n          </h2>\n          <p className=\"text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto\">\n            Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.\n          </p>\n        </motion.div>\n\n        <motion.form\n          onSubmit={handleSubmit}\n          className=\"bg-white rounded-lg shadow-lg p-6 sm:p-8\"\n          variants={staggerItem}\n        >\n          {/* Success Message */}\n          {submissionState.isSuccess && (\n            <motion.div\n              initial={{ opacity: 0, y: -20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"mb-6 p-4 bg-green-50 border border-green-200 rounded-md flex items-center gap-3\"\n            >\n              <CheckCircle className=\"h-5 w-5 text-green-600\" />\n              <p className=\"text-green-800\">Thank you! Your message has been sent successfully.</p>\n            </motion.div>\n          )}\n\n          {/* Error Message */}\n          {submissionState.error && (\n            <motion.div\n              initial={{ opacity: 0, y: -20 }}\n              animate={{ opacity: 1, y: 0 }}\n              className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-md flex items-center gap-3\"\n            >\n              <AlertCircle className=\"h-5 w-5 text-red-600\" />\n              <p className=\"text-red-800\">{submissionState.error}</p>\n            </motion.div>\n          )}\n\n          {/* Name Fields */}\n          <div className=\"grid sm:grid-cols-2 gap-4 sm:gap-6 mb-6\">\n            <div>\n              <Label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                First Name *\n              </Label>\n              <Input\n                type=\"text\"\n                id=\"firstName\"\n                name=\"firstName\"\n                value={formData.firstName}\n                onChange={handleInputChange}\n                className={cn(\n                  \"w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent\",\n                  errors.firstName ? \"border-red-300\" : \"border-gray-300\"\n                )}\n                disabled={submissionState.isLoading}\n              />\n              {errors.firstName && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.firstName}</p>\n              )}\n            </div>\n            <div>\n              <Label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Last Name *\n              </Label>\n              <Input\n                type=\"text\"\n                id=\"lastName\"\n                name=\"lastName\"\n                value={formData.lastName}\n                onChange={handleInputChange}\n                className={cn(\n                  \"w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent\",\n                  errors.lastName ? \"border-red-300\" : \"border-gray-300\"\n                )}\n                disabled={submissionState.isLoading}\n              />\n              {errors.lastName && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.lastName}</p>\n              )}\n            </div>\n          </div>\n\n          {/* Email and Phone Fields */}\n          <div className=\"grid sm:grid-cols-2 gap-4 sm:gap-6 mb-6\">\n            <div>\n              <Label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Email *\n              </Label>\n              <Input\n                type=\"email\"\n                id=\"email\"\n                name=\"email\"\n                value={formData.email}\n                onChange={handleInputChange}\n                className={cn(\n                  \"w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent\",\n                  errors.email ? \"border-red-300\" : \"border-gray-300\"\n                )}\n                disabled={submissionState.isLoading}\n              />\n              {errors.email && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.email}</p>\n              )}\n            </div>\n            {showPhoneField && (\n              <div>\n                <Label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Phone\n                </Label>\n                <Input\n                  type=\"tel\"\n                  id=\"phone\"\n                  name=\"phone\"\n                  value={formData.phone}\n                  onChange={handleInputChange}\n                  className={cn(\n                    \"w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent\",\n                    errors.phone ? \"border-red-300\" : \"border-gray-300\"\n                  )}\n                  disabled={submissionState.isLoading}\n                />\n                {errors.phone && (\n                  <p className=\"mt-1 text-sm text-red-600\">{errors.phone}</p>\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Company Field */}\n          {showCompanyField && (\n            <div className=\"mb-6\">\n              <Label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Company\n              </Label>\n              <Input\n                type=\"text\"\n                id=\"company\"\n                name=\"company\"\n                value={formData.company}\n                onChange={handleInputChange}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent\"\n                disabled={submissionState.isLoading}\n              />\n            </div>\n          )}\n\n          {/* Message Field */}\n          <div className=\"mb-6\">\n            <Label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Message *\n            </Label>\n            <Textarea\n              id=\"message\"\n              name=\"message\"\n              rows={5}\n              value={formData.message}\n              onChange={handleInputChange}\n              className={cn(\n                \"w-full px-4 py-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent\",\n                errors.message ? \"border-red-300\" : \"border-gray-300\"\n              )}\n              placeholder=\"Tell us about your business needs and how we can help...\"\n              disabled={submissionState.isLoading}\n            />\n            {errors.message && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.message}</p>\n            )}\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"text-center\">\n            <Button \n              type=\"submit\" \n              size=\"lg\" \n              className=\"bg-via-primary hover:bg-via-primary-dark text-white px-8 py-3 disabled:opacity-50\"\n              disabled={submissionState.isLoading}\n            >\n              {submissionState.isLoading ? (\n                <>\n                  <Loader2 className=\"mr-2 h-5 w-5 animate-spin\" />\n                  Sending...\n                </>\n              ) : (\n                <>\n                  <MessageCircle className=\"mr-2 h-5 w-5\" />\n                  Send Message\n                </>\n              )}\n            </Button>\n          </div>\n        </motion.form>\n      </div>\n    </motion.section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAOA;AACA;;;AAhBA;;;;;;;;;;AAkBA,0BAA0B;AAC1B,MAAM,eAAe,CAAC;IACpB,MAAM,SAA4B,CAAC;IAEnC,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,IAAI;QAC1B,OAAO,SAAS,GAAG;IACrB,OAAO,IAAI,KAAK,SAAS,CAAC,MAAM,GAAG,GAAG;QACpC,OAAO,SAAS,GAAG;IACrB;IAEA,IAAI,CAAC,KAAK,QAAQ,CAAC,IAAI,IAAI;QACzB,OAAO,QAAQ,GAAG;IACpB,OAAO,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;QACnC,OAAO,QAAQ,GAAG;IACpB;IAEA,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI;QACtB,OAAO,KAAK,GAAG;IACjB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,KAAK,GAAG;QACzD,OAAO,KAAK,GAAG;IACjB;IAEA,IAAI,KAAK,KAAK,IAAI,CAAC,yBAAyB,IAAI,CAAC,KAAK,KAAK,CAAC,OAAO,CAAC,eAAe,MAAM;QACvF,OAAO,KAAK,GAAG;IACjB;IAEA,IAAI,CAAC,KAAK,OAAO,CAAC,IAAI,IAAI;QACxB,OAAO,OAAO,GAAG;IACnB,OAAO,IAAI,KAAK,OAAO,CAAC,MAAM,GAAG,IAAI;QACnC,OAAO,OAAO,GAAG;IACnB;IAEA,OAAO;AACT;AAEO,SAAS,oBAAoB,KAKjB;QALiB,EAClC,QAAQ,EACR,SAAS,EACT,mBAAmB,IAAI,EACvB,iBAAiB,IAAI,EACJ,GALiB;;IAMlC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,0TAAQ,EAAkB;QACxD,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,0TAAQ,EAAoB,CAAC;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,0TAAQ,EAAsB;QAC1E,WAAW;QACX,WAAW;QACX,OAAO;IACT;IAEA,MAAM,oBAAoB,IAAA,6TAAW;8DAAC,CACpC;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;YAChC;sEAAY,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,KAAK,EAAE;oBAAM,CAAC;;YAE/C,qDAAqD;YACrD,IAAI,MAAM,CAAC,KAAgC,EAAE;gBAC3C;0EAAU,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,CAAC,KAAK,EAAE;wBAAU,CAAC;;YACnD;QACF;6DAAG;QAAC;KAAO;IAEX,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,gBAAgB;QAChB,MAAM,mBAAmB,aAAa;QACtC,IAAI,OAAO,IAAI,CAAC,kBAAkB,MAAM,GAAG,GAAG;YAC5C,UAAU;YACV;QACF;QAEA,mBAAmB;YAAE,WAAW;YAAM,WAAW;YAAO,OAAO;QAAK;QAEpE,IAAI;YACF,IAAI,UAAU;gBACZ,MAAM,SAAS;YACjB,OAAO;gBACL,2BAA2B;gBAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ,oBAAoB;gBAC7E,QAAQ,GAAG,CAAC,mBAAmB;YACjC;YAEA,mBAAmB;gBAAE,WAAW;gBAAO,WAAW;gBAAM,OAAO;YAAK;YAEpE,yCAAyC;YACzC,WAAW;gBACT,YAAY;oBACV,WAAW;oBACX,UAAU;oBACV,OAAO;oBACP,OAAO;oBACP,SAAS;oBACT,SAAS;gBACX;gBACA,mBAAmB;oBAAE,WAAW;oBAAO,WAAW;oBAAO,OAAO;gBAAK;YACvE,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,mBAAmB;gBACjB,WAAW;gBACX,WAAW;gBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,qBACE,8UAAC,+TAAM,CAAC,OAAO;QACb,WAAW,IAAA,uIAAE,EAAC,sDAAsD;QACpE,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;QACzC,UAAU,0JAAgB;kBAE1B,cAAA,8UAAC;YAAI,WAAU;;8BACb,8UAAC,+TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU,qJAAW;;sCAErB,8UAAC;4BAAG,WAAU;sCAAgE;;;;;;sCAG9E,8UAAC;4BAAE,WAAU;sCAAqD;;;;;;;;;;;;8BAKpE,8UAAC,+TAAM,CAAC,IAAI;oBACV,UAAU;oBACV,WAAU;oBACV,UAAU,qJAAW;;wBAGpB,gBAAgB,SAAS,kBACxB,8UAAC,+TAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;;8CAEV,8UAAC,mVAAW;oCAAC,WAAU;;;;;;8CACvB,8UAAC;oCAAE,WAAU;8CAAiB;;;;;;;;;;;;wBAKjC,gBAAgB,KAAK,kBACpB,8UAAC,+TAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;;8CAEV,8UAAC,4UAAW;oCAAC,WAAU;;;;;;8CACvB,8UAAC;oCAAE,WAAU;8CAAgB,gBAAgB,KAAK;;;;;;;;;;;;sCAKtD,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;;sDACC,8UAAC,wJAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAA+C;;;;;;sDAGpF,8UAAC,wJAAK;4CACJ,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,SAAS;4CACzB,UAAU;4CACV,WAAW,IAAA,uIAAE,EACX,sHACA,OAAO,SAAS,GAAG,mBAAmB;4CAExC,UAAU,gBAAgB,SAAS;;;;;;wCAEpC,OAAO,SAAS,kBACf,8UAAC;4CAAE,WAAU;sDAA6B,OAAO,SAAS;;;;;;;;;;;;8CAG9D,8UAAC;;sDACC,8UAAC,wJAAK;4CAAC,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,8UAAC,wJAAK;4CACJ,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU;4CACV,WAAW,IAAA,uIAAE,EACX,sHACA,OAAO,QAAQ,GAAG,mBAAmB;4CAEvC,UAAU,gBAAgB,SAAS;;;;;;wCAEpC,OAAO,QAAQ,kBACd,8UAAC;4CAAE,WAAU;sDAA6B,OAAO,QAAQ;;;;;;;;;;;;;;;;;;sCAM/D,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;;sDACC,8UAAC,wJAAK;4CAAC,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8UAAC,wJAAK;4CACJ,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAW,IAAA,uIAAE,EACX,sHACA,OAAO,KAAK,GAAG,mBAAmB;4CAEpC,UAAU,gBAAgB,SAAS;;;;;;wCAEpC,OAAO,KAAK,kBACX,8UAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;gCAGzD,gCACC,8UAAC;;sDACC,8UAAC,wJAAK;4CAAC,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8UAAC,wJAAK;4CACJ,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,WAAW,IAAA,uIAAE,EACX,sHACA,OAAO,KAAK,GAAG,mBAAmB;4CAEpC,UAAU,gBAAgB,SAAS;;;;;;wCAEpC,OAAO,KAAK,kBACX,8UAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK;;;;;;;;;;;;;;;;;;wBAO7D,kCACC,8UAAC;4BAAI,WAAU;;8CACb,8UAAC,wJAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAA+C;;;;;;8CAGlF,8UAAC,wJAAK;oCACJ,MAAK;oCACL,IAAG;oCACH,MAAK;oCACL,OAAO,SAAS,OAAO;oCACvB,UAAU;oCACV,WAAU;oCACV,UAAU,gBAAgB,SAAS;;;;;;;;;;;;sCAMzC,8UAAC;4BAAI,WAAU;;8CACb,8UAAC,wJAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAA+C;;;;;;8CAGlF,8UAAC,8JAAQ;oCACP,IAAG;oCACH,MAAK;oCACL,MAAM;oCACN,OAAO,SAAS,OAAO;oCACvB,UAAU;oCACV,WAAW,IAAA,uIAAE,EACX,sHACA,OAAO,OAAO,GAAG,mBAAmB;oCAEtC,aAAY;oCACZ,UAAU,gBAAgB,SAAS;;;;;;gCAEpC,OAAO,OAAO,kBACb,8UAAC;oCAAE,WAAU;8CAA6B,OAAO,OAAO;;;;;;;;;;;;sCAK5D,8UAAC;4BAAI,WAAU;sCACb,cAAA,8UAAC,0JAAM;gCACL,MAAK;gCACL,MAAK;gCACL,WAAU;gCACV,UAAU,gBAAgB,SAAS;0CAElC,gBAAgB,SAAS,iBACxB;;sDACE,8UAAC,qUAAO;4CAAC,WAAU;;;;;;wCAA8B;;iEAInD;;sDACE,8UAAC,kVAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5D;GA7RgB;KAAA", "debugId": null}}, {"offset": {"line": 1063, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/connect/animated-locations-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { MapPin, Phone, Calendar } from \"lucide-react\";\nimport { LocationInfo, LocationSectionProps } from \"@/types/connect\";\nimport { staggerContainer, staggerItem } from \"@/lib/animations\";\nimport { cn } from \"@/lib/utils\";\n\ninterface AnimatedLocationCardProps {\n  location: LocationInfo;\n  index: number;\n}\n\nfunction AnimatedLocationCard({ location, index }: AnimatedLocationCardProps) {\n  return (\n    <motion.div\n      className=\"bg-gray-50 rounded-lg p-4 sm:p-6 border border-gray-200 group hover:shadow-lg transition-all duration-300\"\n      variants={staggerItem}\n      whileHover={{\n        y: -8,\n        scale: 1.02,\n        boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)\",\n        transition: { duration: 0.3, ease: \"easeInOut\" }\n      }}\n      whileTap={{ scale: 0.98 }}\n    >\n      <motion.h3 \n        className=\"text-xl font-semibold text-gray-900 mb-3 group-hover:text-via-primary transition-colors duration-200\"\n        whileHover={{\n          scale: 1.05,\n          transition: { duration: 0.2 }\n        }}\n      >\n        {location.name}\n      </motion.h3>\n      \n      <div className=\"space-y-2 mb-4\">\n        <motion.div \n          className=\"flex items-start\"\n          whileHover={{ x: 4, transition: { duration: 0.2 } }}\n        >\n          <MapPin className=\"h-5 w-5 text-via-primary mr-3 mt-0.5 flex-shrink-0\" />\n          <p className=\"text-gray-600\">{location.address}</p>\n        </motion.div>\n        \n        <motion.div \n          className=\"flex items-center\"\n          whileHover={{ x: 4, transition: { duration: 0.2 } }}\n        >\n          <Phone className=\"h-5 w-5 text-via-primary mr-3 flex-shrink-0\" />\n          <a \n            href={`tel:${location.phone}`}\n            className=\"text-gray-600 hover:text-via-primary transition-colors duration-200\"\n          >\n            {location.phone}\n          </a>\n        </motion.div>\n        \n        {location.hours && (\n          <motion.div \n            className=\"flex items-center\"\n            whileHover={{ x: 4, transition: { duration: 0.2 } }}\n          >\n            <Calendar className=\"h-5 w-5 text-via-primary mr-3 flex-shrink-0\" />\n            <p className=\"text-gray-600 text-sm\">{location.hours}</p>\n          </motion.div>\n        )}\n      </div>\n      \n      <motion.div\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n      >\n        <Button \n          variant=\"outline\" \n          className=\"border-via-primary text-via-primary hover:bg-via-primary hover:text-white transition-all duration-200 w-full\"\n        >\n          <Calendar className=\"mr-2 h-4 w-4\" />\n          Schedule Tour\n        </Button>\n      </motion.div>\n    </motion.div>\n  );\n}\n\nexport function AnimatedLocationsSection({\n  title,\n  subtitle,\n  locations,\n  className\n}: LocationSectionProps) {\n  return (\n    <motion.section\n      className={cn(\"py-12 sm:py-16 lg:py-20 bg-white overflow-hidden\", className)}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: \"-100px\" }}\n      variants={staggerContainer}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-12 sm:mb-16\"\n          variants={staggerItem}\n        >\n          <h2 className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            {title}\n          </h2>\n          {subtitle && (\n            <p className=\"text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto\">\n              {subtitle}\n            </p>\n          )}\n        </motion.div>\n\n        <motion.div\n          className=\"grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8\"\n          variants={staggerContainer}\n        >\n          {locations.map((location, index) => (\n            <AnimatedLocationCard\n              key={location.name}\n              location={location}\n              index={index}\n            />\n          ))}\n        </motion.div>\n      </div>\n    </motion.section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AAPA;;;;;;;AAcA,SAAS,qBAAqB,KAA8C;QAA9C,EAAE,QAAQ,EAAE,KAAK,EAA6B,GAA9C;IAC5B,qBACE,8UAAC,+TAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU,qJAAW;QACrB,YAAY;YACV,GAAG,CAAC;YACJ,OAAO;YACP,WAAW;YACX,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAY;QACjD;QACA,UAAU;YAAE,OAAO;QAAK;;0BAExB,8UAAC,+TAAM,CAAC,EAAE;gBACR,WAAU;gBACV,YAAY;oBACV,OAAO;oBACP,YAAY;wBAAE,UAAU;oBAAI;gBAC9B;0BAEC,SAAS,IAAI;;;;;;0BAGhB,8UAAC;gBAA<PERSON>,WAAU;;kCACb,8UAAC,+TAAM,CAAC,GAAG;wBACT,WAAU;wBACV,YAAY;4BAAE,GAAG;4BAAG,YAAY;gCAAE,UAAU;4BAAI;wBAAE;;0CAElD,8UAAC,6TAAM;gCAAC,WAAU;;;;;;0CAClB,8UAAC;gCAAE,WAAU;0CAAiB,SAAS,OAAO;;;;;;;;;;;;kCAGhD,8UAAC,+TAAM,CAAC,GAAG;wBACT,WAAU;wBACV,YAAY;4BAAE,GAAG;4BAAG,YAAY;gCAAE,UAAU;4BAAI;wBAAE;;0CAElD,8UAAC,sTAAK;gCAAC,WAAU;;;;;;0CACjB,8UAAC;gCACC,MAAM,AAAC,OAAqB,OAAf,SAAS,KAAK;gCAC3B,WAAU;0CAET,SAAS,KAAK;;;;;;;;;;;;oBAIlB,SAAS,KAAK,kBACb,8UAAC,+TAAM,CAAC,GAAG;wBACT,WAAU;wBACV,YAAY;4BAAE,GAAG;4BAAG,YAAY;gCAAE,UAAU;4BAAI;wBAAE;;0CAElD,8UAAC,+TAAQ;gCAAC,WAAU;;;;;;0CACpB,8UAAC;gCAAE,WAAU;0CAAyB,SAAS,KAAK;;;;;;;;;;;;;;;;;;0BAK1D,8UAAC,+TAAM,CAAC,GAAG;gBACT,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;0BAExB,cAAA,8UAAC,0JAAM;oBACL,SAAQ;oBACR,WAAU;;sCAEV,8UAAC,+TAAQ;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAM/C;KAtES;AAwEF,SAAS,yBAAyB,KAKlB;QALkB,EACvC,KAAK,EACL,QAAQ,EACR,SAAS,EACT,SAAS,EACY,GALkB;IAMvC,qBACE,8UAAC,+TAAM,CAAC,OAAO;QACb,WAAW,IAAA,uIAAE,EAAC,oDAAoD;QAClE,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;QACzC,UAAU,0JAAgB;kBAE1B,cAAA,8UAAC;YAAI,WAAU;;8BACb,8UAAC,+TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU,qJAAW;;sCAErB,8UAAC;4BAAG,WAAU;sCACX;;;;;;wBAEF,0BACC,8UAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAKP,8UAAC,+TAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU,0JAAgB;8BAEzB,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8UAAC;4BAEC,UAAU;4BACV,OAAO;2BAFF,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;AAShC;MA5CgB", "debugId": null}}, {"offset": {"line": 1331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/components/connect/animated-cta-section.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Calendar, Phone } from \"lucide-react\";\nimport { CTASectionProps } from \"@/types/connect\";\nimport { fadeInUp, staggerContainer, staggerItem } from \"@/lib/animations\";\nimport { cn } from \"@/lib/utils\";\n\nexport function AnimatedCTASection({\n  title,\n  subtitle,\n  primaryButtonText,\n  secondaryButtonText,\n  onPrimaryClick,\n  onSecondaryClick,\n  className\n}: CTASectionProps) {\n  return (\n    <motion.section\n      className={cn(\"py-12 sm:py-16 lg:py-20 bg-via-primary text-white overflow-hidden relative\", className)}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: \"-100px\" }}\n      variants={staggerContainer}\n    >\n      {/* Animated background elements */}\n      <motion.div\n        className=\"absolute inset-0 opacity-5\"\n        initial={{ scale: 0.8, opacity: 0 }}\n        whileInView={{ scale: 1, opacity: 0.05 }}\n        transition={{ duration: 2, ease: \"easeOut\" }}\n        viewport={{ once: true }}\n      >\n        <div className=\"absolute top-10 left-10 w-24 h-24 bg-white rounded-full blur-2xl\"></div>\n        <div className=\"absolute bottom-10 right-10 w-32 h-32 bg-white rounded-full blur-2xl\"></div>\n        <div className=\"absolute top-1/2 left-1/3 w-20 h-20 bg-white rounded-full blur-2xl\"></div>\n      </motion.div>\n\n      <div className=\"relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.h2\n          className=\"text-2xl sm:text-3xl md:text-4xl font-bold mb-4\"\n          variants={fadeInUp}\n        >\n          {title}\n        </motion.h2>\n\n        <motion.p\n          className=\"text-lg sm:text-xl text-via-primary-light mb-6 sm:mb-8 max-w-2xl mx-auto px-4\"\n          variants={fadeInUp}\n        >\n          {subtitle}\n        </motion.p>\n\n        <motion.div\n          className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center px-4\"\n          variants={staggerItem}\n        >\n          <motion.div\n            whileHover={{\n              scale: 1.05,\n              boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.2)\"\n            }}\n            whileTap={{ scale: 0.95 }}\n            transition={{ duration: 0.2 }}\n            className=\"w-full sm:w-auto\"\n          >\n            <Button\n              size=\"lg\"\n              className=\"w-full sm:w-auto bg-white text-via-primary hover:bg-gray-100 px-6 sm:px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300\"\n              onClick={onPrimaryClick}\n            >\n              <Calendar className=\"mr-2 h-4 sm:h-5 w-4 sm:w-5\" />\n              {primaryButtonText}\n            </Button>\n          </motion.div>\n\n          <motion.div\n            whileHover={{\n              scale: 1.05,\n              boxShadow: \"0 10px 25px rgba(255, 255, 255, 0.1)\"\n            }}\n            whileTap={{ scale: 0.95 }}\n            transition={{ duration: 0.2 }}\n            className=\"w-full sm:w-auto\"\n          >\n            <Button\n              size=\"lg\"\n              variant=\"outline\"\n              className=\"w-full sm:w-auto border-white text-white hover:bg-white hover:text-via-primary px-6 sm:px-8 py-3 border-2 transition-all duration-300\"\n              onClick={onSecondaryClick}\n            >\n              <Phone className=\"mr-2 h-4 sm:h-5 w-4 sm:w-5\" />\n              {secondaryButtonText}\n            </Button>\n          </motion.div>\n        </motion.div>\n      </div>\n    </motion.section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAEA;AACA;AAPA;;;;;;;AASO,SAAS,mBAAmB,KAQjB;QARiB,EACjC,KAAK,EACL,QAAQ,EACR,iBAAiB,EACjB,mBAAmB,EACnB,cAAc,EACd,gBAAgB,EAChB,SAAS,EACO,GARiB;IASjC,qBACE,8UAAC,+TAAM,CAAC,OAAO;QACb,WAAW,IAAA,uIAAE,EAAC,8EAA8E;QAC5F,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;QACzC,UAAU,0JAAgB;;0BAG1B,8UAAC,+TAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,aAAa;oBAAE,OAAO;oBAAG,SAAS;gBAAK;gBACvC,YAAY;oBAAE,UAAU;oBAAG,MAAM;gBAAU;gBAC3C,UAAU;oBAAE,MAAM;gBAAK;;kCAEvB,8UAAC;wBAAI,WAAU;;;;;;kCACf,8UAAC;wBAAI,WAAU;;;;;;kCACf,8UAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8UAAC;gBAAI,WAAU;;kCACb,8UAAC,+TAAM,CAAC,EAAE;wBACR,WAAU;wBACV,UAAU,kJAAQ;kCAEjB;;;;;;kCAGH,8UAAC,+TAAM,CAAC,CAAC;wBACP,WAAU;wBACV,UAAU,kJAAQ;kCAEjB;;;;;;kCAGH,8UAAC,+TAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU,qJAAW;;0CAErB,8UAAC,+TAAM,CAAC,GAAG;gCACT,YAAY;oCACV,OAAO;oCACP,WAAW;gCACb;gCACA,UAAU;oCAAE,OAAO;gCAAK;gCACxB,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,8UAAC,0JAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS;;sDAET,8UAAC,+TAAQ;4CAAC,WAAU;;;;;;wCACnB;;;;;;;;;;;;0CAIL,8UAAC,+TAAM,CAAC,GAAG;gCACT,YAAY;oCACV,OAAO;oCACP,WAAW;gCACb;gCACA,UAAU;oCAAE,OAAO;gCAAK;gCACxB,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;0CAEV,cAAA,8UAAC,0JAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;;sDAET,8UAAC,sTAAK;4CAAC,WAAU;;;;;;wCAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;KA3FgB", "debugId": null}}, {"offset": {"line": 1533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from \"@supabase/ssr\";\n\nexport function createClient() {\n  return createBrowserClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_OR_ANON_KEY!,\n  );\n}\n"], "names": [], "mappings": ";;;;AAII;AAJJ;AAAA;;AAEO,SAAS;IACd,OAAO,IAAA,mUAAmB;AAI5B", "debugId": null}}, {"offset": {"line": 1551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/lib/contact-form.ts"], "sourcesContent": ["import { createClient } from \"@/lib/supabase/client\";\nimport { ContactFormData, ContactFormSubmissionResponse } from \"@/types/connect\";\n\n// Validate email format\nexport const isValidEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n// Validate phone number format (basic validation)\nexport const isValidPhone = (phone: string): boolean => {\n  if (!phone) return true; // Phone is optional\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n  return phoneRegex.test(phone.replace(/[\\s\\-\\(\\)]/g, ''));\n};\n\n// Submit contact form to Supabase\nexport const submitContactForm = async (\n  formData: ContactFormData\n): Promise<ContactFormSubmissionResponse> => {\n  try {\n    const supabase = createClient();\n\n    // Insert form data into Supabase\n    const { data, error } = await supabase\n      .from('contact_submissions')\n      .insert([\n        {\n          first_name: formData.firstName,\n          last_name: formData.lastName,\n          email: formData.email,\n          phone: formData.phone || null,\n          company: formData.company || null,\n          message: formData.message,\n          submitted_at: new Date().toISOString(),\n          status: 'new'\n        }\n      ])\n      .select();\n\n    if (error) {\n      console.error('Supabase error:', error);\n      return {\n        success: false,\n        message: 'Failed to submit form. Please try again.',\n        errors: { general: error.message }\n      };\n    }\n\n    // Send notification email (you would implement this with your email service)\n    try {\n      await sendNotificationEmail(formData);\n    } catch (emailError) {\n      console.error('Email notification failed:', emailError);\n      // Don't fail the form submission if email fails\n    }\n\n    return {\n      success: true,\n      message: 'Thank you for your message! We\\'ll get back to you soon.',\n      data: data?.[0]\n    };\n\n  } catch (error) {\n    console.error('Form submission error:', error);\n    return {\n      success: false,\n      message: 'An unexpected error occurred. Please try again.',\n      errors: { general: 'Network error' }\n    };\n  }\n};\n\n// Send notification email (placeholder - implement with your email service)\nconst sendNotificationEmail = async (formData: ContactFormData): Promise<void> => {\n  // This would integrate with your email service (SendGrid, Resend, etc.)\n  // For now, we'll just log it\n  console.log('Sending notification email for:', formData.email);\n  \n  // Example implementation with a hypothetical email service:\n  /*\n  const emailData = {\n    to: '<EMAIL>',\n    subject: `New Contact Form Submission from ${formData.firstName} ${formData.lastName}`,\n    html: `\n      <h2>New Contact Form Submission</h2>\n      <p><strong>Name:</strong> ${formData.firstName} ${formData.lastName}</p>\n      <p><strong>Email:</strong> ${formData.email}</p>\n      ${formData.phone ? `<p><strong>Phone:</strong> ${formData.phone}</p>` : ''}\n      ${formData.company ? `<p><strong>Company:</strong> ${formData.company}</p>` : ''}\n      <p><strong>Message:</strong></p>\n      <p>${formData.message}</p>\n    `\n  };\n  \n  await emailService.send(emailData);\n  */\n};\n\n// Send auto-reply email to the user\nexport const sendAutoReply = async (email: string, name: string): Promise<void> => {\n  // This would send an auto-reply to the user\n  console.log(`Sending auto-reply to ${email}`);\n  \n  // Example implementation:\n  /*\n  const autoReplyData = {\n    to: email,\n    subject: 'Thank you for contacting Via Executive Suites',\n    html: `\n      <h2>Thank you for your inquiry, ${name}!</h2>\n      <p>We've received your message and will get back to you within 24 hours.</p>\n      <p>In the meantime, feel free to:</p>\n      <ul>\n        <li>Browse our <a href=\"https://viaexecutivesuites.com/services\">services</a></li>\n        <li>Check out our <a href=\"https://viaexecutivesuites.com/locations\">locations</a></li>\n        <li>Call us directly at (*************</li>\n      </ul>\n      <p>Best regards,<br>The Via Executive Suites Team</p>\n    `\n  };\n  \n  await emailService.send(autoReplyData);\n  */\n};\n\n// Create contact_submissions table schema (for reference)\nexport const createContactSubmissionsTable = `\n  CREATE TABLE IF NOT EXISTS contact_submissions (\n    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n    first_name VARCHAR(100) NOT NULL,\n    last_name VARCHAR(100) NOT NULL,\n    email VARCHAR(255) NOT NULL,\n    phone VARCHAR(20),\n    company VARCHAR(255),\n    message TEXT NOT NULL,\n    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    status VARCHAR(20) DEFAULT 'new',\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n  );\n\n  -- Create index for faster queries\n  CREATE INDEX IF NOT EXISTS idx_contact_submissions_email ON contact_submissions(email);\n  CREATE INDEX IF NOT EXISTS idx_contact_submissions_status ON contact_submissions(status);\n  CREATE INDEX IF NOT EXISTS idx_contact_submissions_submitted_at ON contact_submissions(submitted_at);\n\n  -- Enable RLS (Row Level Security)\n  ALTER TABLE contact_submissions ENABLE ROW LEVEL SECURITY;\n\n  -- Create policy for authenticated users to read their own submissions\n  CREATE POLICY \"Users can view their own submissions\" ON contact_submissions\n    FOR SELECT USING (auth.uid() IS NOT NULL);\n\n  -- Create policy for inserting new submissions (allow anonymous)\n  CREATE POLICY \"Anyone can insert contact submissions\" ON contact_submissions\n    FOR INSERT WITH CHECK (true);\n`;\n\n// Utility function to format phone number for display\nexport const formatPhoneNumber = (phone: string): string => {\n  if (!phone) return '';\n  \n  // Remove all non-digits\n  const digits = phone.replace(/\\D/g, '');\n  \n  // Format as (XXX) XXX-XXXX for US numbers\n  if (digits.length === 10) {\n    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;\n  }\n  \n  // Return original if not a standard US number\n  return phone;\n};\n\n// Utility function to sanitize input\nexport const sanitizeInput = (input: string): string => {\n  return input.trim().replace(/[<>]/g, '');\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AAIO,MAAM,eAAe,CAAC;IAC3B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,OAAO,OAAO,MAAM,oBAAoB;IAC7C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,eAAe;AACtD;AAGO,MAAM,oBAAoB,OAC/B;IAEA,IAAI;QACF,MAAM,WAAW,IAAA,8JAAY;QAE7B,iCAAiC;QACjC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,uBACL,MAAM,CAAC;YACN;gBACE,YAAY,SAAS,SAAS;gBAC9B,WAAW,SAAS,QAAQ;gBAC5B,OAAO,SAAS,KAAK;gBACrB,OAAO,SAAS,KAAK,IAAI;gBACzB,SAAS,SAAS,OAAO,IAAI;gBAC7B,SAAS,SAAS,OAAO;gBACzB,cAAc,IAAI,OAAO,WAAW;gBACpC,QAAQ;YACV;SACD,EACA,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,mBAAmB;YACjC,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,QAAQ;oBAAE,SAAS,MAAM,OAAO;gBAAC;YACnC;QACF;QAEA,6EAA6E;QAC7E,IAAI;YACF,MAAM,sBAAsB;QAC9B,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,gDAAgD;QAClD;QAEA,OAAO;YACL,SAAS;YACT,SAAS;YACT,IAAI,EAAE,iBAAA,2BAAA,IAAM,CAAC,EAAE;QACjB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YACL,SAAS;YACT,SAAS;YACT,QAAQ;gBAAE,SAAS;YAAgB;QACrC;IACF;AACF;AAEA,4EAA4E;AAC5E,MAAM,wBAAwB,OAAO;IACnC,wEAAwE;IACxE,6BAA6B;IAC7B,QAAQ,GAAG,CAAC,mCAAmC,SAAS,KAAK;AAE7D,4DAA4D;AAC5D;;;;;;;;;;;;;;;;EAgBA,GACF;AAGO,MAAM,gBAAgB,OAAO,OAAe;IACjD,4CAA4C;IAC5C,QAAQ,GAAG,CAAC,AAAC,yBAA8B,OAAN;AAErC,0BAA0B;AAC1B;;;;;;;;;;;;;;;;;;EAkBA,GACF;AAGO,MAAM,gCAAiC;AAiCvC,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,OAAO,OAAO;IAEnB,wBAAwB;IACxB,MAAM,SAAS,MAAM,OAAO,CAAC,OAAO;IAEpC,0CAA0C;IAC1C,IAAI,OAAO,MAAM,KAAK,IAAI;QACxB,OAAO,AAAC,IAA0B,OAAvB,OAAO,KAAK,CAAC,GAAG,IAAG,MAA0B,OAAtB,OAAO,KAAK,CAAC,GAAG,IAAG,KAAmB,OAAhB,OAAO,KAAK,CAAC;IACvE;IAEA,8CAA8C;IAC9C,OAAO;AACT;AAGO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS;AACvC", "debugId": null}}, {"offset": {"line": 1695, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/connect/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Phone, Mail, MapPin, Clock } from \"lucide-react\";\r\nimport { AnimatedHeroSection } from \"@/components/connect/animated-hero-section\";\r\nimport { AnimatedContactInfo } from \"@/components/connect/animated-contact-info\";\r\nimport { EnhancedContactForm } from \"@/components/connect/enhanced-contact-form\";\r\nimport { AnimatedLocationsSection } from \"@/components/connect/animated-locations-section\";\r\nimport { AnimatedCTASection } from \"@/components/connect/animated-cta-section\";\r\nimport { ContactInfo, LocationInfo, ContactFormData } from \"@/types/connect\";\r\nimport { submitContactForm, sendAutoReply } from \"@/lib/contact-form\";\r\n\r\n// Contact information data\r\nconst contactInfo: ContactInfo[] = [\r\n  {\r\n    title: \"Phone\",\r\n    value: \"(*************\",\r\n    description: \"Call us during business hours\",\r\n    icon: Phone,\r\n    href: \"tel:+***********\",\r\n  },\r\n  {\r\n    title: \"Email\",\r\n    value: \"<EMAIL>\",\r\n    description: \"Send us a message anytime\",\r\n    icon: Mail,\r\n    href: \"mailto:<EMAIL>\",\r\n  },\r\n  {\r\n    title: \"Main Office\",\r\n    value: \"813 N. Main St., McAllen, TX 78501\",\r\n    description: \"Visit our flagship location\",\r\n    icon: MapPin,\r\n    href: \"https://maps.google.com/?q=813+N.+Main+St.,+McAllen,+TX+78501\",\r\n  },\r\n  {\r\n    title: \"Business Hours\",\r\n    value: \"Monday - Friday: 8:00 AM - 6:00 PM\",\r\n    description: \"Saturday: 9:00 AM - 2:00 PM\",\r\n    icon: Clock,\r\n  },\r\n];\r\n\r\n// Locations data\r\nconst locations: LocationInfo[] = [\r\n  {\r\n    name: \"ADBC Location\",\r\n    address: \"813 N. Main St., McAllen, Texas 78501\",\r\n    phone: \"(*************\",\r\n    hours: \"Mon-Fri: 8AM-6PM, Sat: 9AM-2PM\",\r\n    mapUrl: \"https://maps.google.com/?q=813+N.+Main+St.,+McAllen,+Texas+78501\",\r\n  },\r\n  {\r\n    name: \"La Costa Location\",\r\n    address: \"214 N 16th St, McAllen, Texas 78501\",\r\n    phone: \"(*************\",\r\n    hours: \"Mon-Fri: 8AM-6PM, Sat: 9AM-2PM\",\r\n    mapUrl: \"https://maps.google.com/?q=214+N+16th+St,+McAllen,+Texas+78501\",\r\n  },\r\n  {\r\n    name: \"23rd Street Location\",\r\n    address: \"1821 N 23rd Street, McAllen, Texas 78501\",\r\n    phone: \"(*************\",\r\n    hours: \"Mon-Fri: 8AM-6PM, Sat: 9AM-2PM\",\r\n    mapUrl: \"https://maps.google.com/?q=1821+N+23rd+Street,+McAllen,+Texas+78501\",\r\n  },\r\n  {\r\n    name: \"Edinburg Location\",\r\n    address: \"1409 S 9th Ave, Edinburg, Texas 78539\",\r\n    phone: \"(*************\",\r\n    hours: \"Mon-Fri: 8AM-6PM, Sat: 9AM-2PM\",\r\n    mapUrl: \"https://maps.google.com/?q=1409+S+9th+Ave,+Edinburg,+Texas+78539\",\r\n  },\r\n];\r\n\r\nexport default function ConnectPage() {\r\n  // Handle button clicks\r\n  const handleScheduleTour = () => {\r\n    // Open scheduling modal or redirect to scheduling page\r\n    window.open('tel:+***********', '_self');\r\n  };\r\n\r\n  const handleContactUs = () => {\r\n    // Scroll to contact form\r\n    const formElement = document.querySelector('#contact-form');\r\n    if (formElement) {\r\n      formElement.scrollIntoView({ behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  const handleFormSubmit = async (formData: ContactFormData) => {\r\n    try {\r\n      // Submit form to Supabase\r\n      const response = await submitContactForm(formData);\r\n\r\n      if (response.success) {\r\n        // Send auto-reply email\r\n        try {\r\n          await sendAutoReply(formData.email, `${formData.firstName} ${formData.lastName}`);\r\n        } catch (emailError) {\r\n          console.error('Auto-reply failed:', emailError);\r\n          // Don't fail the form submission if auto-reply fails\r\n        }\r\n      }\r\n\r\n      // The form component will handle the response display\r\n      if (!response.success) {\r\n        throw new Error(response.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('Form submission error:', error);\r\n      throw error; // Re-throw to let the form component handle it\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen\">\r\n      {/* Enhanced Hero Section */}\r\n      <AnimatedHeroSection\r\n        title=\"Let's Connect\"\r\n        subtitle=\"Ready to find your perfect workspace? Get in touch with us today.\"\r\n        primaryButtonText=\"Schedule a Tour\"\r\n        secondaryButtonText=\"Contact Us\"\r\n        onPrimaryClick={handleScheduleTour}\r\n        onSecondaryClick={handleContactUs}\r\n      />\r\n\r\n      {/* Enhanced Contact Information */}\r\n      <AnimatedContactInfo\r\n        title=\"Get in Touch\"\r\n        subtitle=\"We're here to help you find the perfect workspace solution. Reach out to us through any of these channels.\"\r\n        contactInfo={contactInfo}\r\n      />\r\n\r\n      {/* Enhanced Contact Form */}\r\n      <div id=\"contact-form\">\r\n        <EnhancedContactForm onSubmit={handleFormSubmit} />\r\n      </div>\r\n\r\n      {/* Enhanced Locations Section */}\r\n      <AnimatedLocationsSection\r\n        title=\"Our Locations\"\r\n        subtitle=\"Visit any of our convenient locations throughout the Rio Grande Valley.\"\r\n        locations={locations}\r\n      />\r\n\r\n      {/* Enhanced CTA Section */}\r\n      <AnimatedCTASection\r\n        title=\"Ready to Get Started?\"\r\n        subtitle=\"Schedule a tour today and see how Via Executive Suites can help your business thrive.\"\r\n        primaryButtonText=\"Schedule a Tour\"\r\n        secondaryButtonText=\"Call Now\"\r\n        onPrimaryClick={handleScheduleTour}\r\n        onSecondaryClick={handleContactUs}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AATA;;;;;;;;;AAWA,2BAA2B;AAC3B,MAAM,cAA6B;IACjC;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,sTAAK;QACX,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,mTAAI;QACV,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,6TAAM;QACZ,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,sTAAK;IACb;CACD;AAED,iBAAiB;AACjB,MAAM,YAA4B;IAChC;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,uBAAuB;IACvB,MAAM,qBAAqB;QACzB,uDAAuD;QACvD,OAAO,IAAI,CAAC,oBAAoB;IAClC;IAEA,MAAM,kBAAkB;QACtB,yBAAyB;QACzB,MAAM,cAAc,SAAS,aAAa,CAAC;QAC3C,IAAI,aAAa;YACf,YAAY,cAAc,CAAC;gBAAE,UAAU;YAAS;QAClD;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,0BAA0B;YAC1B,MAAM,WAAW,MAAM,IAAA,gKAAiB,EAAC;YAEzC,IAAI,SAAS,OAAO,EAAE;gBACpB,wBAAwB;gBACxB,IAAI;oBACF,MAAM,IAAA,4JAAa,EAAC,SAAS,KAAK,EAAE,AAAC,GAAwB,OAAtB,SAAS,SAAS,EAAC,KAAqB,OAAlB,SAAS,QAAQ;gBAChF,EAAE,OAAO,YAAY;oBACnB,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,qDAAqD;gBACvD;YACF;YAEA,sDAAsD;YACtD,IAAI,CAAC,SAAS,OAAO,EAAE;gBACrB,MAAM,IAAI,MAAM,SAAS,OAAO;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,OAAO,+CAA+C;QAC9D;IACF;IAEA,qBACE,8UAAC;QAAI,WAAU;;0BAEb,8UAAC,iMAAmB;gBAClB,OAAM;gBACN,UAAS;gBACT,mBAAkB;gBAClB,qBAAoB;gBACpB,gBAAgB;gBAChB,kBAAkB;;;;;;0BAIpB,8UAAC,iMAAmB;gBAClB,OAAM;gBACN,UAAS;gBACT,aAAa;;;;;;0BAIf,8UAAC;gBAAI,IAAG;0BACN,cAAA,8UAAC,iMAAmB;oBAAC,UAAU;;;;;;;;;;;0BAIjC,8UAAC,2MAAwB;gBACvB,OAAM;gBACN,UAAS;gBACT,WAAW;;;;;;0BAIb,8UAAC,+LAAkB;gBACjB,OAAM;gBACN,UAAS;gBACT,mBAAkB;gBAClB,qBAAoB;gBACpB,gBAAgB;gBAChB,kBAAkB;;;;;;;;;;;;AAI1B;KAlFwB", "debugId": null}}]}