{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/connect/layout.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\n\nexport const metadata: Metadata = {\n  title: \"Connect - Via Executive Suites\",\n  description: \"Get in touch with Via Executive Suites. Schedule a tour, ask questions, or learn more about our professional workspace solutions.\",\n  keywords: \"contact, schedule tour, office space, executive suites, Rio Grande Valley\",\n  authors: [{ name: \"Via Executive Suites\" }],\n  openGraph: {\n    title: \"Connect - Via Executive Suites\",\n    description: \"Get in touch with Via Executive Suites. Schedule a tour, ask questions, or learn more about our professional workspace solutions.\",\n    type: \"website\",\n    locale: \"en_US\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"Connect - Via Executive Suites\",\n    description: \"Get in touch with Via Executive Suites. Schedule a tour, ask questions, or learn more about our professional workspace solutions.\",\n  },\n};\n\nexport default function ConnectLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return children;\n}\n"], "names": [], "mappings": ";;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAuB;KAAE;IAC3C,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;AACF;AAEe,SAAS,cAAc,EACpC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}