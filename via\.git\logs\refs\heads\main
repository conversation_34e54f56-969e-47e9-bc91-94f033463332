0000000000000000000000000000000000000000 c9acbc8c255d8a1a187727178c32ff849b28936e Capital Code <<EMAIL>> 1755765526 -0500	commit (initial): first commit
c9acbc8c255d8a1a187727178c32ff849b28936e c9acbc8c255d8a1a187727178c32ff849b28936e Capital Code <<EMAIL>> 1755765527 -0500	Branch: renamed refs/heads/master to refs/heads/main
c9acbc8c255d8a1a187727178c32ff849b28936e 40f88bdf7c8a97385aac01ece7ad7a9c8daf0575 Capital Code <<EMAIL>> 1755765704 -0500	commit: Initial commit: Add complete Next.js project with Supabase integration
40f88bdf7c8a97385aac01ece7ad7a9c8daf0575 ff8a058e1fd036faf8cbbfc619444f65df4f4ccc Capital Code <<EMAIL>> 1755765870 -0500	commit: promts
ff8a058e1fd036faf8cbbfc619444f65df4f4ccc 23c5a49da11fba4b50f0335db7a213e284778636 Capital Code <<EMAIL>> 1755766017 -0500	commit: Update .gitignore to exclude .cursor/mcp.json file
23c5a49da11fba4b50f0335db7a213e284778636 c48145b34d5d7586da59a865b3ae258fd16522f5 Capital Code <<EMAIL>> 1755767214 -0500	commit: Enhance error handling in SignUpForm and update password requirements message
c48145b34d5d7586da59a865b3ae258fd16522f5 16070cbd7649787822a1264c74f21b8801f08173 Capital Code <<EMAIL>> 1755769096 -0500	commit: Add comprehensive project documentation and context files for Via Workspace Platform
16070cbd7649787822a1264c74f21b8801f08173 3ae39fae7ac45394e08dbfdabee3aae42e89837b Capital Code <<EMAIL>> 1755771598 -0500	commit: Implement Via branding across the application, including a new navigation bar, updated metadata, and integration of brand colors. Add multiple sections to the homepage, including About, Services, Locations, and Testimonials, along with a contact form and gallery. Enhance the global styles and configuration for improved image handling.
3ae39fae7ac45394e08dbfdabee3aae42e89837b 0461237a296bd2c1fb84ce881a18486bd9ba35a7 Capital Code <<EMAIL>> 1755771867 -0500	commit: Refactor TextareaProps type definition in textarea component for improved clarity and consistency. Update active context documentation to reflect ESLint build issue resolution.
0461237a296bd2c1fb84ce881a18486bd9ba35a7 23768b0492654071c45a7ea77ee079a12062350f Capital Code <<EMAIL>> 1755773206 -0500	commit: Implement About page components including hero section, company story, mission and vision, all-inclusive services, testimonials, and a contact form. Update Next.js configuration for secure asset handling from viatosuccess.com.
23768b0492654071c45a7ea77ee079a12062350f 0cafab42c4f5b68e8c5631af81975d4aa85646af Capital Code <<EMAIL>> 1755774643 -0500	commit: framer motion
0cafab42c4f5b68e8c5631af81975d4aa85646af 35e5591196196314594f56d0c840ced2d649fb79 Capital Code <<EMAIL>> 1755776556 -0500	commit: Implement location pages and enhance layout with overflow handling. Add new components for location details, loading states, and not found pages. Update global styles to prevent overflow issues during animations. Integrate Framer Motion for animated transitions in location sections.
35e5591196196314594f56d0c840ced2d649fb79 3e06747ae28d940a33b8eba071184d72ac1311b2 Capital Code <<EMAIL>> 1755776879 -0500	commit: Refactor location data structure in layout component to enforce type safety with specific properties for name, fullName, and address.
3e06747ae28d940a33b8eba071184d72ac1311b2 955ff77de77e7023ebb8ae818de04206e5c89254 Capital Code <<EMAIL>> 1755777002 -0500	commit: Update not-found page for consistent apostrophe usage, simplify locations mapping in LocationsSection, and enhance FetchDataSteps component with loading and error handling for fetching notes from Supabase.
955ff77de77e7023ebb8ae818de04206e5c89254 2966cfff8451d88a4e029198315a42544c5bbde3 Capital Code <<EMAIL>> 1755778172 -0500	commit: Add blog and connect pages, implement new location for Lindberg, and enhance services with dedicated pages for executive suites, beauty suites, and virtual offices. Update services section component for improved presentation and animations.
