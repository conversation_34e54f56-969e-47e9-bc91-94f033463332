"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { MapPin, Phone, Clock } from "lucide-react";
import {
  fadeInUp,
  fadeInDown,
  scaleIn,
  staggerContainer,
  staggerItem,
  animationConfig
} from "@/lib/animations";
import { Location, LocationSlugs } from "@/lib/types/location";

const locations: Record<LocationSlugs, Location> = {
  "adbc": {
    name: "ADBC",
    fullName: "Via Executive Suites ADBC",
    address: "813 N. Main St., McAllen, Texas 78501",
    phone: "(*************",
    hours: "Mon-Fri: 8:00 AM - 5:00 PM",
    email: "<EMAIL>",
    coordinates: { lat: 26.212138647510635, lng: -98.23348265965645 },
    description: "Located in the heart of McAllen, our ADBC location offers premium office spaces with easy access to major highways and business districts.",
    features: [
      "Private office suites",
      "Conference rooms",
      "Reception services",
      "High-speed internet",
      "Free parking",
      "Break room amenities",
      "Mail handling",
      "24/7 building access"
    ],
    image: "https://via2success.com/wp-content/uploads/2021/01/location-adbc.jpg"
  },
  "la-costa": {
    name: "La Costa",
    fullName: "Via Executive Suites La Costa",
    address: "214 N 16th St, McAllen, Texas 78501",
    phone: "(*************",
    hours: "Mon-Fri: 8:00 AM - 5:00 PM",
    email: "<EMAIL>",
    coordinates: { lat: 26.20654823351708, lng: -98.2359421731485 },
    description: "Our La Costa location provides modern office solutions in a vibrant business community, perfect for growing companies and entrepreneurs.",
    features: [
      "Flexible office spaces",
      "Virtual office services",
      "Meeting facilities",
      "Business support services",
      "Secure access",
      "Professional environment",
      "Networking opportunities",
      "Convenient location"
    ],
    image: "https://via2success.com/wp-content/uploads/2021/01/location-la-costa.jpg"
  },
  "23rd": {
    name: "23rd",
    fullName: "Via Executive Suites 23rd",
    address: "1821 N 23rd Street, McAllen, Texas 78501",
    phone: "(*************",
    hours: "Mon-Fri: 8:00 AM - 5:00 PM",
    email: "<EMAIL>",
    coordinates: { lat: 26.22271557411775, lng: -98.24298783082033 },
    description: "The 23rd Street location offers spacious office environments with premium amenities, ideal for established businesses seeking professional workspace.",
    features: [
      "Executive office suites",
      "Boardroom facilities",
      "Premium amenities",
      "Dedicated support staff",
      "Advanced technology",
      "Professional services",
      "Business networking",
      "Premium location"
    ],
    image: "https://via2success.com/wp-content/uploads/2021/01/location-23rd.jpg"
  },
  "edinburg": {
    name: "Edinburg",
    fullName: "Via Executive Suites Edinburg",
    address: "1409 S 9th Ave, Edinburg, Texas 78539",
    phone: "(*************",
    hours: "Mon-Fri: 8:00 AM - 5:00 PM",
    email: "<EMAIL>",
    coordinates: { lat: 26.28915674741617, lng: -98.16747817499945 },
    description: "Our Edinburg location serves the northern Rio Grande Valley with professional office solutions and comprehensive business support services.",
    features: [
      "Modern office spaces",
      "Collaborative areas",
      "Technology infrastructure",
      "Business services",
      "Community events",
      "Professional development",
      "Local partnerships",
      "Strategic positioning"
    ],
    image: "https://via2success.com/wp-content/uploads/2021/01/location-edinburg.jpg"
  }
};

export function LocationsSection() {
  return (
    <section className="py-16 bg-white overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Animated Header */}
        <motion.div
          className="text-center mb-12"
          initial="hidden"
          whileInView="visible"
          viewport={animationConfig.viewport}
          variants={staggerContainer}
        >
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-gray-900 mb-4"
            variants={fadeInDown}
          >
            Locations
          </motion.h2>
          <motion.p
            className="text-lg text-gray-600 max-w-3xl mx-auto"
            variants={fadeInUp}
          >
            Our 4 locations are situated throughout the RGV for maximum convenience.
          </motion.p>
        </motion.div>

        {/* Animated Map Placeholder */}
        <motion.div
          className="mb-12"
          initial="hidden"
          whileInView="visible"
          viewport={animationConfig.viewport}
          variants={scaleIn}
        >
          <motion.div
            className="bg-gray-200 rounded-lg h-96 flex items-center justify-center overflow-hidden"
            whileHover={{
              scale: 1.02,
              transition: { duration: 0.3, ease: "easeInOut" }
            }}
          >
            <motion.div
              className="text-center text-gray-500"
              variants={fadeInUp}
            >
              <motion.div
                whileHover={{
                  scale: 1.1,
                  rotate: 5,
                  transition: { duration: 0.2 }
                }}
              >
                <MapPin className="w-12 h-12 mx-auto mb-4" />
              </motion.div>
              <p className="text-lg font-medium">Interactive Map</p>
              <p className="text-sm">Map integration would be implemented here</p>
            </motion.div>
          </motion.div>
        </motion.div>

        {/* Animated Location Cards */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          initial="hidden"
          whileInView="visible"
          viewport={animationConfig.viewport}
          variants={staggerContainer}
        >
          {Object.entries(locations).map(([slug, location]) => (
            <motion.div
              key={slug}
              className="bg-white border border-gray-200 rounded-lg shadow-md p-6 cursor-pointer"
              variants={staggerItem}
              whileHover={{
                y: -8,
                scale: 1.02,
                boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                transition: {
                  duration: 0.3,
                  ease: "easeInOut"
                }
              }}
              whileTap={{ scale: 0.98 }}
            >
              <motion.h3
                className="text-xl font-semibold text-via-primary mb-4"
                whileHover={{
                  scale: 1.05,
                  transition: { duration: 0.2 }
                }}
              >
                {location.name}
              </motion.h3>

              <div className="space-y-3 text-sm text-gray-600">
                <motion.div
                  className="flex items-start"
                  whileHover={{ x: 4, transition: { duration: 0.2 } }}
                >
                  <MapPin className="w-4 h-4 mr-2 mt-0.5 text-via-primary flex-shrink-0" />
                  <div>
                    <div className="font-medium text-gray-900">{location.fullName}</div>
                    <div>{location.address}</div>
                  </div>
                </motion.div>

                <motion.div
                  className="flex items-center"
                  whileHover={{ x: 4, transition: { duration: 0.2 } }}
                >
                  <Phone className="w-4 h-4 mr-2 text-via-primary flex-shrink-0" />
                  <div>{location.phone}</div>
                </motion.div>

                <motion.div
                  className="flex items-center"
                  whileHover={{ x: 4, transition: { duration: 0.2 } }}
                >
                  <Clock className="w-4 h-4 mr-2 text-via-primary flex-shrink-0" />
                  <div>{location.hours}</div>
                </motion.div>
              </div>
              
              <div className="mt-6 space-y-2">
                <Link href={`/locations/${slug}`}>
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full border-via-primary text-via-primary hover:bg-via-primary hover:text-white transition-all duration-300"
                    >
                      View Details
                    </Button>
                  </motion.div>
                </Link>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full text-gray-600 hover:text-via-primary transition-all duration-300"
                    onClick={() => {
                      // Open Google Maps directions
                      const encodedAddress = encodeURIComponent(location.address);
                      window.open(`https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`, '_blank');
                    }}
                  >
                    Get Directions
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Animated Bottom CTA */}
        <motion.div
          className="text-center mt-12"
          initial="hidden"
          whileInView="visible"
          viewport={animationConfig.viewport}
          variants={fadeInUp}
        >
          <Link href="/locations">
            <motion.div
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.2, ease: "easeInOut" }
              }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                size="lg"
                className="bg-via-primary hover:bg-via-primary-dark text-white px-8 py-3 text-lg font-semibold rounded-md transition-all duration-300"
              >
                View All Locations
              </Button>
            </motion.div>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
