{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/connect/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON>ada<PERSON> } from \"next\";\r\nimport { Phone, Mail, MapPin, Clock } from \"lucide-react\";\r\nimport { AnimatedHeroSection } from \"@/components/connect/animated-hero-section\";\r\nimport { AnimatedContactInfo } from \"@/components/connect/animated-contact-info\";\r\nimport { EnhancedContactForm } from \"@/components/connect/enhanced-contact-form\";\r\nimport { AnimatedLocationsSection } from \"@/components/connect/animated-locations-section\";\r\nimport { AnimatedCTASection } from \"@/components/connect/animated-cta-section\";\r\nimport { ContactInfo, LocationInfo } from \"@/types/connect\";\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Connect - Via Executive Suites\",\r\n  description: \"Get in touch with Via Executive Suites. Schedule a tour, ask questions, or learn more about our professional workspace solutions.\",\r\n  keywords: \"contact, schedule tour, office space, executive suites, Rio Grande Valley\",\r\n  openGraph: {\r\n    title: \"Connect - Via Executive Suites\",\r\n    description: \"Get in touch with Via Executive Suites. Schedule a tour, ask questions, or learn more about our professional workspace solutions.\",\r\n    type: \"website\",\r\n    locale: \"en_US\",\r\n  },\r\n};\r\n\r\n// Contact information data\r\nconst contactInfo: ContactInfo[] = [\r\n  {\r\n    title: \"Phone\",\r\n    value: \"(*************\",\r\n    description: \"Call us during business hours\",\r\n    icon: Phone,\r\n    href: \"tel:+***********\",\r\n  },\r\n  {\r\n    title: \"Email\",\r\n    value: \"<EMAIL>\",\r\n    description: \"Send us a message anytime\",\r\n    icon: Mail,\r\n    href: \"mailto:<EMAIL>\",\r\n  },\r\n  {\r\n    title: \"Main Office\",\r\n    value: \"813 N. Main St., McAllen, TX 78501\",\r\n    description: \"Visit our flagship location\",\r\n    icon: MapPin,\r\n    href: \"https://maps.google.com/?q=813+N.+Main+St.,+McAllen,+TX+78501\",\r\n  },\r\n  {\r\n    title: \"Business Hours\",\r\n    value: \"Monday - Friday: 8:00 AM - 6:00 PM\",\r\n    description: \"Saturday: 9:00 AM - 2:00 PM\",\r\n    icon: Clock,\r\n  },\r\n];\r\n\r\n// Locations data\r\nconst locations: LocationInfo[] = [\r\n  {\r\n    name: \"ADBC Location\",\r\n    address: \"813 N. Main St., McAllen, Texas 78501\",\r\n    phone: \"(*************\",\r\n    hours: \"Mon-Fri: 8AM-6PM, Sat: 9AM-2PM\",\r\n    mapUrl: \"https://maps.google.com/?q=813+N.+Main+St.,+McAllen,+Texas+78501\",\r\n  },\r\n  {\r\n    name: \"La Costa Location\",\r\n    address: \"214 N 16th St, McAllen, Texas 78501\",\r\n    phone: \"(*************\",\r\n    hours: \"Mon-Fri: 8AM-6PM, Sat: 9AM-2PM\",\r\n    mapUrl: \"https://maps.google.com/?q=214+N+16th+St,+McAllen,+Texas+78501\",\r\n  },\r\n  {\r\n    name: \"23rd Street Location\",\r\n    address: \"1821 N 23rd Street, McAllen, Texas 78501\",\r\n    phone: \"(*************\",\r\n    hours: \"Mon-Fri: 8AM-6PM, Sat: 9AM-2PM\",\r\n    mapUrl: \"https://maps.google.com/?q=1821+N+23rd+Street,+McAllen,+Texas+78501\",\r\n  },\r\n  {\r\n    name: \"Edinburg Location\",\r\n    address: \"1409 S 9th Ave, Edinburg, Texas 78539\",\r\n    phone: \"(*************\",\r\n    hours: \"Mon-Fri: 8AM-6PM, Sat: 9AM-2PM\",\r\n    mapUrl: \"https://maps.google.com/?q=1409+S+9th+Ave,+Edinburg,+Texas+78539\",\r\n  },\r\n];\r\n\r\nexport default function ConnectPage() {\r\n  return (\r\n    <div className=\"min-h-screen\">\r\n      {/* Hero Section */}\r\n      <section className=\"relative bg-gradient-to-r from-via-primary via-via-primary-light to-via-primary text-white py-24 lg:py-32 overflow-hidden\">\r\n        <div className=\"absolute inset-0 bg-black/20\"></div>\r\n        <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\r\n          <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6\">\r\n            Let's Connect\r\n          </h1>\r\n          <p className=\"text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto\">\r\n            Ready to find your perfect workspace? Get in touch with us today.\r\n          </p>\r\n          <div className=\"mt-8\">\r\n            <Button size=\"lg\" className=\"bg-white text-via-primary hover:bg-gray-100 px-8 py-3 mr-4\">\r\n              Schedule a Tour\r\n            </Button>\r\n            <Button size=\"lg\" variant=\"outline\" className=\"border-white text-white hover:bg-white hover:text-via-primary px-8 py-3\">\r\n              Contact Us\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Information */}\r\n      <section className=\"py-16 bg-white overflow-hidden\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center mb-16\">\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n              Get in Touch\r\n            </h2>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n              We're here to help you find the perfect workspace solution. Reach out to us through any of these channels.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n            {contactInfo.map((info) => {\r\n              const IconComponent = info.icon;\r\n              return (\r\n                <div key={info.title} className=\"text-center\">\r\n                  <div className=\"w-16 h-16 bg-via-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\r\n                    <IconComponent className=\"h-8 w-8 text-via-primary\" />\r\n                  </div>\r\n                  <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{info.title}</h3>\r\n                  <p className=\"text-via-primary font-medium mb-2\">{info.value}</p>\r\n                  <p className=\"text-gray-600 text-sm\">{info.description}</p>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Contact Form */}\r\n      <section className=\"py-16 bg-gray-50 overflow-hidden\">\r\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center mb-12\">\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n              Send Us a Message\r\n            </h2>\r\n            <p className=\"text-xl text-gray-600\">\r\n              Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.\r\n            </p>\r\n          </div>\r\n\r\n          <form className=\"bg-white rounded-lg shadow-lg p-8\">\r\n            <div className=\"grid md:grid-cols-2 gap-6 mb-6\">\r\n              <div>\r\n                <label htmlFor=\"firstName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  First Name *\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"firstName\"\r\n                  name=\"firstName\"\r\n                  required\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent\"\r\n                />\r\n              </div>\r\n              <div>\r\n                <label htmlFor=\"lastName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Last Name *\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"lastName\"\r\n                  name=\"lastName\"\r\n                  required\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"grid md:grid-cols-2 gap-6 mb-6\">\r\n              <div>\r\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Email *\r\n                </label>\r\n                <input\r\n                  type=\"email\"\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  required\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent\"\r\n                />\r\n              </div>\r\n              <div>\r\n                <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                  Phone\r\n                </label>\r\n                <input\r\n                  type=\"tel\"\r\n                  id=\"phone\"\r\n                  name=\"phone\"\r\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mb-6\">\r\n              <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                Company\r\n              </label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"company\"\r\n                name=\"company\"\r\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent\"\r\n              />\r\n            </div>\r\n\r\n            <div className=\"mb-6\">\r\n              <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                Message *\r\n              </label>\r\n              <textarea\r\n                id=\"message\"\r\n                name=\"message\"\r\n                rows={5}\r\n                required\r\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-via-primary focus:border-transparent\"\r\n                placeholder=\"Tell us about your business needs and how we can help...\"\r\n              ></textarea>\r\n            </div>\r\n\r\n            <div className=\"text-center\">\r\n              <Button type=\"submit\" size=\"lg\" className=\"bg-via-primary hover:bg-via-primary-dark text-white px-8 py-3\">\r\n                <MessageCircle className=\"mr-2 h-5 w-5\" />\r\n                Send Message\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Locations */}\r\n      <section className=\"py-16 bg-white overflow-hidden\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"text-center mb-16\">\r\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\r\n              Our Locations\r\n            </h2>\r\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\r\n              Visit any of our convenient locations throughout the Rio Grande Valley.\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"grid md:grid-cols-2 gap-8\">\r\n            {locations.map((location) => (\r\n              <div key={location.name} className=\"bg-gray-50 rounded-lg p-6 border border-gray-200\">\r\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-3\">{location.name}</h3>\r\n                <div className=\"space-y-2\">\r\n                  <div className=\"flex items-start\">\r\n                    <MapPin className=\"h-5 w-5 text-via-primary mr-3 mt-0.5 flex-shrink-0\" />\r\n                    <p className=\"text-gray-600\">{location.address}</p>\r\n                  </div>\r\n                  <div className=\"flex items-center\">\r\n                    <Phone className=\"h-5 w-5 text-via-primary mr-3 flex-shrink-0\" />\r\n                    <p className=\"text-gray-600\">{location.phone}</p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"mt-4\">\r\n                  <Button variant=\"outline\" className=\"border-via-primary text-via-primary hover:bg-via-primary hover:text-white\">\r\n                    <Calendar className=\"mr-2 h-4 w-4\" />\r\n                    Schedule Tour\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-16 bg-via-primary text-white overflow-hidden\">\r\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\r\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-4\">\r\n            Ready to Get Started?\r\n          </h2>\r\n          <p className=\"text-xl text-via-primary-light mb-8 max-w-2xl mx-auto\">\r\n            Schedule a tour today and see how Via Executive Suites can help your business thrive.\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n            <Button size=\"lg\" className=\"bg-white text-via-primary hover:bg-gray-100 px-8 py-3\">\r\n              <Calendar className=\"mr-2 h-5 w-5\" />\r\n              Schedule a Tour\r\n            </Button>\r\n            <Button size=\"lg\" variant=\"outline\" className=\"border-white text-white hover:bg-white hover:text-via-primary px-8 py-3\">\r\n              <Phone className=\"mr-2 h-5 w-5\" />\r\n              Call Now\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAAA;AAAA;AAAA;AAHA;;;AAWO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,WAAW;QACT,OAAO;QACP,aAAa;QACb,MAAM;QACN,QAAQ;IACV;AACF;AAEA,2BAA2B;AAC3B,MAAM,cAA6B;IACjC;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,sTAAK;QACX,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,mTAAI;QACV,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,6TAAM;QACZ,MAAM;IACR;IACA;QACE,OAAO;QACP,OAAO;QACP,aAAa;QACb,MAAM,sTAAK;IACb;CACD;AAED,iBAAiB;AACjB,MAAM,YAA4B;IAChC;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,QAAQ;IACV;IACA;QACE,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,qBACE,8UAAC;QAAI,WAAU;;0BAEb,8UAAC;gBAAQ,WAAU;;kCACjB,8UAAC;wBAAI,WAAU;;;;;;kCACf,8UAAC;wBAAI,WAAU;;0CACb,8UAAC;gCAAG,WAAU;0CAAkD;;;;;;0CAGhE,8UAAC;gCAAE,WAAU;0CAAsD;;;;;;0CAGnE,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;wCAAO,MAAK;wCAAK,WAAU;kDAA6D;;;;;;kDAGzF,8UAAC;wCAAO,MAAK;wCAAK,SAAQ;wCAAU,WAAU;kDAA0E;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9H,8UAAC;gBAAQ,WAAU;0BACjB,cAAA,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8UAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8UAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC;gCAChB,MAAM,gBAAgB,KAAK,IAAI;gCAC/B,qBACE,8UAAC;oCAAqB,WAAU;;sDAC9B,8UAAC;4CAAI,WAAU;sDACb,cAAA,8UAAC;gDAAc,WAAU;;;;;;;;;;;sDAE3B,8UAAC;4CAAG,WAAU;sDAA4C,KAAK,KAAK;;;;;;sDACpE,8UAAC;4CAAE,WAAU;sDAAqC,KAAK,KAAK;;;;;;sDAC5D,8UAAC;4CAAE,WAAU;sDAAyB,KAAK,WAAW;;;;;;;mCAN9C,KAAK,KAAK;;;;;4BASxB;;;;;;;;;;;;;;;;;0BAMN,8UAAC;gBAAQ,WAAU;0BACjB,cAAA,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8UAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8UAAC;4BAAK,WAAU;;8CACd,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;;8DACC,8UAAC;oDAAM,SAAQ;oDAAY,WAAU;8DAA+C;;;;;;8DAGpF,8UAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAGd,8UAAC;;8DACC,8UAAC;oDAAM,SAAQ;oDAAW,WAAU;8DAA+C;;;;;;8DAGnF,8UAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,QAAQ;oDACR,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;;8DACC,8UAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA+C;;;;;;8DAGhF,8UAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAGd,8UAAC;;8DACC,8UAAC;oDAAM,SAAQ;oDAAQ,WAAU;8DAA+C;;;;;;8DAGhF,8UAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,WAAU;;;;;;;;;;;;;;;;;;8CAKhB,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAA+C;;;;;;sDAGlF,8UAAC;4CACC,MAAK;4CACL,IAAG;4CACH,MAAK;4CACL,WAAU;;;;;;;;;;;;8CAId,8UAAC;oCAAI,WAAU;;sDACb,8UAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAA+C;;;;;;sDAGlF,8UAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAM;4CACN,QAAQ;4CACR,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8UAAC;oCAAI,WAAU;8CACb,cAAA,8UAAC;wCAAO,MAAK;wCAAS,MAAK;wCAAK,WAAU;;0DACxC,8UAAC;gDAAc,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpD,8UAAC;gBAAQ,WAAU;0BACjB,cAAA,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8UAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAKzD,8UAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8UAAC;oCAAwB,WAAU;;sDACjC,8UAAC;4CAAG,WAAU;sDAA4C,SAAS,IAAI;;;;;;sDACvE,8UAAC;4CAAI,WAAU;;8DACb,8UAAC;oDAAI,WAAU;;sEACb,8UAAC,6TAAM;4DAAC,WAAU;;;;;;sEAClB,8UAAC;4DAAE,WAAU;sEAAiB,SAAS,OAAO;;;;;;;;;;;;8DAEhD,8UAAC;oDAAI,WAAU;;sEACb,8UAAC,sTAAK;4DAAC,WAAU;;;;;;sEACjB,8UAAC;4DAAE,WAAU;sEAAiB,SAAS,KAAK;;;;;;;;;;;;;;;;;;sDAGhD,8UAAC;4CAAI,WAAU;sDACb,cAAA,8UAAC;gDAAO,SAAQ;gDAAU,WAAU;;kEAClC,8UAAC;wDAAS,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;mCAdjC,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;0BAyB/B,8UAAC;gBAAQ,WAAU;0BACjB,cAAA,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8UAAC;4BAAE,WAAU;sCAAwD;;;;;;sCAGrE,8UAAC;4BAAI,WAAU;;8CACb,8UAAC;oCAAO,MAAK;oCAAK,WAAU;;sDAC1B,8UAAC;4CAAS,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,8UAAC;oCAAO,MAAK;oCAAK,SAAQ;oCAAU,WAAU;;sDAC5C,8UAAC,sTAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;KAzNwB", "debugId": null}}, {"offset": {"line": 766, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/phone.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/phone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384',\n      key: '9njp5v',\n    },\n  ],\n];\n\n/**\n * @component @name Phone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMuODMyIDE2LjU2OGExIDEgMCAwIDAgMS4yMTMtLjMwM2wuMzU1LS40NjVBMiAyIDAgMCAxIDE3IDE1aDNhMiAyIDAgMCAxIDIgMnYzYTIgMiAwIDAgMS0yIDJBMTggMTggMCAwIDEgMiA0YTIgMiAwIDAgMSAyLTJoM2EyIDIgMCAwIDEgMiAydjNhMiAyIDAgMCAxLS44IDEuNmwtLjQ2OC4zNTFhMSAxIDAgMCAwLS4yOTIgMS4yMzMgMTQgMTQgMCAwIDAgNi4zOTIgNi4zODQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/phone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Phone = createLucideIcon('phone', __iconNode);\n\nexport default Phone;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/mail.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/mail.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7', key: '132q7q' }],\n  ['rect', { x: '2', y: '4', width: '20', height: '16', rx: '2', key: 'izxlao' }],\n];\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgNy04Ljk5MSA1LjcyN2EyIDIgMCAwIDEtMi4wMDkgMEwyIDciIC8+CiAgPHJlY3QgeD0iMiIgeT0iNCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjE2IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('mail', __iconNode);\n\nexport default Mail;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,QAAQ,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/map-pin.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/map-pin.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0',\n      key: '1r0f0z',\n    },\n  ],\n  ['circle', { cx: '12', cy: '10', r: '3', key: 'ilqhr7' }],\n];\n\n/**\n * @component @name MapPin\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTBjMCA0Ljk5My01LjUzOSAxMC4xOTMtNy4zOTkgMTEuNzk5YTEgMSAwIDAgMS0xLjIwMiAwQzkuNTM5IDIwLjE5MyA0IDE0Ljk5MyA0IDEwYTggOCAwIDAgMSAxNiAwIiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTAiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/map-pin\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MapPin = createLucideIcon('map-pin', __iconNode);\n\nexport default MapPin;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 900, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/clock.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}