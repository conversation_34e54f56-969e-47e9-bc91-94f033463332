module.exports = [
"[project]/Desktop/via/app/connect/layout.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>ConnectLayout,
    "metadata",
    ()=>metadata
]);
const metadata = {
    title: "Connect - Via Executive Suites",
    description: "Get in touch with Via Executive Suites. Schedule a tour, ask questions, or learn more about our professional workspace solutions.",
    keywords: "contact, schedule tour, office space, executive suites, Rio Grande Valley",
    authors: [
        {
            name: "Via Executive Suites"
        }
    ],
    openGraph: {
        title: "Connect - Via Executive Suites",
        description: "Get in touch with Via Executive Suites. Schedule a tour, ask questions, or learn more about our professional workspace solutions.",
        type: "website",
        locale: "en_US"
    },
    twitter: {
        card: "summary_large_image",
        title: "Connect - Via Executive Suites",
        description: "Get in touch with Via Executive Suites. Schedule a tour, ask questions, or learn more about our professional workspace solutions."
    }
};
function ConnectLayout({ children }) {
    return children;
}
}),
];

//# sourceMappingURL=Desktop_via_app_connect_layout_tsx_57e99747._.js.map