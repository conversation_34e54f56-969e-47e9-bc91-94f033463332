{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/via/app/locations/%5Bslug%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { use } from \"react\";\r\nimport { notFound } from \"next/navigation\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { MapPin, Phone, Clock, Mail, Car, Building2 } from \"lucide-react\";\r\nimport { Location, LocationPageProps, LocationSlugs } from \"@/lib/types/location\";\r\n\r\n// Location data - in a real app, this would come from a database\r\nconst locationData: Record<LocationSlugs, Location> = {\r\n  \"adbc\": {\r\n    name: \"ADBC\",\r\n    fullName: \"Via Executive Suites ADBC\",\r\n    address: \"813 N. Main St., McAllen, Texas 78501\",\r\n    phone: \"(*************\",\r\n    hours: \"Mon-Fri: 8:00 AM - 5:00 PM\",\r\n    email: \"<EMAIL>\",\r\n    coordinates: { lat: 26.212138647510635, lng: -98.23348265965645 },\r\n    description: \"Located in the heart of McAllen, our ADBC location offers premium office spaces with easy access to major highways and business districts.\",\r\n    features: [\r\n      \"Private office suites\",\r\n      \"Conference rooms\",\r\n      \"Reception services\",\r\n      \"High-speed internet\",\r\n      \"Free parking\",\r\n      \"Break room amenities\",\r\n      \"Mail handling\",\r\n      \"24/7 building access\"\r\n    ],\r\n    image: \"https://via2success.com/wp-content/uploads/2021/01/location-adbc.jpg\"\r\n  },\r\n  \"la-costa\": {\r\n    name: \"La Costa\",\r\n    fullName: \"Via Executive Suites La Costa\",\r\n    address: \"214 N 16th St, McAllen, Texas 78501\",\r\n    phone: \"(*************\",\r\n    hours: \"Mon-Fri: 8:00 AM - 5:00 PM\",\r\n    email: \"<EMAIL>\",\r\n    coordinates: { lat: 26.20654823351708, lng: -98.2359421731485 },\r\n    description: \"Our La Costa location provides modern office solutions in a vibrant business community, perfect for growing companies and entrepreneurs.\",\r\n    features: [\r\n      \"Flexible office spaces\",\r\n      \"Virtual office services\",\r\n      \"Meeting facilities\",\r\n      \"Business support services\",\r\n      \"Secure access\",\r\n      \"Professional environment\",\r\n      \"Networking opportunities\",\r\n      \"Convenient location\"\r\n    ],\r\n    image: \"https://via2success.com/wp-content/uploads/2021/01/location-la-costa.jpg\"\r\n  },\r\n  \"23rd\": {\r\n    name: \"23rd\",\r\n    fullName: \"Via Executive Suites 23rd\",\r\n    address: \"1821 N 23rd Street, McAllen, Texas 78501\",\r\n    phone: \"(*************\",\r\n    hours: \"Mon-Fri: 8:00 AM - 5:00 PM\",\r\n    email: \"<EMAIL>\",\r\n    coordinates: { lat: 26.22271557411775, lng: -98.24298783082033 },\r\n    description: \"The 23rd Street location offers spacious office environments with premium amenities, ideal for established businesses seeking professional workspace.\",\r\n    features: [\r\n      \"Executive office suites\",\r\n      \"Boardroom facilities\",\r\n      \"Premium amenities\",\r\n      \"Dedicated support staff\",\r\n      \"Advanced technology\",\r\n      \"Professional services\",\r\n      \"Business networking\",\r\n      \"Premium location\"\r\n    ],\r\n    image: \"https://via2success.com/wp-content/uploads/2021/01/location-23rd.jpg\"\r\n  },\r\n  \"edinburg\": {\r\n    name: \"Edinburg\",\r\n    fullName: \"Via Executive Suites Edinburg\",\r\n    address: \"1409 S 9th Ave, Edinburg, Texas 78539\",\r\n    phone: \"(*************\",\r\n    hours: \"Mon-Fri: 8:00 AM - 5:00 PM\",\r\n    email: \"<EMAIL>\",\r\n    coordinates: { lat: 26.28915674741617, lng: -98.16747817499945 },\r\n    description: \"Our Edinburg location serves the northern Rio Grande Valley with professional office solutions and comprehensive business support services.\",\r\n    features: [\r\n      \"Modern office spaces\",\r\n      \"Collaborative areas\",\r\n      \"Technology infrastructure\",\r\n      \"Business services\",\r\n      \"Community events\",\r\n      \"Professional development\",\r\n      \"Local partnerships\",\r\n      \"Strategic positioning\"\r\n    ],\r\n    image: \"https://via2success.com/wp-content/uploads/2021/01/location-edinburg.jpg\"\r\n  }\r\n};\r\n\r\nexport default function LocationPage({ params }: LocationPageProps) {\r\n  // Unwrap the params Promise using React.use() for Next.js 15 compatibility\r\n  const resolvedParams = use(params) as { slug: string };\r\n  const location = locationData[resolvedParams.slug as LocationSlugs];\r\n\r\n  if (!location) {\r\n    notFound();\r\n  }\r\n\r\n  return (\r\n    <div className=\"min-h-screen\">\r\n      {/* Hero Section */}\r\n      <section className=\"relative bg-gradient-to-r from-via-primary via-via-primary-light to-via-primary text-white py-24 lg:py-32 overflow-hidden\">\r\n        <div className=\"absolute inset-0 bg-black/20\"></div>\r\n        <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\r\n          <h1 className=\"text-3xl md:text-5xl lg:text-6xl font-bold mb-6\">\r\n            {location.fullName}\r\n          </h1>\r\n          <p className=\"text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto\">\r\n            Professional office solutions in the heart of the Rio Grande Valley\r\n          </p>\r\n        </div>\r\n      </section>\r\n\r\n      {/* Location Details */}\r\n      <section className=\"py-16 bg-white overflow-hidden\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-start\">\r\n            {/* Location Information */}\r\n            <div className=\"space-y-8\">\r\n              <div>\r\n                <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">\r\n                  Location Details\r\n                </h2>\r\n                <p className=\"text-lg text-gray-600 leading-relaxed\">\r\n                  {location.description}\r\n                </p>\r\n              </div>\r\n\r\n              {/* Contact Information */}\r\n              <div className=\"space-y-4\">\r\n                <h3 className=\"text-xl font-semibold text-gray-900\">Contact Information</h3>\r\n                <div className=\"space-y-3\">\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <MapPin className=\"w-5 h-5 text-via-primary flex-shrink-0\" />\r\n                    <span className=\"text-gray-700\">{location.address}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <Phone className=\"w-5 h-5 text-via-primary flex-shrink-0\" />\r\n                    <span className=\"text-gray-700\">{location.phone}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <Clock className=\"w-5 h-5 text-via-primary flex-shrink-0\" />\r\n                    <span className=\"text-gray-700\">{location.hours}</span>\r\n                  </div>\r\n                  <div className=\"flex items-center space-x-3\">\r\n                    <Mail className=\"w-5 h-5 text-via-primary flex-shrink-0\" />\r\n                    <span className=\"text-gray-700\">{location.email}</span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Action Buttons */}\r\n              <div className=\"flex flex-col sm:flex-row gap-4\">\r\n                <Button\r\n                  onClick={() => window.open(`tel:${location.phone}`, '_self')}\r\n                  className=\"bg-via-primary hover:bg-via-primary-dark text-white\"\r\n                >\r\n                  <Phone className=\"w-4 h-4 mr-2\" />\r\n                  Call Now\r\n                </Button>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  onClick={() => {\r\n                    const encodedAddress = encodeURIComponent(location.address);\r\n                    window.open(`https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`, '_blank');\r\n                  }}\r\n                  className=\"border-via-primary text-via-primary hover:bg-via-primary hover:text-white\"\r\n                >\r\n                  <Car className=\"w-4 h-4 mr-2\" />\r\n                  Get Directions\r\n                </Button>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  onClick={() => window.open(`mailto:${location.email}`, '_self')}\r\n                  className=\"border-via-primary text-via-primary hover:bg-via-primary hover:text-white\"\r\n                >\r\n                  <Mail className=\"w-4 h-4 mr-2\" />\r\n                  Send Email\r\n                </Button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Features and Amenities */}\r\n            <div className=\"space-y-6\">\r\n              <h3 className=\"text-2xl font-bold text-gray-900\">Features & Amenities</h3>\r\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\r\n                {location.features.map((feature, index) => (\r\n                  <div key={index} className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\r\n                    <div className=\"w-2 h-2 bg-via-primary rounded-full\"></div>\r\n                    <span className=\"text-gray-700\">{feature}</span>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n\r\n              {/* Location Image Placeholder */}\r\n              <div className=\"bg-gray-200 rounded-lg h-64 flex items-center justify-center\">\r\n                <div className=\"text-center text-gray-500\">\r\n                  <Building2 className=\"w-12 h-12 mx-auto mb-4\" />\r\n                  <p className=\"text-lg font-medium\">Location Image</p>\r\n                  <p className=\"text-sm\">Image would be displayed here</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </section>\r\n\r\n      {/* CTA Section */}\r\n      <section className=\"py-16 bg-gray-50 overflow-hidden\">\r\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\r\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">\r\n            Ready to Get Started?\r\n          </h2>\r\n          <p className=\"text-lg text-gray-600 mb-8 max-w-2xl mx-auto\">\r\n            Contact us today to schedule a tour of {location.fullName} and discover how we can support your business growth.\r\n          </p>\r\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n            <Button\r\n              size=\"lg\"\r\n              className=\"bg-via-primary hover:bg-via-primary-dark text-white px-8 py-3\"\r\n            >\r\n              Schedule a Tour\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"lg\"\r\n              className=\"border-via-primary text-via-primary hover:bg-via-primary hover:text-white px-8 py-3\"\r\n            >\r\n              Contact Us\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAQA,iEAAiE;AACjE,MAAM,eAAgD;IACpD,QAAQ;QACN,MAAM;QACN,UAAU;QACV,SAAS;QACT,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;YAAE,KAAK;YAAoB,KAAK,CAAC;QAAkB;QAChE,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;IACA,YAAY;QACV,MAAM;QACN,UAAU;QACV,SAAS;QACT,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;YAAE,KAAK;YAAmB,KAAK,CAAC;QAAiB;QAC9D,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,SAAS;QACT,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;YAAE,KAAK;YAAmB,KAAK,CAAC;QAAkB;QAC/D,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;IACA,YAAY;QACV,MAAM;QACN,UAAU;QACV,SAAS;QACT,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;YAAE,KAAK;YAAmB,KAAK,CAAC;QAAkB;QAC/D,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;IACT;AACF;AAEe,SAAS,aAAa,KAA6B;QAA7B,EAAE,MAAM,EAAqB,GAA7B;IACnC,2EAA2E;IAC3E,MAAM,iBAAiB,IAAA,qTAAG,EAAC;IAC3B,MAAM,WAAW,YAAY,CAAC,eAAe,IAAI,CAAkB;IAEnE,IAAI,CAAC,UAAU;QACb,IAAA,kSAAQ;IACV;IAEA,qBACE,8UAAC;QAAI,WAAU;;0BAEb,8UAAC;gBAAQ,WAAU;;kCACjB,8UAAC;wBAAI,WAAU;;;;;;kCACf,8UAAC;wBAAI,WAAU;;0CACb,8UAAC;gCAAG,WAAU;0CACX,SAAS,QAAQ;;;;;;0CAEpB,8UAAC;gCAAE,WAAU;0CAAsD;;;;;;;;;;;;;;;;;;0BAOvE,8UAAC;gBAAQ,WAAU;0BACjB,cAAA,8UAAC;oBAAI,WAAU;8BACb,cAAA,8UAAC;wBAAI,WAAU;;0CAEb,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;;0DACC,8UAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAGtD,8UAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;;;;;;;kDAKzB,8UAAC;wCAAI,WAAU;;0DACb,8UAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8UAAC;gDAAI,WAAU;;kEACb,8UAAC;wDAAI,WAAU;;0EACb,8UAAC,6TAAM;gEAAC,WAAU;;;;;;0EAClB,8UAAC;gEAAK,WAAU;0EAAiB,SAAS,OAAO;;;;;;;;;;;;kEAEnD,8UAAC;wDAAI,WAAU;;0EACb,8UAAC,sTAAK;gEAAC,WAAU;;;;;;0EACjB,8UAAC;gEAAK,WAAU;0EAAiB,SAAS,KAAK;;;;;;;;;;;;kEAEjD,8UAAC;wDAAI,WAAU;;0EACb,8UAAC,sTAAK;gEAAC,WAAU;;;;;;0EACjB,8UAAC;gEAAK,WAAU;0EAAiB,SAAS,KAAK;;;;;;;;;;;;kEAEjD,8UAAC;wDAAI,WAAU;;0EACb,8UAAC,mTAAI;gEAAC,WAAU;;;;;;0EAChB,8UAAC;gEAAK,WAAU;0EAAiB,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAMrD,8UAAC;wCAAI,WAAU;;0DACb,8UAAC,0JAAM;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,OAAqB,OAAf,SAAS,KAAK,GAAI;gDACpD,WAAU;;kEAEV,8UAAC,sTAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,8UAAC,0JAAM;gDACL,SAAQ;gDACR,SAAS;oDACP,MAAM,iBAAiB,mBAAmB,SAAS,OAAO;oDAC1D,OAAO,IAAI,CAAC,AAAC,sDAAoE,OAAf,iBAAkB;gDACtF;gDACA,WAAU;;kEAEV,8UAAC,gTAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGlC,8UAAC,0JAAM;gDACL,SAAQ;gDACR,SAAS,IAAM,OAAO,IAAI,CAAC,AAAC,UAAwB,OAAf,SAAS,KAAK,GAAI;gDACvD,WAAU;;kEAEV,8UAAC,mTAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAOvC,8UAAC;gCAAI,WAAU;;kDACb,8UAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8UAAC;wCAAI,WAAU;kDACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC/B,8UAAC;gDAAgB,WAAU;;kEACzB,8UAAC;wDAAI,WAAU;;;;;;kEACf,8UAAC;wDAAK,WAAU;kEAAiB;;;;;;;+CAFzB;;;;;;;;;;kDAQd,8UAAC;wCAAI,WAAU;kDACb,cAAA,8UAAC;4CAAI,WAAU;;8DACb,8UAAC,sUAAS;oDAAC,WAAU;;;;;;8DACrB,8UAAC;oDAAE,WAAU;8DAAsB;;;;;;8DACnC,8UAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASnC,8UAAC;gBAAQ,WAAU;0BACjB,cAAA,8UAAC;oBAAI,WAAU;;sCACb,8UAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8UAAC;4BAAE,WAAU;;gCAA+C;gCAClB,SAAS,QAAQ;gCAAC;;;;;;;sCAE5D,8UAAC;4BAAI,WAAU;;8CACb,8UAAC,0JAAM;oCACL,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8UAAC,0JAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KAlJwB", "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/map-pin.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/map-pin.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0',\n      key: '1r0f0z',\n    },\n  ],\n  ['circle', { cx: '12', cy: '10', r: '3', key: 'ilqhr7' }],\n];\n\n/**\n * @component @name MapPin\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTBjMCA0Ljk5My01LjUzOSAxMC4xOTMtNy4zOTkgMTEuNzk5YTEgMSAwIDAgMS0xLjIwMiAwQzkuNTM5IDIwLjE5MyA0IDE0Ljk5MyA0IDEwYTggOCAwIDAgMSAxNiAwIiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTAiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/map-pin\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MapPin = createLucideIcon('map-pin', __iconNode);\n\nexport default MapPin;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 636, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/phone.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/phone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384',\n      key: '9njp5v',\n    },\n  ],\n];\n\n/**\n * @component @name Phone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMuODMyIDE2LjU2OGExIDEgMCAwIDAgMS4yMTMtLjMwM2wuMzU1LS40NjVBMiAyIDAgMCAxIDE3IDE1aDNhMiAyIDAgMCAxIDIgMnYzYTIgMiAwIDAgMS0yIDJBMTggMTggMCAwIDEgMiA0YTIgMiAwIDAgMSAyLTJoM2EyIDIgMCAwIDEgMiAydjNhMiAyIDAgMCAxLS44IDEuNmwtLjQ2OC4zNTFhMSAxIDAgMCAwLS4yOTIgMS4yMzMgMTQgMTQgMCAwIDAgNi4zOTIgNi4zODQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/phone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Phone = createLucideIcon('phone', __iconNode);\n\nexport default Phone;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/clock.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['polyline', { points: '12 6 12 12 16 14', key: '68esgv' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIxMiA2IDEyIDEyIDE2IDE0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;QAAA,CAAA;YAAE,QAAQ,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/mail.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/mail.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7', key: '132q7q' }],\n  ['rect', { x: '2', y: '4', width: '20', height: '16', rx: '2', key: 'izxlao' }],\n];\n\n/**\n * @component @name Mail\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjIgNy04Ljk5MSA1LjcyN2EyIDIgMCAwIDEtMi4wMDkgMEwyIDciIC8+CiAgPHJlY3QgeD0iMiIgeT0iNCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjE2IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mail\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mail = createLucideIcon('mail', __iconNode);\n\nexport default Mail;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAG;YAAK,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,QAAQ,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAChF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/car.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/car.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M19 17h2c.6 0 1-.4 1-1v-3c0-.9-.7-1.7-1.5-1.9C18.7 10.6 16 10 16 10s-1.3-1.4-2.2-2.3c-.5-.4-1.1-.7-1.8-.7H5c-.6 0-1.1.4-1.4.9l-1.4 2.9A3.7 3.7 0 0 0 2 12v4c0 .6.4 1 1 1h2',\n      key: '5owen',\n    },\n  ],\n  ['circle', { cx: '7', cy: '17', r: '2', key: 'u2ysq9' }],\n  ['path', { d: 'M9 17h6', key: 'r8uit2' }],\n  ['circle', { cx: '17', cy: '17', r: '2', key: 'axvx0g' }],\n];\n\n/**\n * @component @name Car\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMTdoMmMuNiAwIDEtLjQgMS0xdi0zYzAtLjktLjctMS43LTEuNS0xLjlDMTguNyAxMC42IDE2IDEwIDE2IDEwcy0xLjMtMS40LTIuMi0yLjNjLS41LS40LTEuMS0uNy0xLjgtLjdINWMtLjYgMC0xLjEuNC0xLjQuOWwtMS40IDIuOUEzLjcgMy43IDAgMCAwIDIgMTJ2NGMwIC42LjQgMSAxIDFoMiIgLz4KICA8Y2lyY2xlIGN4PSI3IiBjeT0iMTciIHI9IjIiIC8+CiAgPHBhdGggZD0iTTkgMTdoNiIgLz4KICA8Y2lyY2xlIGN4PSIxNyIgY3k9IjE3IiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/car\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Car = createLucideIcon('car', __iconNode);\n\nexport default Car;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/Desktop/via/node_modules/.pnpm/lucide-react@0.511.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/building-2.js", "sources": ["file:///C:/Users/<USER>/Desktop/via/node_modules/.pnpm/lucide-react%400.511.0_react%4019.1.1/node_modules/lucide-react/src/icons/building-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z', key: '1b4qmf' }],\n  ['path', { d: 'M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2', key: 'i71pzd' }],\n  ['path', { d: 'M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2', key: '10jefs' }],\n  ['path', { d: 'M10 6h4', key: '1itunk' }],\n  ['path', { d: 'M10 10h4', key: 'tcdvrf' }],\n  ['path', { d: 'M10 14h4', key: 'kelpxr' }],\n  ['path', { d: 'M10 18h4', key: '1ulq68' }],\n];\n\n/**\n * @component @name Building2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNiAyMlY0YTIgMiAwIDAgMSAyLTJoOGEyIDIgMCAwIDEgMiAydjE4WiIgLz4KICA8cGF0aCBkPSJNNiAxMkg0YTIgMiAwIDAgMC0yIDJ2NmEyIDIgMCAwIDAgMiAyaDIiIC8+CiAgPHBhdGggZD0iTTE4IDloMmEyIDIgMCAwIDEgMiAydjlhMiAyIDAgMCAxLTIgMmgtMiIgLz4KICA8cGF0aCBkPSJNMTAgNmg0IiAvPgogIDxwYXRoIGQ9Ik0xMCAxMGg0IiAvPgogIDxwYXRoIGQ9Ik0xMCAxNGg0IiAvPgogIDxwYXRoIGQ9Ik0xMCAxOGg0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/building-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Building2 = createLucideIcon('building-2', __iconNode);\n\nexport default Building2;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA4C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uQAAiB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}