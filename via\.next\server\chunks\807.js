exports.id=807,exports.ids=[807],exports.modules={15353:(a,b,c)=>{"use strict";c.d(b,{$:()=>j});var d=c(92536),e=c(84041),f=c(93071),g=c(10265),h=c(21026);let i=(0,g.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},21026:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(11131),e=c(18327);function f(...a){return(0,e.QP)((0,d.$)(a))}},21557:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(72339);let e=async a=>[{type:"image/png",width:1200,height:600,url:(0,d.fillMetadataSegment)(".",await a.params,"twitter-image.png")+"?2e0bc232e210f89d"}]},25626:()=>{},26518:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,91644,23)),Promise.resolve().then(c.t.bind(c,89439,23)),Promise.resolve().then(c.t.bind(c,82243,23)),Promise.resolve().then(c.t.bind(c,88454,23)),Promise.resolve().then(c.t.bind(c,65890,23)),Promise.resolve().then(c.t.bind(c,422,23)),Promise.resolve().then(c.t.bind(c,19078,23)),Promise.resolve().then(c.t.bind(c,47379,23)),Promise.resolve().then(c.bind(c,22534))},38729:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(72339);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},44455:(a,b,c)=>{Promise.resolve().then(c.bind(c,64592)),Promise.resolve().then(c.bind(c,70967)),Promise.resolve().then(c.bind(c,57480)),Promise.resolve().then(c.t.bind(c,4163,23)),Promise.resolve().then(c.t.bind(c,55198,23))},61327:(a,b,c)=>{Promise.resolve().then(c.bind(c,79822)),Promise.resolve().then(c.bind(c,93593)),Promise.resolve().then(c.bind(c,41062)),Promise.resolve().then(c.t.bind(c,81169,23)),Promise.resolve().then(c.t.bind(c,62856,23))},63470:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,41970,23)),Promise.resolve().then(c.t.bind(c,8685,23)),Promise.resolve().then(c.t.bind(c,2205,23)),Promise.resolve().then(c.t.bind(c,404,23)),Promise.resolve().then(c.t.bind(c,80876,23)),Promise.resolve().then(c.t.bind(c,52784,23)),Promise.resolve().then(c.t.bind(c,5608,23)),Promise.resolve().then(c.t.bind(c,42257,23)),Promise.resolve().then(c.t.bind(c,26200,23))},64592:(a,b,c)=>{"use strict";c.d(b,{Navbar:()=>o});var d=c(92536),e=c(84041),f=c(4163),g=c.n(f),h=c(20159),i=c(67830),j=c(62430),k=c(2063),l=c(15353),m=c(21026);let n=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Locations",href:"/locations",dropdown:[{name:"ADBC",href:"/locations/adbc"},{name:"La Costa",href:"/locations/la-costa"},{name:"23rd",href:"/locations/23rd"},{name:"Edinburg",href:"/locations/edinburg"},{name:"Lindberg",href:"/locations/lindberg"}]},{name:"Services",href:"/services",dropdown:[{name:"Executive Suites",href:"/services/executive-suites"},{name:"Virtual Offices",href:"/services/virtual-offices"},{name:"Beauty Suites",href:"/services/beauty-suites"}]},{name:"Blog",href:"/blog"},{name:"Connect",href:"/connect"}];function o(){let[a,b]=(0,e.useState)(!1),[c,f]=(0,e.useState)(null);return(0,d.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50",children:(0,d.jsxs)("nav",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8","aria-label":"Top",children:[(0,d.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsxs)(g(),{href:"/",className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"relative h-10 w-auto",children:(0,d.jsx)(h.default,{src:"https://via2success.com/wp-content/themes/viatosuccess_v1/assets/images/logo.svg",alt:"Via Executive Suites",width:120,height:40,className:"h-10 w-auto",priority:!0})}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("span",{className:"text-xl font-bold text-via-primary leading-none",children:"via"}),(0,d.jsx)("span",{className:"text-xs font-medium text-gray-600 leading-none",children:"executive suites"})]})]})}),(0,d.jsx)("div",{className:"hidden md:flex md:items-center md:space-x-8",children:n.map(a=>(0,d.jsx)("div",{className:"relative",children:a.dropdown?(0,d.jsxs)("div",{className:"relative",onMouseEnter:()=>f(a.name),onMouseLeave:()=>f(null),children:[(0,d.jsxs)("button",{className:"flex items-center text-gray-700 hover:text-via-primary font-medium transition-colors duration-200",children:[a.name,(0,d.jsx)(i.A,{className:"ml-1 h-4 w-4"})]}),c===a.name&&(0,d.jsx)("div",{className:"absolute top-full left-0 mt-1 w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50",children:a.dropdown.map(a=>(0,d.jsx)(g(),{href:a.href,className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-via-primary transition-colors duration-200",children:a.name},a.name))})]}):(0,d.jsx)(g(),{href:a.href,className:"text-gray-700 hover:text-via-primary font-medium transition-colors duration-200",children:a.name})},a.name))}),(0,d.jsxs)("div",{className:"hidden md:flex md:items-center md:space-x-4",children:[(0,d.jsx)(g(),{href:"/connect",children:(0,d.jsx)(l.$,{variant:"outline",className:"text-via-primary border-via-primary hover:bg-via-primary hover:text-white",children:"Schedule a Tour"})}),(0,d.jsx)(g(),{href:"/auth/login",children:(0,d.jsx)(l.$,{variant:"ghost",className:"text-gray-700 hover:text-via-primary",children:"Sign In"})}),(0,d.jsx)(g(),{href:"/auth/sign-up",children:(0,d.jsx)(l.$,{className:"bg-via-primary hover:bg-via-primary-dark text-white",children:"Get Started"})})]}),(0,d.jsx)("div",{className:"md:hidden",children:(0,d.jsxs)(l.$,{variant:"ghost",size:"sm",onClick:()=>b(!a),className:"text-gray-700",children:[(0,d.jsx)("span",{className:"sr-only",children:"Open main menu"}),a?(0,d.jsx)(j.A,{className:"h-6 w-6","aria-hidden":"true"}):(0,d.jsx)(k.A,{className:"h-6 w-6","aria-hidden":"true"})]})})]}),(0,d.jsx)("div",{className:(0,m.cn)("md:hidden transition-all duration-300 ease-in-out",a?"max-h-[500px] opacity-100 visible":"max-h-0 opacity-0 invisible overflow-hidden"),children:(0,d.jsxs)("div",{className:"space-y-1 pb-3 pt-2",children:[n.map(a=>(0,d.jsx)("div",{children:a.dropdown?(0,d.jsxs)("div",{children:[(0,d.jsxs)("button",{className:"flex items-center justify-between w-full px-3 py-2 text-base font-medium text-gray-700 hover:text-via-primary hover:bg-gray-50 rounded-md transition-colors duration-200",onClick:()=>f(c===a.name?null:a.name),children:[a.name,(0,d.jsx)(i.A,{className:(0,m.cn)("h-4 w-4 transition-transform",c===a.name&&"rotate-180")})]}),c===a.name&&(0,d.jsx)("div",{className:"pl-4 space-y-1",children:a.dropdown.map(a=>(0,d.jsx)(g(),{href:a.href,className:"block px-3 py-2 text-sm text-gray-600 hover:text-via-primary hover:bg-gray-50 rounded-md transition-colors duration-200",onClick:()=>b(!1),children:a.name},a.name))})]}):(0,d.jsx)(g(),{href:a.href,className:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-via-primary hover:bg-gray-50 rounded-md transition-colors duration-200",onClick:()=>b(!1),children:a.name})},a.name)),(0,d.jsx)("div",{className:"border-t border-gray-200 pt-4 pb-3",children:(0,d.jsxs)("div",{className:"flex flex-col space-y-3 px-3",children:[(0,d.jsx)(g(),{href:"/connect",onClick:()=>b(!1),children:(0,d.jsx)(l.$,{variant:"outline",className:"w-full text-via-primary border-via-primary hover:bg-via-primary hover:text-white",children:"Schedule a Tour"})}),(0,d.jsx)(g(),{href:"/auth/login",onClick:()=>b(!1),children:(0,d.jsx)(l.$,{variant:"ghost",className:"w-full justify-start text-gray-700 hover:text-via-primary",children:"Sign In"})}),(0,d.jsx)(g(),{href:"/auth/sign-up",onClick:()=>b(!1),children:(0,d.jsx)(l.$,{className:"w-full bg-via-primary hover:bg-via-primary-dark text-white",children:"Get Started"})})]})})]})})]})})}},70967:(a,b,c)=>{"use strict";c.d(b,{PageTransition:()=>i});var d=c(92536),e=c(60193),f=c(49216),g=c(63414),h=c(94552);function i({children:a}){let b=(0,g.usePathname)();return(0,d.jsx)(e.N,{mode:"wait",initial:!1,children:(0,d.jsx)(f.P.div,{initial:"hidden",animate:"visible",exit:"exit",variants:h.Bs,className:"min-h-screen overflow-hidden",children:a},b)})}},77707:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(72339);let e=async a=>[{type:"image/png",width:1200,height:600,url:(0,d.fillMetadataSegment)(".",await a.params,"opengraph-image.png")+"?2e0bc232e210f89d"}]},78385:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>w,metadata:()=>v});var d=c(31850),e=c(32457),f=c.n(e),g=c(41062),h=c(79822),i=c(81169),j=c.n(i),k=c(89849),l=c(57510),m=c(92592),n=c(69492),o=c(58489),p=c(9535);let q=[{name:"Via Edinburg",href:"/locations/edinburg"},{name:"La Costa",href:"/locations/la-costa"},{name:"Via 23rd",href:"/locations/23rd"},{name:"ADBC",href:"/locations/adbc"}],r=[{name:"Virtual Offices",href:"/services/virtual-offices"},{name:"Beauty Suites",href:"/services/beauty-suites"},{name:"Executive Suites",href:"/services/executive-suites"}],s=[{name:"Facebook",href:"https://www.facebook.com/viatosuccess/",icon:l.A},{name:"Instagram",href:"https://www.instagram.com/viatosuccess/",icon:m.A},{name:"LinkedIn",href:"https://www.linkedin.com/company/via-executive-suites/",icon:n.A},{name:"YouTube",href:"https://www.youtube.com/channel/UCzAaMIx4GQqXgLv7k_7meuA",icon:o.A},{name:"Twitter",href:"https://twitter.com/viatosuccess",icon:p.A}];function t(){return(0,d.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,d.jsxs)("div",{className:"lg:col-span-1",children:[(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsx)(k.default,{src:"https://via2success.com/wp-content/themes/viatosuccess_v1/assets/images/logo-footer.svg",alt:"Via Executive Suites",width:150,height:40,className:"h-10 w-auto filter brightness-0 invert"})}),(0,d.jsx)("p",{className:"text-gray-300 text-sm leading-relaxed mb-6",children:"Via Executive Suites has four all inclusive business centers strategically located in the Rio Grande Valley. So we take pride in helping entrepreneurs and startups. And we also make it our mission to enhance the enterprise, identity, and integrity of our clients in order to further our region's economy."}),(0,d.jsxs)("div",{className:"text-sm text-gray-300",children:[(0,d.jsx)("div",{className:"font-semibold text-white mb-1",children:"Main Phone:"}),(0,d.jsx)("div",{children:"(*************"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Locations"}),(0,d.jsx)("ul",{className:"space-y-2",children:q.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(j(),{href:a.href,className:"text-gray-300 hover:text-white transition-colors duration-200 text-sm",children:a.name})},a.name))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Services"}),(0,d.jsx)("ul",{className:"space-y-2",children:r.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(j(),{href:a.href,className:"text-gray-300 hover:text-white transition-colors duration-200 text-sm",children:a.name})},a.name))})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Follow Us"}),(0,d.jsx)("div",{className:"flex space-x-4",children:s.map(a=>{let b=a.icon;return(0,d.jsx)("a",{href:a.href,target:"_blank",rel:"noopener noreferrer",className:"text-gray-300 hover:text-white transition-colors duration-200","aria-label":a.name,children:(0,d.jsx)(b,{className:"w-5 h-5"})},a.name)})}),(0,d.jsxs)("div",{className:"mt-6",children:[(0,d.jsx)("h4",{className:"text-sm font-semibold mb-2",children:"Quick Links"}),(0,d.jsxs)("ul",{className:"space-y-1",children:[(0,d.jsx)("li",{children:(0,d.jsx)(j(),{href:"/about",className:"text-gray-300 hover:text-white transition-colors duration-200 text-sm",children:"About"})}),(0,d.jsx)("li",{children:(0,d.jsx)(j(),{href:"/blog",className:"text-gray-300 hover:text-white transition-colors duration-200 text-sm",children:"Blog"})}),(0,d.jsx)("li",{children:(0,d.jsx)(j(),{href:"/connect",className:"text-gray-300 hover:text-white transition-colors duration-200 text-sm",children:"Connect"})})]})]})]})]}),(0,d.jsxs)("div",{className:"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,d.jsx)("div",{className:"text-sm text-gray-400",children:"\xa9 2025 Via Executive Suites. All rights reserved."}),(0,d.jsx)("div",{className:"mt-4 md:mt-0",children:(0,d.jsx)(j(),{href:"/privacy-policy",className:"text-sm text-gray-400 hover:text-white transition-colors duration-200",children:"Privacy Policy"})})]})]})})}var u=c(93593);c(25626);let v={metadataBase:new URL(process.env.VERCEL_URL?`https://${process.env.VERCEL_URL}`:"http://localhost:3000"),title:"Via Executive Suites - Premium Workspace Solutions",description:"Transform your business with Via Executive Suites. Premium office spaces, meeting rooms, coworking environments, and virtual office solutions in the Rio Grande Valley and beyond.",keywords:["office space","executive suites","coworking","virtual offices","Rio Grande Valley","business solutions"],authors:[{name:"Via Executive Suites"}],openGraph:{title:"Via Executive Suites - Premium Workspace Solutions",description:"Transform your business with Via Executive Suites. Premium office spaces, meeting rooms, coworking environments, and virtual office solutions.",type:"website",locale:"en_US"}};function w({children:a}){return(0,d.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,d.jsx)("body",{className:`${f().className} antialiased min-h-screen flex flex-col`,suppressHydrationWarning:!0,children:(0,d.jsxs)(g.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:[(0,d.jsx)(h.Navbar,{}),(0,d.jsx)("main",{className:"flex-1 overflow-x-hidden",children:(0,d.jsx)(u.PageTransition,{children:a})}),(0,d.jsx)(t,{})]})})})}},79822:(a,b,c)=>{"use strict";c.d(b,{Navbar:()=>d});let d=(0,c(90850).registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\via\\components\\navbar.tsx","Navbar")},93593:(a,b,c)=>{"use strict";c.d(b,{PageTransition:()=>d});let d=(0,c(90850).registerClientReference)(function(){throw Error("Attempted to call PageTransition() from the server but PageTransition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\via\\components\\page-transition.tsx","PageTransition")},94552:(a,b,c)=>{"use strict";c.d(b,{Aw:()=>p,Bs:()=>k,Ce:()=>e,Pz:()=>o,Rf:()=>j,Yo:()=>h,ZY:()=>n,bK:()=>i,gy:()=>g,tE:()=>d,wI:()=>m,xb:()=>l,xs:()=>f});let d={hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6,ease:[.6,-.05,.01,.99]}}},e={hidden:{opacity:0,y:-30},visible:{opacity:1,y:0,transition:{duration:.6,ease:[.6,-.05,.01,.99]}}},f={hidden:{opacity:0,x:-30},visible:{opacity:1,x:0,transition:{duration:.6,ease:[.6,-.05,.01,.99]}}},g={hidden:{opacity:0,x:30},visible:{opacity:1,x:0,transition:{duration:.6,ease:[.6,-.05,.01,.99]}}},h={hidden:{opacity:0,scale:.9},visible:{opacity:1,scale:1,transition:{duration:.5,ease:[.6,-.05,.01,.99]}}},i={hidden:{},visible:{transition:{staggerChildren:.1,delayChildren:.1}}},j={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5,ease:[.6,-.05,.01,.99]}}},k={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.4,ease:[.6,-.05,.01,.99]}},exit:{opacity:0,y:-20,transition:{duration:.3,ease:[.6,-.05,.01,.99]}}},l={viewport:{once:!0,margin:"-100px",amount:.3},reducedMotion:{duration:.01,ease:"linear"}},m={hidden:{},visible:{transition:{staggerChildren:.2,delayChildren:.1}}},n={hidden:{opacity:0,y:50},visible:{opacity:1,y:0,transition:{duration:.8,ease:[.6,-.05,.01,.99]}}},o={hidden:{opacity:0,y:25},visible:{opacity:1,y:0,transition:{duration:.6,ease:[.6,-.05,.01,.99],delay:.2}}},p={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5,ease:[.6,-.05,.01,.99],delay:.4}}}}};